import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react';

// Button variants using CVA for consistent styling
const buttonVariants = cva(
  // Base styles - mobile-first with 44px minimum touch target
  'inline-flex items-center justify-center whitespace-nowrap rounded-xl text-base font-semibold transition-all duration-250 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-95',
  {
    variants: {
      variant: {
        primary: 'bg-blue-600 text-white hover:bg-blue-700 focus-visible:ring-blue-500 shadow-md hover:shadow-lg',
        secondary: 'bg-neutral-100 text-neutral-900 hover:bg-neutral-200 focus-visible:ring-neutral-500 border border-neutral-200',
        success: 'bg-green-600 text-white hover:bg-green-700 focus-visible:ring-green-500 shadow-md hover:shadow-lg',
        warning: 'bg-yellow-500 text-white hover:bg-yellow-600 focus-visible:ring-yellow-500 shadow-md hover:shadow-lg',
        error: 'bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-500 shadow-md hover:shadow-lg',
        outline: 'border-2 border-neutral-300 bg-transparent text-neutral-700 hover:bg-neutral-50 hover:border-neutral-400 focus-visible:ring-neutral-500',
        ghost: 'text-neutral-700 hover:bg-neutral-100 focus-visible:ring-neutral-500',
        link: 'text-blue-600 underline-offset-4 hover:underline focus-visible:ring-blue-500 p-0 h-auto',
      },
      size: {
        sm: 'h-10 px-4 text-sm min-w-[80px]',
        md: 'h-12 px-6 text-base min-w-[100px]', // Default mobile-friendly size
        lg: 'h-14 px-8 text-lg min-w-[120px]',
        xl: 'h-16 px-10 text-xl min-w-[140px]',
        icon: 'h-12 w-12 p-0', // Square icon button
      },
      fullWidth: {
        true: 'w-full',
        false: '',
      }
    },
    defaultVariants: {
      variant: 'primary',
      size: 'md',
      fullWidth: false,
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  loading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  children: React.ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ 
    className, 
    variant, 
    size, 
    fullWidth,
    loading = false,
    leftIcon,
    rightIcon,
    disabled,
    children, 
    ...props 
  }, ref) => {
    const isDisabled = disabled || loading;

    return (
      <button
        className={cn(buttonVariants({ variant, size, fullWidth, className }))}
        ref={ref}
        disabled={isDisabled}
        {...props}
      >
        {loading && (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        )}
        {!loading && leftIcon && (
          <span className="mr-2 flex items-center">
            {leftIcon}
          </span>
        )}
        <span className="flex-1 text-center">
          {children}
        </span>
        {!loading && rightIcon && (
          <span className="ml-2 flex items-center">
            {rightIcon}
          </span>
        )}
      </button>
    );
  }
);

Button.displayName = 'Button';

// Quick action buttons for common visit tracking scenarios
export const QuickActionButton: React.FC<{
  icon: React.ReactNode;
  label: string;
  onClick: () => void;
  variant?: 'sale' | 'appointment' | 'no-interest' | 'not-home';
  loading?: boolean;
}> = ({ icon, label, onClick, variant = 'sale', loading = false }) => {
  const variantStyles = {
    sale: 'bg-green-600 hover:bg-green-700 text-white',
    appointment: 'bg-blue-600 hover:bg-blue-700 text-white',
    'no-interest': 'bg-neutral-500 hover:bg-neutral-600 text-white',
    'not-home': 'bg-red-600 hover:bg-red-700 text-white',
  };

  return (
    <button
      onClick={onClick}
      disabled={loading}
      className={cn(
        'flex flex-col items-center justify-center p-4 rounded-2xl transition-all duration-250 active:scale-95 min-h-[100px] min-w-[120px] shadow-lg hover:shadow-xl',
        variantStyles[variant],
        loading && 'opacity-50 pointer-events-none'
      )}
    >
      {loading ? (
        <Loader2 className="h-8 w-8 animate-spin mb-2" />
      ) : (
        <div className="mb-2 text-2xl">
          {icon}
        </div>
      )}
      <span className="text-sm font-medium text-center leading-tight">
        {label}
      </span>
    </button>
  );
};

// Status button specifically for visit tracking
export const StatusButton: React.FC<{
  status: 'N/A' | 'Angetroffen → Termin' | 'Angetroffen → Kein Interesse' | 'Angetroffen → Sale';
  onClick: () => void;
  loading?: boolean;
  disabled?: boolean;
}> = ({ status, onClick, loading = false, disabled = false }) => {
  const statusConfig = {
    'N/A': {
      variant: 'outline' as const,
      icon: '❓',
      label: 'Nicht angetroffen',
      color: 'border-neutral-300 text-neutral-600'
    },
    'Angetroffen → Termin': {
      variant: 'primary' as const,
      icon: '📅',
      label: 'Termin vereinbaren',
      color: 'bg-blue-600 hover:bg-blue-700'
    },
    'Angetroffen → Kein Interesse': {
      variant: 'secondary' as const,
      icon: '❌',
      label: 'Kein Interesse',
      color: 'bg-neutral-500 hover:bg-neutral-600'
    },
    'Angetroffen → Sale': {
      variant: 'success' as const,
      icon: '💰',
      label: 'Verkauf!',
      color: 'bg-green-600 hover:bg-green-700'
    },
  };

  const config = statusConfig[status];

  return (
    <Button
      variant={config.variant}
      size="lg"
      fullWidth
      onClick={onClick}
      loading={loading}
      disabled={disabled}
      leftIcon={<span className="text-xl">{config.icon}</span>}
      className={cn(
        'h-16 text-lg font-semibold',
        status !== 'N/A' && 'text-white',
        config.color
      )}
    >
      {config.label}
    </Button>
  );
};

export { Button, buttonVariants };
export type { ButtonProps };
