import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  MapPin, 
  Mic, 
  Camera, 
  Navigation, 
  Clock,
  Zap,
  Wifi,
  WifiOff,
  Volume2,
  Square,
  Play,
  Trash2,
  Target,
  CheckCircle
} from 'lucide-react';
import { useGPSTracking } from '@/hooks/useGPSTracking';
import { useVoiceRecording } from '@/hooks/useVoiceRecording';
import { useSwipeGestures, triggerHapticFeedback, validateTouchTarget } from '@/hooks/useSwipeGestures';
import { useData } from '@/context/data';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';

interface EnhancedVisitTrackerProps {
  className?: string;
  onVisitComplete?: (visitId: string) => void;
}

export const EnhancedVisitTracker: React.FC<EnhancedVisitTrackerProps> = ({ 
  className, 
  onVisitComplete 
}) => {
  const { updateVisitStatus, updateDoorStatus } = useData();
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [currentVisitId, setCurrentVisitId] = useState<string | null>(null);
  const [currentDoorId, setCurrentDoorId] = useState<string | null>(null);
  const [visitNotes, setVisitNotes] = useState<string>('');
  const [capturedPhotos, setCapturedPhotos] = useState<string[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);

  // GPS Tracking
  const {
    currentPosition,
    isTracking: isGPSTracking,
    nearbyAddresses,
    error: gpsError,
    startTracking: startGPSTracking,
    stopTracking: stopGPSTracking
  } = useGPSTracking({
    enableAutoLogging: true,
    proximityThreshold: 30,
    onAddressDetected: (address) => {
      triggerHapticFeedback('medium');
      toast.info(`Adresse erkannt: ${address.address}`);
    },
    onVisitLogged: (visitId) => {
      setCurrentVisitId(visitId);
      triggerHapticFeedback('success');
    }
  });

  // Voice Recording
  const {
    recordingState,
    recordings,
    startRecording,
    stopRecording,
    pauseRecording,
    deleteRecording,
    playRecording
  } = useVoiceRecording({
    maxDuration: 180, // 3 minutes
    enableAutoTranscription: true,
    onRecordingComplete: (audioBlob, duration) => {
      // Convert audio to base64 and add to visit notes
      const reader = new FileReader();
      reader.onload = () => {
        const audioData = reader.result as string;
        setVisitNotes(prev => 
          prev + `\n[Sprachnotiz ${duration.toFixed(1)}s: ${audioData.substring(0, 50)}...]`
        );
      };
      reader.readAsDataURL(audioBlob);
    },
    onTranscriptionComplete: (text) => {
      setVisitNotes(prev => prev + `\nTranskription: ${text}`);
    }
  });

  // Swipe gestures for quick actions
  useSwipeGestures({
    onSwipeUp: () => {
      if (currentVisitId && currentDoorId) {
        handleQuickStatus('Angetroffen → Sale');
        triggerHapticFeedback('success');
      }
    },
    onSwipeDown: () => {
      if (currentVisitId && currentDoorId) {
        handleQuickStatus('N/A');
        triggerHapticFeedback('medium');
      }
    },
    onSwipeLeft: () => {
      if (currentVisitId && currentDoorId) {
        handleQuickStatus('Angetroffen → Termin');
        triggerHapticFeedback('medium');
      }
    },
    onSwipeRight: () => {
      if (currentVisitId && currentDoorId) {
        handleQuickStatus('Angetroffen → kein Interesse');
        triggerHapticFeedback('medium');
      }
    },
    threshold: 50
  });

  // Online/Offline detection
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      toast.success('Verbindung wiederhergestellt');
      triggerHapticFeedback('success');
    };
    
    const handleOffline = () => {
      setIsOnline(false);
      toast.warning('Offline-Modus aktiviert');
      triggerHapticFeedback('warning');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Validate touch targets on mount
  useEffect(() => {
    if (containerRef.current) {
      const buttons = containerRef.current.querySelectorAll('button');
      buttons.forEach(button => {
        if (!validateTouchTarget(button as HTMLElement)) {
          console.warn('Touch target too small:', button);
        }
      });
    }
  }, []);

  // Quick status update with haptic feedback
  const handleQuickStatus = (status: string) => {
    if (!currentVisitId || !currentDoorId) return;

    try {
      updateDoorStatus(currentDoorId, status as any);
      updateVisitStatus(currentVisitId, status as any);
      
      triggerHapticFeedback('success');
      toast.success(`Status aktualisiert: ${status}`);
      
      onVisitComplete?.(currentVisitId);
      
      // Reset for next visit
      setCurrentVisitId(null);
      setCurrentDoorId(null);
      setVisitNotes('');
      setCapturedPhotos([]);
      
    } catch (error) {
      triggerHapticFeedback('error');
      toast.error('Status-Update fehlgeschlagen');
    }
  };

  // Photo capture
  const handlePhotoCapture = async () => {
    try {
      if ('mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices) {
        const stream = await navigator.mediaDevices.getUserMedia({ 
          video: { 
            facingMode: 'environment',
            width: { ideal: 1920 },
            height: { ideal: 1080 }
          } 
        });
        
        // Create video element for preview
        const video = document.createElement('video');
        video.srcObject = stream;
        video.play();
        
        // Create canvas for capture
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        
        video.onloadedmetadata = () => {
          canvas.width = video.videoWidth;
          canvas.height = video.videoHeight;
          
          // Capture frame
          context?.drawImage(video, 0, 0);
          const photoData = canvas.toDataURL('image/jpeg', 0.8);
          
          setCapturedPhotos(prev => [...prev, photoData]);
          
          // Stop stream
          stream.getTracks().forEach(track => track.stop());
          
          triggerHapticFeedback('success');
          toast.success('Foto aufgenommen');
        };
        
      } else {
        // Fallback to file input
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';
        input.capture = 'environment';
        
        input.onchange = (e) => {
          const file = (e.target as HTMLInputElement).files?.[0];
          if (file) {
            const reader = new FileReader();
            reader.onload = () => {
              setCapturedPhotos(prev => [...prev, reader.result as string]);
              triggerHapticFeedback('success');
              toast.success('Foto aufgenommen');
            };
            reader.readAsDataURL(file);
          }
        };
        
        input.click();
      }
    } catch (error) {
      triggerHapticFeedback('error');
      toast.error('Foto-Aufnahme fehlgeschlagen');
      console.error('Photo capture error:', error);
    }
  };

  // Start GPS tracking on mount
  useEffect(() => {
    startGPSTracking();
    return () => stopGPSTracking();
  }, [startGPSTracking, stopGPSTracking]);

  return (
    <div className={className} ref={containerRef}>
      {/* Status Bar */}
      <Card className="mb-4">
        <CardContent className="pt-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {isOnline ? (
                <Wifi className="h-4 w-4 text-green-500" />
              ) : (
                <WifiOff className="h-4 w-4 text-red-500" />
              )}
              <span className="text-sm">
                {isOnline ? 'Online' : 'Offline'}
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              {isGPSTracking && (
                <>
                  <Target className="h-4 w-4 text-blue-500" />
                  <span className="text-sm">GPS aktiv</span>
                </>
              )}
            </div>
            
            {currentPosition && (
              <Badge variant="outline" className="text-xs">
                ±{currentPosition.accuracy.toFixed(0)}m
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Nearby Addresses */}
      {nearbyAddresses.length > 0 && (
        <Card className="mb-4">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <MapPin className="h-5 w-5 text-blue-500" />
              Adressen in der Nähe
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {nearbyAddresses.slice(0, 3).map((address, index) => (
                <div key={address.addressId} className="flex items-center justify-between p-2 bg-blue-50 rounded">
                  <div>
                    <p className="font-medium text-sm">{address.address}</p>
                    <p className="text-xs text-muted-foreground">
                      {address.distance.toFixed(0)}m entfernt
                    </p>
                  </div>
                  <Button
                    size="sm"
                    onClick={() => {
                      setCurrentVisitId(`visit-${address.houseId}`);
                      setCurrentDoorId(`door-${address.houseId}`);
                      triggerHapticFeedback('medium');
                    }}
                    className="h-10 min-w-[44px]"
                  >
                    <CheckCircle className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Voice Recording */}
      <Card className="mb-4">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Mic className="h-5 w-5 text-purple-500" />
            Sprachnotizen
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {/* Recording Controls */}
            <div className="flex items-center gap-2">
              {!recordingState.isRecording ? (
                <Button
                  onClick={startRecording}
                  className="flex-1 h-12 bg-red-500 hover:bg-red-600"
                  disabled={recordingState.error !== null}
                >
                  <Mic className="h-4 w-4 mr-2" />
                  Aufnahme starten
                </Button>
              ) : (
                <>
                  <Button
                    onClick={pauseRecording}
                    variant="outline"
                    className="flex-1 h-12"
                  >
                    {recordingState.isPaused ? (
                      <Play className="h-4 w-4 mr-2" />
                    ) : (
                      <Square className="h-4 w-4 mr-2" />
                    )}
                    {recordingState.isPaused ? 'Fortsetzen' : 'Pause'}
                  </Button>
                  <Button
                    onClick={stopRecording}
                    className="flex-1 h-12 bg-red-500 hover:bg-red-600"
                  >
                    <Square className="h-4 w-4 mr-2" />
                    Stopp
                  </Button>
                </>
              )}
            </div>

            {/* Recording Status */}
            {recordingState.isRecording && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">
                    {format(new Date(recordingState.duration * 1000), 'mm:ss')}
                  </span>
                  <Badge className={recordingState.isPaused ? 'bg-yellow-500' : 'bg-red-500'}>
                    {recordingState.isPaused ? 'Pausiert' : 'Aufnahme'}
                  </Badge>
                </div>
                <Progress 
                  value={recordingState.audioLevel * 100} 
                  className="h-2"
                />
              </div>
            )}

            {/* Recordings List */}
            {recordings.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Aufnahmen:</h4>
                {recordings.slice(-3).map((recording) => (
                  <div key={recording.id} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => playRecording(recording.id)}
                      className="h-8 w-8 p-0"
                    >
                      <Volume2 className="h-3 w-3" />
                    </Button>
                    <div className="flex-1">
                      <p className="text-xs">
                        {format(new Date(recording.timestamp), 'HH:mm', { locale: de })} - 
                        {recording.duration.toFixed(1)}s
                      </p>
                      {recording.transcription && (
                        <p className="text-xs text-muted-foreground truncate">
                          {recording.transcription}
                        </p>
                      )}
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => deleteRecording(recording.id)}
                      className="h-8 w-8 p-0 text-red-500"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Photo Capture */}
      <Card className="mb-4">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Camera className="h-5 w-5 text-green-500" />
            Fotos
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Button
            onClick={handlePhotoCapture}
            className="w-full h-12 mb-3"
            variant="outline"
          >
            <Camera className="h-4 w-4 mr-2" />
            Foto aufnehmen
          </Button>
          
          {capturedPhotos.length > 0 && (
            <div className="grid grid-cols-3 gap-2">
              {capturedPhotos.slice(-6).map((photo, index) => (
                <img
                  key={index}
                  src={photo}
                  alt={`Foto ${index + 1}`}
                  className="w-full h-20 object-cover rounded border"
                />
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      {currentVisitId && currentDoorId && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Zap className="h-5 w-5 text-yellow-500" />
              Schnellaktionen
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-2 mb-4">
              <Button
                onClick={() => handleQuickStatus('Angetroffen → Sale')}
                className="h-12 bg-green-500 hover:bg-green-600"
              >
                ↑ Sale
              </Button>
              <Button
                onClick={() => handleQuickStatus('Angetroffen → Termin')}
                className="h-12 bg-blue-500 hover:bg-blue-600"
              >
                ← Termin
              </Button>
              <Button
                onClick={() => handleQuickStatus('Angetroffen → kein Interesse')}
                className="h-12 bg-orange-500 hover:bg-orange-600"
              >
                → Kein Interesse
              </Button>
              <Button
                onClick={() => handleQuickStatus('N/A')}
                className="h-12 bg-red-500 hover:bg-red-600"
              >
                ↓ Nicht angetroffen
              </Button>
            </div>
            
            <div className="text-center">
              <p className="text-xs text-muted-foreground">
                Oder wischen Sie für Schnellaktionen
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default EnhancedVisitTracker;
