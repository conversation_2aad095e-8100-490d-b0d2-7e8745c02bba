import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { User, Lock } from 'lucide-react';
import <PERSON>Field from '../FormField';

// Mock haptic feedback
Object.defineProperty(navigator, 'vibrate', {
  writable: true,
  value: vi.fn(),
});

describe('FormField', () => {
  const defaultProps = {
    id: 'test-field',
    label: 'Test Field',
    value: '',
    onChange: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render basic form field', () => {
    render(<FormField {...defaultProps} />);
    
    expect(screen.getByLabelText('Test Field')).toBeInTheDocument();
    expect(screen.getByRole('textbox')).toBeInTheDocument();
  });

  it('should render with icon', () => {
    render(
      <FormField 
        {...defaultProps} 
        icon={<User data-testid="user-icon" />}
      />
    );
    
    expect(screen.getByTestId('user-icon')).toBeInTheDocument();
  });

  it('should show password toggle for password fields', () => {
    render(
      <FormField 
        {...defaultProps} 
        type="password"
        showPasswordToggle
        data-testid="password-field"
      />
    );
    
    expect(screen.getByTestId('password-field-password-toggle')).toBeInTheDocument();
  });

  it('should toggle password visibility', () => {
    render(
      <FormField
        {...defaultProps}
        type="password"
        showPasswordToggle
        data-testid="password-field"
      />
    );

    const input = screen.getByTestId('password-field');
    const toggleButton = screen.getByTestId('password-field-password-toggle');

    // Initially should be password type
    expect(input).toHaveAttribute('type', 'password');

    // Click toggle
    fireEvent.click(toggleButton);

    // Should now be text type
    expect(input).toHaveAttribute('type', 'text');
  });

  it('should display error message', () => {
    const error = {
      field: 'test-field',
      message: 'This field is required'
    };
    
    render(
      <FormField 
        {...defaultProps} 
        error={error}
      />
    );
    
    expect(screen.getByText('This field is required')).toBeInTheDocument();
    expect(screen.getByRole('alert')).toBeInTheDocument();
  });

  it('should show validation icon when valid', () => {
    const { container } = render(
      <FormField
        {...defaultProps}
        value="valid input"
        isValid={true}
      />
    );

    // Should show check icon for valid field - check for green color class
    const validationIcon = container.querySelector('.text-green-500');
    expect(validationIcon).toBeInTheDocument();
  });

  it('should call onChange when input changes', () => {
    const onChange = vi.fn();
    
    render(
      <FormField 
        {...defaultProps} 
        onChange={onChange}
      />
    );
    
    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: 'new value' } });
    
    expect(onChange).toHaveBeenCalledWith('new value');
  });

  it('should call onBlur when input loses focus', () => {
    const onBlur = vi.fn();
    
    render(
      <FormField 
        {...defaultProps} 
        onBlur={onBlur}
      />
    );
    
    const input = screen.getByRole('textbox');
    fireEvent.blur(input);
    
    expect(onBlur).toHaveBeenCalled();
  });

  it('should call onFocus when input gains focus', () => {
    const onFocus = vi.fn();
    
    render(
      <FormField 
        {...defaultProps} 
        onFocus={onFocus}
      />
    );
    
    const input = screen.getByRole('textbox');
    fireEvent.focus(input);
    
    expect(onFocus).toHaveBeenCalled();
  });

  it('should show character count when maxLength is set', () => {
    render(
      <FormField 
        {...defaultProps} 
        value="test"
        maxLength={10}
      />
    );
    
    expect(screen.getByText('4/10')).toBeInTheDocument();
  });

  it('should show required indicator', () => {
    render(
      <FormField 
        {...defaultProps} 
        required
      />
    );
    
    // The required indicator is added via CSS pseudo-element
    // We can check if the required prop is passed to the input
    const input = screen.getByRole('textbox');
    expect(input).toHaveAttribute('required');
  });

  it('should be disabled when disabled prop is true', () => {
    render(
      <FormField 
        {...defaultProps} 
        disabled
      />
    );
    
    const input = screen.getByRole('textbox');
    expect(input).toBeDisabled();
  });

  it('should have proper accessibility attributes', () => {
    const error = {
      field: 'test-field',
      message: 'Error message'
    };
    
    render(
      <FormField 
        {...defaultProps} 
        error={error}
      />
    );
    
    const errorElement = screen.getByRole('alert');
    expect(errorElement).toHaveAttribute('aria-live', 'polite');
  });

  it('should apply custom className', () => {
    render(
      <FormField 
        {...defaultProps} 
        className="custom-class"
      />
    );
    
    const container = screen.getByRole('textbox').closest('.custom-class');
    expect(container).toBeInTheDocument();
  });

  it('should handle email type correctly', () => {
    render(
      <FormField 
        {...defaultProps} 
        type="email"
      />
    );
    
    const input = screen.getByRole('textbox');
    expect(input).toHaveAttribute('type', 'email');
  });

  it('should meet minimum touch target requirements', () => {
    render(
      <FormField
        {...defaultProps}
        data-testid="touch-target"
      />
    );

    const input = screen.getByTestId('touch-target');

    // Check that min-height is set for touch targets
    expect(input).toHaveClass('min-h-[44px]');
    expect(input).toHaveClass('touch-manipulation');
  });

  describe('Enhanced Features', () => {
    it('should trigger haptic feedback on focus', async () => {
      const user = userEvent.setup();

      render(
        <FormField
          {...defaultProps}
          enableHapticFeedback
        />
      );

      const input = screen.getByRole('textbox');
      await user.click(input);

      expect(navigator.vibrate).toHaveBeenCalledWith([10]);
    });

    it('should not trigger haptic feedback when disabled', async () => {
      const user = userEvent.setup();

      render(
        <FormField
          {...defaultProps}
          enableHapticFeedback={false}
        />
      );

      const input = screen.getByRole('textbox');
      await user.click(input);

      expect(navigator.vibrate).not.toHaveBeenCalled();
    });

    it('should trigger haptic feedback on password toggle', async () => {
      const user = userEvent.setup();

      render(
        <FormField
          {...defaultProps}
          type="password"
          showPasswordToggle
          enableHapticFeedback
          data-testid="password-field"
        />
      );

      const toggleButton = screen.getByTestId('password-field-password-toggle');
      await user.click(toggleButton);

      expect(navigator.vibrate).toHaveBeenCalledWith([20]);
    });

    it('should display help text', () => {
      render(
        <FormField
          {...defaultProps}
          helpText="This is helpful information"
        />
      );

      expect(screen.getByText('This is helpful information')).toBeInTheDocument();
    });

    it('should hide help text when error is present', () => {
      const error = {
        field: 'test-field',
        message: 'Error message'
      };

      render(
        <FormField
          {...defaultProps}
          error={error}
          helpText="This should be hidden"
        />
      );

      expect(screen.queryByText('This should be hidden')).not.toBeInTheDocument();
      expect(screen.getByText('Error message')).toBeInTheDocument();
    });

    it('should show character count without maxLength when showCharacterCount is true', () => {
      render(
        <FormField
          {...defaultProps}
          value="hello"
          showCharacterCount
        />
      );

      expect(screen.getByText('5')).toBeInTheDocument();
    });

    it('should warn when approaching character limit', () => {
      render(
        <FormField
          {...defaultProps}
          value="123456789"
          maxLength={10}
        />
      );

      const counter = screen.getByText('9/10');
      expect(counter).toHaveClass('text-orange-500');
    });

    it('should show error color when at character limit', () => {
      render(
        <FormField
          {...defaultProps}
          value="1234567890"
          maxLength={10}
        />
      );

      const counter = screen.getByText('10/10');
      expect(counter).toHaveClass('text-red-500');
    });

    it('should have enhanced focus styles', async () => {
      const user = userEvent.setup();

      render(
        <FormField
          {...defaultProps}
        />
      );

      const input = screen.getByRole('textbox');
      await user.click(input);

      expect(input).toHaveClass('focus-visible:ring-2');
      expect(input).toHaveClass('focus-visible:ring-red-500/20');
    });
  });
});
