import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import ProductSelection from '../ProductSelection';
import { DataProvider } from '@/context/data';
import { AuthProvider } from '@/context/auth/AuthContext';
import { mockUser, mockAddress, mockHouse, mockVisit, mockDoor } from '@/test/test-utils';

// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useParams: () => ({ visitId: mockVisit.id }),
  };
});

// Mock sonner toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

const renderWithProviders = () => {
  return render(
    <BrowserRouter>
      <AuthProvider>
        <DataProvider>
          <ProductSelection />
        </DataProvider>
      </AuthProvider>
    </BrowserRouter>
  );
};

describe('ProductSelection', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock localStorage with test data including doors
    const mockData = {
      addresses: [mockAddress],
      houses: [mockHouse],
      visits: [mockVisit],
      doors: [mockDoor],
      products: [],
      users: [mockUser],
    };
    
    Object.keys(mockData).forEach(key => {
      localStorage.setItem(key, JSON.stringify(mockData[key as keyof typeof mockData]));
    });
    
    localStorage.setItem('currentUser', JSON.stringify(mockUser));
  });

  it('should render without crashing', () => {
    renderWithProviders();
    expect(screen.getByText('Produkte auswählen')).toBeInTheDocument();
  });

  it('should show door selection', () => {
    renderWithProviders();
    
    expect(screen.getByText('Türen auswählen')).toBeInTheDocument();
    expect(screen.getByText('Wohnung 1')).toBeInTheDocument(); // mockDoor.name
  });

  it('should allow selecting doors', () => {
    renderWithProviders();
    
    const doorCheckbox = screen.getByRole('checkbox', { name: /wohnung 1/i });
    expect(doorCheckbox).not.toBeChecked();
    
    fireEvent.click(doorCheckbox);
    expect(doorCheckbox).toBeChecked();
  });

  it('should add KIP product', () => {
    renderWithProviders();
    
    const addKIPButton = screen.getByText('KIP hinzufügen');
    fireEvent.click(addKIPButton);
    
    expect(screen.getByText('KIP Standard')).toBeInTheDocument();
  });

  it('should add TV product', () => {
    renderWithProviders();
    
    const addTVButton = screen.getByText('TV hinzufügen');
    fireEvent.click(addTVButton);
    
    expect(screen.getByDisplayValue('sky')).toBeInTheDocument();
  });

  it('should add Mobile product', () => {
    renderWithProviders();
    
    const addMobileButton = screen.getByText('Mobile hinzufügen');
    fireEvent.click(addMobileButton);
    
    expect(screen.getByDisplayValue('contract')).toBeInTheDocument();
  });

  it('should remove product when remove button is clicked', () => {
    renderWithProviders();
    
    // Add a product first
    const addKIPButton = screen.getByText('KIP hinzufügen');
    fireEvent.click(addKIPButton);
    
    expect(screen.getByText('KIP Standard')).toBeInTheDocument();
    
    // Remove the product
    const removeButton = screen.getByText('Entfernen');
    fireEvent.click(removeButton);
    
    expect(screen.queryByText('KIP Standard')).not.toBeInTheDocument();
  });

  it('should update product quantity', () => {
    renderWithProviders();
    
    // Add a product first
    const addKIPButton = screen.getByText('KIP hinzufügen');
    fireEvent.click(addKIPButton);
    
    const quantityInput = screen.getByDisplayValue('1');
    fireEvent.change(quantityInput, { target: { value: '3' } });
    
    expect(screen.getByDisplayValue('3')).toBeInTheDocument();
  });

  it('should submit products successfully', async () => {
    const { toast } = await import('sonner');
    
    renderWithProviders();
    
    // Select a door
    const doorCheckbox = screen.getByRole('checkbox', { name: /wohnung 1/i });
    fireEvent.click(doorCheckbox);
    
    // Add a product
    const addKIPButton = screen.getByText('KIP hinzufügen');
    fireEvent.click(addKIPButton);
    
    // Submit
    const submitButton = screen.getByText('Produkte speichern');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(toast.success).toHaveBeenCalledWith('1 Produkte für 1 Tür(en) erfolgreich gespeichert');
      expect(mockNavigate).toHaveBeenCalledWith('/daily-view');
    });
  });

  it('should submit without products when no products added', async () => {
    const { toast } = await import('sonner');
    
    renderWithProviders();
    
    // Select a door
    const doorCheckbox = screen.getByRole('checkbox', { name: /wohnung 1/i });
    fireEvent.click(doorCheckbox);
    
    // Submit without adding products
    const submitButton = screen.getByText('Produkte speichern');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(toast.success).toHaveBeenCalledWith('Besuch für 1 Tür(en) ohne Produkte gespeichert');
      expect(mockNavigate).toHaveBeenCalledWith('/daily-view');
    });
  });

  it('should show error when no doors are selected', async () => {
    const { toast } = await import('sonner');
    
    renderWithProviders();
    
    // Add a product but don't select any doors
    const addKIPButton = screen.getByText('KIP hinzufügen');
    fireEvent.click(addKIPButton);
    
    // Try to submit
    const submitButton = screen.getByText('Produkte speichern');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Bitte wählen Sie mindestens eine Tür aus');
    });
  });

  it('should disable submit button while submitting', async () => {
    renderWithProviders();
    
    // Select a door
    const doorCheckbox = screen.getByRole('checkbox', { name: /wohnung 1/i });
    fireEvent.click(doorCheckbox);
    
    const submitButton = screen.getByText('Produkte speichern');
    
    // Button should be enabled initially
    expect(submitButton).not.toBeDisabled();
    
    fireEvent.click(submitButton);
    
    // Button should be disabled while submitting
    expect(submitButton).toBeDisabled();
  });

  it('should navigate back when back button is clicked', () => {
    renderWithProviders();
    
    const backButton = screen.getByText('Zurück');
    fireEvent.click(backButton);
    
    expect(mockNavigate).toHaveBeenCalledWith(-1);
  });

  it('should show visit not found when invalid visit ID', () => {
    // Mock useParams to return invalid visit ID
    vi.doMock('react-router-dom', async () => {
      const actual = await vi.importActual('react-router-dom');
      return {
        ...actual,
        useNavigate: () => mockNavigate,
        useParams: () => ({ visitId: 'invalid-visit-id' }),
      };
    });
    
    renderWithProviders();
    
    expect(screen.getByText('Besuch nicht gefunden')).toBeInTheDocument();
  });

  it('should handle multiple products of different categories', () => {
    renderWithProviders();
    
    // Add multiple products
    fireEvent.click(screen.getByText('KIP hinzufügen'));
    fireEvent.click(screen.getByText('TV hinzufügen'));
    fireEvent.click(screen.getByText('Mobile hinzufügen'));
    
    // Should show all products
    expect(screen.getByText('KIP Standard')).toBeInTheDocument();
    expect(screen.getByDisplayValue('sky')).toBeInTheDocument();
    expect(screen.getByDisplayValue('contract')).toBeInTheDocument();
  });

  it('should update product type when dropdown changes', () => {
    renderWithProviders();
    
    // Add a TV product
    fireEvent.click(screen.getByText('TV hinzufügen'));
    
    const typeSelect = screen.getByDisplayValue('sky');
    fireEvent.change(typeSelect, { target: { value: 'netflix' } });
    
    expect(screen.getByDisplayValue('netflix')).toBeInTheDocument();
  });

  it('should show correct product count in submit button', () => {
    renderWithProviders();
    
    // Initially no products
    expect(screen.getByText('Produkte speichern')).toBeInTheDocument();
    
    // Add products
    fireEvent.click(screen.getByText('KIP hinzufügen'));
    fireEvent.click(screen.getByText('TV hinzufügen'));
    
    // Should show count
    expect(screen.getByText('2 Produkte speichern')).toBeInTheDocument();
  });
});
