
import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { User, UserRole } from '@/types';
import { Edit } from 'lucide-react';

interface Team {
  id: string;
  name: string;
}

interface ExtendedUser extends User {
  is_active?: boolean;
}

interface UserEditDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  user: ExtendedUser | null;
  teams: Team[];
  mentors: User[];
  onUpdateRole: (role: UserRole) => void;
  onUpdateTeam: (teamId: string) => void;
  onUpdateMentor: (mentorId: string) => void;
  onUpdateActive: (active: boolean) => void;
  onSave: () => void;
}

export const UserEditDialog: React.FC<UserEditDialogProps> = ({
  isOpen,
  onOpenChange,
  user,
  teams,
  mentors,
  onUpdateRole,
  onUpdateTeam,
  onUpdateMentor,
  onUpdateActive,
  onSave,
}) => {
  if (!user) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5 text-blue-600" />
            Benutzer bearbeiten: {user.name}
          </DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="edit-role">Rolle ändern</Label>
            <Select
              value={user.role}
              onValueChange={(value: UserRole) => onUpdateRole(value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="berater">Berater</SelectItem>
                <SelectItem value="mentor">Mentor</SelectItem>
                <SelectItem value="teamleiter">Teamleiter</SelectItem>
                <SelectItem value="gebietsmanager">Gebietsmanager</SelectItem>
                <SelectItem value="admin">Administrator</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="edit-team">Team zuweisen</Label>
            <Select
              value={user.teamId || ''}
              onValueChange={(value) => onUpdateTeam(value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Team auswählen" />
              </SelectTrigger>
              <SelectContent>
                {teams.map((team) => (
                  <SelectItem key={team.id} value={team.id || ''}>
                    {team.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          {user.role === 'berater' && (
            <div className="grid gap-2">
              <Label htmlFor="edit-mentor">Mentor zuweisen</Label>
              <Select
                value={user.mentorId || ''}
                onValueChange={(value) => onUpdateMentor(value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Mentor auswählen" />
                </SelectTrigger>
                <SelectContent>
                  {mentors.map((mentor) => (
                    <SelectItem key={mentor.id} value={mentor.id}>
                      {mentor.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          <div className="flex items-center space-x-2">
            <Switch
              id="active"
              checked={user.is_active !== false}
              onCheckedChange={onUpdateActive}
            />
            <Label htmlFor="active">Benutzer ist aktiv</Label>
          </div>
        </div>
        <Button onClick={onSave}>Änderungen speichern</Button>
      </DialogContent>
    </Dialog>
  );
};
