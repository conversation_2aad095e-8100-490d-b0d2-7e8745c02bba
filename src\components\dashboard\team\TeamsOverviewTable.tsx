
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, Card<PERSON>itle, CardDescription } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { TeamData } from './types';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';
import { useIsMobile } from '@/hooks/use-mobile';
import { Building } from 'lucide-react';

interface TeamsOverviewTableProps {
  teamStats: TeamData[];
}

const TeamsOverviewTable: React.FC<TeamsOverviewTableProps> = ({ teamStats }) => {
  const isMobile = useIsMobile();
  
  return (
    <Card className="glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-slide-in">
      <CardHeader className={`${isMobile ? 'p-4 pb-2' : 'p-6 pb-4'}`}>
        <CardTitle className={`flex items-center gap-3 ${isMobile ? 'text-lg' : 'text-2xl'} font-bold text-gray-800`}>
          <Building className={`${isMobile ? 'h-5 w-5' : 'h-6 w-6'} text-blue-600`} />
          Teams-Übersicht
        </CardTitle>
        <CardDescription className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-600`}>
          {format(new Date(), "'Stand:' d. MMMM yyyy", { locale: de })}
        </CardDescription>
      </CardHeader>
      <CardContent className={`${isMobile ? 'p-4 pt-0' : 'p-6 pt-0'}`}>
        <div className="overflow-x-auto scrollbar-none">
          <Table>
            <TableHeader>
              <TableRow className="border-gray-200">
                <TableHead className={`font-semibold text-gray-700 ${isMobile ? 'text-xs' : 'text-sm'}`}>Name</TableHead>
                <TableHead className={`font-semibold text-gray-700 ${isMobile ? 'text-xs' : 'text-sm'}`}>Mit.</TableHead>
                <TableHead className={`font-semibold text-gray-700 ${isMobile ? 'text-xs' : 'text-sm'}`}>Bes.</TableHead>
                <TableHead className={`font-semibold text-gray-700 ${isMobile ? 'text-xs' : 'text-sm'}`}>Verk.</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {teamStats.map((team, index) => (
                <TableRow key={team.id} className="hover:bg-blue-50/50 transition-colors border-gray-100" style={{ animationDelay: `${index * 0.05}s` }}>
                  <TableCell className={`font-medium text-gray-800 ${isMobile ? 'text-sm' : 'text-base'}`}>
                    {team.name}
                  </TableCell>
                  <TableCell className={`${isMobile ? 'text-sm' : 'text-base'}`}>
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800 hover:bg-blue-200">
                      {team.memberCount}
                    </Badge>
                  </TableCell>
                  <TableCell className={`${isMobile ? 'text-sm' : 'text-base'}`}>
                    <Badge variant="secondary" className="bg-purple-100 text-purple-800 hover:bg-purple-200">
                      {team.visitCount}
                    </Badge>
                  </TableCell>
                  <TableCell className={`${isMobile ? 'text-sm' : 'text-base'}`}>
                    <Badge variant="secondary" className="bg-green-100 text-green-800 hover:bg-green-200">
                      {team.salesCount}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
              {teamStats.length === 0 && (
                <TableRow>
                  <TableCell colSpan={4} className={`text-center py-8 text-gray-500 ${isMobile ? 'text-sm' : 'text-base'}`}>
                    Keine Teams gefunden
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};

export default TeamsOverviewTable;
