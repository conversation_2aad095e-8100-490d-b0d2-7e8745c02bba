
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Users, CheckCircle } from 'lucide-react';

interface StatisticsSummaryCardsProps {
  totalDoors: number;
  salesCount: number;
}

export const StatisticsSummaryCards: React.FC<StatisticsSummaryCardsProps> = ({
  totalDoors,
  salesCount
}) => {
  return (
    <div className="grid grid-cols-2 gap-4">
      <Card className="glass-card hover-lift rounded-3xl border-0 shadow-2xl">
        <CardContent className="p-6 text-center">
          <div className="flex items-center justify-center mb-3">
            <Users className="h-8 w-8 text-blue-600" />
          </div>
          <p className="text-sm text-muted-foreground mb-2">Gesamttüren</p>
          <p className="text-3xl font-bold text-gray-800">{totalDoors}</p>
        </CardContent>
      </Card>

      <Card className="glass-card hover-lift rounded-3xl border-0 shadow-2xl">
        <CardContent className="p-6 text-center">
          <div className="flex items-center justify-center mb-3">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <p className="text-sm text-muted-foreground mb-2">Sales</p>
          <p className="text-3xl font-bold text-green-600">{salesCount}</p>
        </CardContent>
      </Card>
    </div>
  );
};
