
import React from 'react';
import { StatisticsSummaryCards } from './StatisticsSummaryCards';
import { StatusChart } from './StatusChart';
import { ProductChart } from './ProductChart';

interface StatisticsContentProps {
  totalDoors: number;
  salesCount: number;
  statusData: Array<{ name: string; count: number }>;
  productData: Array<{ name: string; count: number }>;
  isMobile: boolean;
}

export const StatisticsContent: React.FC<StatisticsContentProps> = ({
  totalDoors,
  salesCount,
  statusData,
  productData,
  isMobile
}) => {
  return (
    <div className="space-y-6 animate-fade-in">
      <StatisticsSummaryCards 
        totalDoors={totalDoors}
        salesCount={salesCount}
      />
      <StatusChart data={statusData} isMobile={isMobile} />
      <ProductChart data={productData} isMobile={isMobile} />
    </div>
  );
};
