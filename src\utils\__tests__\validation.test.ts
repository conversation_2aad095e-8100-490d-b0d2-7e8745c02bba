import { describe, it, expect } from 'vitest';
import {
  validateEmail,
  validateName,
  validatePassword,
  validatePasswordConfirmation,
  validateLoginCredentials,
  validateRegistrationData,
} from '../validation';

describe('Validation Utils', () => {
  describe('validateEmail', () => {
    it('should return null for valid email', () => {
      expect(validateEmail('<EMAIL>')).toBeNull();
      expect(validateEmail('<EMAIL>')).toBeNull();
    });

    it('should return error for empty email', () => {
      const result = validateEmail('');
      expect(result).toEqual({
        field: 'email',
        message: 'E-Mail-Adresse ist erforderlich'
      });
    });

    it('should return error for invalid email format', () => {
      const result = validateEmail('invalid-email');
      expect(result).toEqual({
        field: 'email',
        message: 'Bitte geben Sie eine gültige E-Mail-Adresse ein'
      });
    });

    it('should return error for email without domain', () => {
      const result = validateEmail('test@');
      expect(result).toEqual({
        field: 'email',
        message: 'Bitte geben Sie eine gültige E-Mail-Adresse ein'
      });
    });
  });

  describe('validateName', () => {
    it('should return null for valid name', () => {
      expect(validateName('John Doe')).toBeNull();
      expect(validateName('Max Mustermann')).toBeNull();
    });

    it('should return error for empty name', () => {
      const result = validateName('');
      expect(result).toEqual({
        field: 'name',
        message: 'Name ist erforderlich'
      });
    });

    it('should return error for name too short', () => {
      const result = validateName('A');
      expect(result).toEqual({
        field: 'name',
        message: 'Name muss mindestens 2 Zeichen lang sein'
      });
    });

    it('should return error for name too long', () => {
      const longName = 'A'.repeat(51);
      const result = validateName(longName);
      expect(result).toEqual({
        field: 'name',
        message: 'Name darf maximal 50 Zeichen lang sein'
      });
    });

    it('should handle whitespace correctly', () => {
      expect(validateName('  ')).toEqual({
        field: 'name',
        message: 'Name ist erforderlich'
      });
      expect(validateName('  John  ')).toBeNull();
    });
  });

  describe('validatePassword', () => {
    it('should return weak strength for simple password', () => {
      const result = validatePassword('123');
      expect(result.strength).toBe('weak');
      expect(result.score).toBe(1);
      expect(result.requirements.minLength).toBe(false);
    });

    it('should return strong strength for complex password', () => {
      const result = validatePassword('MySecure123!');
      expect(result.strength).toBe('strong');
      expect(result.score).toBe(5);
      expect(result.requirements.minLength).toBe(true);
      expect(result.requirements.hasUppercase).toBe(true);
      expect(result.requirements.hasLowercase).toBe(true);
      expect(result.requirements.hasNumbers).toBe(true);
      expect(result.requirements.hasSpecialChars).toBe(true);
    });

    it('should provide helpful suggestions', () => {
      const result = validatePassword('password');
      expect(result.suggestions).toContain('Großbuchstaben hinzufügen');
      expect(result.suggestions).toContain('Zahlen hinzufügen');
      expect(result.suggestions).toContain('Sonderzeichen hinzufügen (!@#$%^&*)');
    });

    it('should handle edge cases', () => {
      expect(validatePassword('').strength).toBe('weak');
      // '12345678' only meets minLength and hasNumbers = 2 requirements = weak
      expect(validatePassword('12345678').strength).toBe('weak');
      // 'Password1' meets minLength, hasUppercase, hasLowercase, hasNumbers = 4 requirements = good
      expect(validatePassword('Password1').strength).toBe('good');
    });
  });

  describe('validatePasswordConfirmation', () => {
    it('should return null when passwords match', () => {
      expect(validatePasswordConfirmation('password123', 'password123')).toBeNull();
    });

    it('should return error when confirmation is empty', () => {
      const result = validatePasswordConfirmation('password123', '');
      expect(result).toEqual({
        field: 'confirmPassword',
        message: 'Passwort-Bestätigung ist erforderlich'
      });
    });

    it('should return error when passwords do not match', () => {
      const result = validatePasswordConfirmation('password123', 'different');
      expect(result).toEqual({
        field: 'confirmPassword',
        message: 'Passwörter stimmen nicht überein'
      });
    });
  });

  describe('validateLoginCredentials', () => {
    it('should return empty array for valid credentials', () => {
      const result = validateLoginCredentials('<EMAIL>', 'password123');
      expect(result).toEqual([]);
    });

    it('should return errors for empty fields', () => {
      const result = validateLoginCredentials('', '');
      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        field: 'emailOrName',
        message: 'E-Mail oder Name ist erforderlich'
      });
      expect(result[1]).toEqual({
        field: 'password',
        message: 'Passwort ist erforderlich'
      });
    });

    it('should handle whitespace correctly', () => {
      const result = validateLoginCredentials('  ', '  ');
      expect(result).toHaveLength(2);
    });
  });

  describe('validateRegistrationData', () => {
    it('should return empty array for valid data', () => {
      const result = validateRegistrationData(
        'John Doe',
        '<EMAIL>',
        'SecurePass123!',
        'SecurePass123!'
      );
      expect(result).toEqual([]);
    });

    it('should return errors for invalid data', () => {
      const result = validateRegistrationData(
        '',
        'invalid-email',
        'weak',
        'different'
      );
      expect(result.length).toBeGreaterThan(0);
      
      const fieldErrors = result.map(error => error.field);
      expect(fieldErrors).toContain('name');
      expect(fieldErrors).toContain('email');
      expect(fieldErrors).toContain('password');
      expect(fieldErrors).toContain('confirmPassword');
    });

    it('should validate password strength', () => {
      const result = validateRegistrationData(
        'John Doe',
        '<EMAIL>',
        'weak',
        'weak'
      );
      
      const passwordError = result.find(error => error.field === 'password');
      expect(passwordError).toBeDefined();
      expect(passwordError?.message).toContain('zu schwach');
    });
  });
});
