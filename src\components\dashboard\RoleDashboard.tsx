
import React from 'react';
import { useAuth } from '@/context/auth';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import BeraterDashboard from './BeraterDashboard';
import MentorDashboard from './MentorDashboard';
import TeamleiterDashboard from './TeamleiterDashboard';
import GebietsmanagerDashboard from './GebietsmanagerDashboard';
import AdminDashboard from './AdminDashboard';

const RoleDashboard: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();

  if (!user) {
    return (
      <Card className="h-full flex items-center justify-center">
        <CardContent className="pt-6 text-center">
          <p className="text-sm md:text-base">Bitte melden Sie sich an, um Ihre <PERSON>t zu sehen.</p>
          <Button 
            onClick={() => navigate('/login')} 
            className="mt-4 text-sm md:text-base"
          >
            Zum Login
          </Button>
        </CardContent>
      </Card>
    );
  }

  // Zeige die entsprechende Dashboard-Komponente basierend auf der Benutzerrolle
  switch (user.role) {
    case 'berater':
      return <BeraterDashboard userId={user.id} />;
    case 'mentor':
      return <MentorDashboard mentorId={user.id} />;
    case 'teamleiter':
      return <TeamleiterDashboard teamId={user.teamId || ''} />;
    case 'gebietsmanager':
      return <GebietsmanagerDashboard />;
    case 'admin':
      return <AdminDashboard />;
    default:
      return (
        <Card className="h-full flex items-center justify-center">
          <CardHeader>
            <CardTitle className="text-base md:text-lg">Willkommen</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm md:text-base">Für Ihre Rolle ist keine spezifische Übersicht verfügbar.</p>
          </CardContent>
        </Card>
      );
  }
};

export default RoleDashboard;
