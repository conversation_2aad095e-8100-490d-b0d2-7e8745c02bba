import { describe, it, expect } from 'vitest';
import {
  validateEmail,
  validateName,
  validatePassword,
  validatePasswordConfirmation,
  validateLoginCredentials,
  validateRegistrationData,
  createFormValidator,
} from '../validation';

describe('Enhanced Validation Utilities', () => {
  describe('validateEmail', () => {
    it('should validate correct email addresses', () => {
      expect(validateEmail('<EMAIL>')).toBeNull();
      expect(validateEmail('<EMAIL>')).toBeNull();
      expect(validateEmail('<EMAIL>')).toBeNull();
    });

    it('should reject invalid email addresses', () => {
      expect(validateEmail('')).toEqual({
        field: 'email',
        message: 'E-Mail-Adresse ist erforderlich'
      });
      
      expect(validateEmail('invalid-email')).toEqual({
        field: 'email',
        message: 'Bitte geben Sie eine gültige E-Mail-Adresse ein'
      });
      
      expect(validateEmail('test@')).toEqual({
        field: 'email',
        message: 'Bitte geben Sie eine gültige E-Mail-Adresse ein'
      });
    });
  });

  describe('validateName', () => {
    it('should validate correct names', () => {
      expect(validateName('John Doe')).toBeNull();
      expect(validateName('Maria García')).toBeNull();
      expect(validateName('李小明')).toBeNull();
    });

    it('should reject invalid names', () => {
      expect(validateName('')).toEqual({
        field: 'name',
        message: 'Name ist erforderlich'
      });
      
      expect(validateName('A')).toEqual({
        field: 'name',
        message: 'Name muss mindestens 2 Zeichen lang sein'
      });
      
      expect(validateName('A'.repeat(51))).toEqual({
        field: 'name',
        message: 'Name darf maximal 50 Zeichen lang sein'
      });
    });
  });

  describe('validatePassword', () => {
    it('should validate strong passwords', () => {
      const result = validatePassword('MySecure123!Password');
      expect(result.strength).toBe('excellent');
      expect(result.isValid).toBe(true);
      expect(result.requirements.minLength).toBe(true);
      expect(result.requirements.hasUppercase).toBe(true);
      expect(result.requirements.hasLowercase).toBe(true);
      expect(result.requirements.hasNumbers).toBe(true);
      expect(result.requirements.hasSpecialChars).toBe(true);
      expect(result.requirements.noCommonPatterns).toBe(true);
      expect(result.entropy).toBeGreaterThan(0);
    });

    it('should identify weak passwords', () => {
      const result = validatePassword('password');
      expect(result.strength).toBe('weak');
      expect(result.isValid).toBe(false);
      expect(result.requirements.noCommonPatterns).toBe(false);
      expect(result.suggestions).toContain('Vermeiden Sie häufige Muster oder Wörter');
    });

    it('should detect common patterns', () => {
      const result = validatePassword('abc123');
      expect(result.requirements.noCommonPatterns).toBe(false);
      expect(result.suggestions).toContain('Vermeiden Sie häufige Muster oder Wörter');
    });

    it('should detect personal information in passwords', () => {
      const personalInfo = { name: 'John Doe', email: '<EMAIL>' };
      const result = validatePassword('JohnPassword123!', personalInfo);
      expect(result.requirements.noPersonalInfo).toBe(false);
      expect(result.suggestions).toContain('Vermeiden Sie persönliche Informationen');
    });

    it('should provide helpful suggestions for improvement', () => {
      const result = validatePassword('weak');
      expect(result.suggestions).toContain('Mindestens 8 Zeichen verwenden');
      expect(result.suggestions).toContain('Großbuchstaben hinzufügen (A-Z)');
      expect(result.suggestions).toContain('Zahlen hinzufügen (0-9)');
      expect(result.suggestions).toContain('Sonderzeichen hinzufügen (!@#$%^&*)');
    });

    it('should calculate entropy correctly', () => {
      const simpleResult = validatePassword('abc');
      const complexResult = validatePassword('MyComplex123!Password');
      expect(complexResult.entropy).toBeGreaterThan(simpleResult.entropy);
    });

    it('should handle different strength levels', () => {
      expect(validatePassword('weak').strength).toBe('weak');
      expect(validatePassword('Better1').strength).toBe('fair');
      expect(validatePassword('Better1!').strength).toBe('good');
      expect(validatePassword('MyGood123!').strength).toBe('strong');
      expect(validatePassword('MyExcellent123!Password').strength).toBe('excellent');
    });
  });

  describe('validatePasswordConfirmation', () => {
    it('should validate matching passwords', () => {
      expect(validatePasswordConfirmation('password123', 'password123')).toBeNull();
    });

    it('should reject non-matching passwords', () => {
      expect(validatePasswordConfirmation('password123', 'different')).toEqual({
        field: 'confirmPassword',
        message: 'Passwörter stimmen nicht überein'
      });
    });

    it('should reject empty confirmation', () => {
      expect(validatePasswordConfirmation('password123', '')).toEqual({
        field: 'confirmPassword',
        message: 'Passwort-Bestätigung ist erforderlich'
      });
    });
  });

  describe('validateLoginCredentials', () => {
    it('should validate correct login credentials', () => {
      const errors = validateLoginCredentials('<EMAIL>', 'password123');
      expect(errors).toHaveLength(0);
    });

    it('should return errors for missing credentials', () => {
      const errors = validateLoginCredentials('', '');
      expect(errors).toHaveLength(2);
      expect(errors[0]).toEqual({
        field: 'emailOrName',
        message: 'E-Mail oder Name ist erforderlich'
      });
      expect(errors[1]).toEqual({
        field: 'password',
        message: 'Passwort ist erforderlich'
      });
    });
  });

  describe('validateRegistrationData', () => {
    it('should validate correct registration data', () => {
      const errors = validateRegistrationData(
        'John Doe',
        '<EMAIL>',
        'MySecure123!Password',
        'MySecure123!Password'
      );
      expect(errors).toHaveLength(0);
    });

    it('should return errors for invalid data', () => {
      const errors = validateRegistrationData(
        '',
        'invalid-email',
        'weak',
        'different'
      );
      expect(errors.length).toBeGreaterThan(0);
      
      // Check that we get errors for each invalid field
      const fieldErrors = errors.map(e => e.field);
      expect(fieldErrors).toContain('name');
      expect(fieldErrors).toContain('email');
      expect(fieldErrors).toContain('confirmPassword');
    });

    it('should reject weak passwords', () => {
      const errors = validateRegistrationData(
        'John Doe',
        '<EMAIL>',
        'weak',
        'weak'
      );
      
      const passwordError = errors.find(e => e.field === 'password');
      expect(passwordError).toBeDefined();
      expect(passwordError?.message).toContain('zu schwach');
    });
  });

  describe('createFormValidator', () => {
    it('should create a working form validator', () => {
      const validator = createFormValidator();
      
      // Test setting and getting errors
      const error = { field: 'email', message: 'Invalid email' };
      validator.setError(error);
      expect(validator.hasErrors()).toBe(true);
      expect(validator.getFieldError('email')).toBeNull(); // Not touched yet
      
      // Touch the field
      validator.setTouched('email');
      expect(validator.getFieldError('email')).toEqual(error);
      
      // Clear error
      validator.clearError('email');
      expect(validator.getFieldError('email')).toBeNull();
      expect(validator.hasErrors()).toBe(false);
    });

    it('should validate individual fields', () => {
      const validator = createFormValidator();
      
      // Test email validation
      const emailError = validator.validateField('email', 'invalid-email');
      expect(emailError).toBeDefined();
      expect(emailError?.field).toBe('email');
      
      // Test valid email
      const validEmailError = validator.validateField('email', '<EMAIL>');
      expect(validEmailError).toBeNull();
      
      // Test name validation
      const nameError = validator.validateField('name', '');
      expect(nameError).toBeDefined();
      expect(nameError?.field).toBe('name');
    });

    it('should reset validator state', () => {
      const validator = createFormValidator();
      
      // Add some errors and touched fields
      validator.setError({ field: 'email', message: 'Error' });
      validator.setTouched('email');
      
      expect(validator.hasErrors()).toBe(true);
      
      // Reset
      validator.reset();
      expect(validator.hasErrors()).toBe(false);
      expect(validator.getFieldError('email')).toBeNull();
    });

    it('should validate all fields', () => {
      const validator = createFormValidator();
      
      // Add an error
      validator.setError({ field: 'email', message: 'Error' });
      expect(validator.validateAll()).toBe(false);
      
      // Clear error
      validator.clearError('email');
      expect(validator.validateAll()).toBe(true);
    });
  });

  describe('Password Strength Levels', () => {
    it('should correctly categorize password strengths', () => {
      const testCases = [
        { password: '123', expectedStrength: 'weak' },
        { password: 'password', expectedStrength: 'weak' },
        { password: 'Password1', expectedStrength: 'fair' },
        { password: 'Password1!', expectedStrength: 'good' },
        { password: 'MySecure123!', expectedStrength: 'strong' },
        { password: 'MyVerySecure123!Password', expectedStrength: 'excellent' },
      ];

      testCases.forEach(({ password, expectedStrength }) => {
        const result = validatePassword(password);
        expect(result.strength).toBe(expectedStrength);
      });
    });
  });

  describe('Security Features', () => {
    it('should detect repeated characters', () => {
      const result = validatePassword('aaa123ABC!');
      expect(result.requirements.noCommonPatterns).toBe(false);
    });

    it('should detect sequential patterns', () => {
      const patterns = ['123456', 'abcdef', 'qwerty'];
      patterns.forEach(pattern => {
        const result = validatePassword(pattern + 'ABC!');
        expect(result.requirements.noCommonPatterns).toBe(false);
      });
    });

    it('should provide bonus points for length and entropy', () => {
      const shortResult = validatePassword('MyGood123!');
      const longResult = validatePassword('MyVeryLongAndSecure123!Password');
      
      expect(longResult.score).toBeGreaterThan(shortResult.score);
      expect(longResult.entropy).toBeGreaterThan(shortResult.entropy);
    });
  });
});
