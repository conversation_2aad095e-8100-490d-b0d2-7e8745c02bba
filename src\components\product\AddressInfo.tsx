
import React from 'react';
import { Address, House } from '@/types';

interface AddressInfoProps {
  address: Address;
  house: House;
}

export const AddressInfo: React.FC<AddressInfoProps> = ({ address, house }) => {
  return (
    <div className="text-center pb-4 pt-4">
      <h1 className="text-xl md:text-2xl font-bold text-gray-800 mb-3">
        Produkte erfassen
      </h1>
      
      <div className="p-3 md:p-4 glass-card rounded-xl">
        <p className="text-base md:text-lg font-semibold text-gray-800">
          {address.street} {house.houseNumber}
        </p>
        <p className="text-gray-600 text-sm md:text-base">
          {address.zipCode} {address.city}
        </p>
      </div>
    </div>
  );
};
