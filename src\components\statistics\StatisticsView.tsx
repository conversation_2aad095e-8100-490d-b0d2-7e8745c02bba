
import React, { useState } from 'react';
import { useData } from '@/context/data';
import { VisitStatus, ProductCategory } from '@/types';
import { useAuth } from '@/context/auth';
import { useIsMobile } from '@/hooks/use-mobile';
import { generateExportData, downloadJSON } from './exportUtils';
import { useToast } from '@/hooks/use-toast';
import { StatisticsHeader } from './StatisticsHeader';
import { StatisticsTabs } from './StatisticsTabs';

const StatisticsView: React.FC = () => {
  const { houses, visits, doors, products, addresses } = useData();
  const { user } = useAuth();
  const [period, setPeriod] = useState<'day' | 'week' | 'month' | 'custom'>('day');
  const [customDateRange, setCustomDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({ from: undefined, to: undefined });
  const isMobile = useIsMobile();
  const { toast } = useToast();

  // Calculate statistics
  const getTotalDoors = () => {
    const efhCount = visits.filter(v => {
      const house = houses.find(h => h.id === v.houseId);
      return house?.type === 'EFH';
    }).length;
    
    const mfhDoorCount = doors.length;
    
    return efhCount + mfhDoorCount;
  };

  const getStatusCount = (status: VisitStatus) => {
    return doors.filter(d => d.status === status).length;
  };

  const getProductCount = (category: ProductCategory) => {
    return products.filter(p => p.category === category).length;
  };

  // Prepare chart data
  const statusData = [
    { name: 'N/A', count: getStatusCount('N/A') },
    { name: 'Termin', count: getStatusCount('Angetroffen → Termin') },
    { name: 'Kein Interesse', count: getStatusCount('Angetroffen → Kein Interesse') },
    { name: 'Sale', count: getStatusCount('Angetroffen → Sale') },
  ];

  const productData = [
    { name: 'KIP', count: getProductCount('KIP') },
    { name: 'TV', count: getProductCount('TV') },
    { name: 'Mobile', count: getProductCount('Mobile') },
  ];

  const handleExport = () => {
    if (period === 'custom' && (!customDateRange.from || !customDateRange.to)) {
      toast({
        title: "Fehler",
        description: "Bitte wählen Sie einen vollständigen Datumsbereich aus.",
        variant: "destructive"
      });
      return;
    }

    try {
      const exportData = generateExportData(
        period,
        visits,
        doors,
        houses,
        addresses,
        period === 'custom' ? customDateRange : undefined
      );
      
      downloadJSON(exportData);
      
      toast({
        title: "Export erfolgreich",
        description: `${exportData.totalVisits} Besuche wurden exportiert.`
      });
    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: "Export fehlgeschlagen",
        description: "Beim Exportieren ist ein Fehler aufgetreten.",
        variant: "destructive"
      });
    }
  };

  return (
    <div className={`${isMobile ? 'px-4 py-6' : 'p-6'} space-y-6 w-full animate-fade-in`}>
      <StatisticsHeader onExport={handleExport} isMobile={isMobile} />
      
      <StatisticsTabs
        period={period}
        onPeriodChange={setPeriod}
        customDateRange={customDateRange}
        onCustomDateRangeChange={setCustomDateRange}
        totalDoors={getTotalDoors()}
        salesCount={getStatusCount('Angetroffen → Sale')}
        statusData={statusData}
        productData={productData}
        isMobile={isMobile}
      />
    </div>
  );
};

export default StatisticsView;
