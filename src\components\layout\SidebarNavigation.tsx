
import React from 'react';
import { Link } from 'react-router-dom';
import { User } from '@/types';
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton
} from '@/components/ui/sidebar';
import {
  Users,
  ChartBar,
  LayoutGrid,
  BarChart4,
  Settings,
  Map,
  MapPin,
  Building,
  Shield
} from 'lucide-react';

interface SidebarNavigationProps {
  user: User | null;
}

export const SidebarNavigation: React.FC<SidebarNavigationProps> = ({ user }) => {
  if (!user) return null;

  switch(user.role) {
    case 'mentor':
      return (
        <SidebarGroup>
          <SidebarGroupLabel>Mentor Navigation</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <Link to="/team-overview" className="flex items-center">
                    <Users className="mr-2 h-4 w-4" />
                    <span>Team Übersicht</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <Link to="/berater-statistics" className="flex items-center">
                    <ChartBar className="mr-2 h-4 w-4" />
                    <span>Berater Statistiken</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      );
    
    case 'teamleiter':
      return (
        <SidebarGroup>
          <SidebarGroupLabel>Teamleiter Navigation</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <Link to="/teams-overview" className="flex items-center">
                    <LayoutGrid className="mr-2 h-4 w-4" />
                    <span>Teams Übersicht</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <Link to="/teams-statistics" className="flex items-center">
                    <BarChart4 className="mr-2 h-4 w-4" />
                    <span>Teams Statistiken</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      );
    
    case 'gebietsmanager':
      return (
        <SidebarGroup>
          <SidebarGroupLabel>Manager Navigation</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <Link to="/area-overview" className="flex items-center">
                    <LayoutGrid className="mr-2 h-4 w-4" />
                    <span>Gebietsübersicht</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      );
    
    case 'admin':
      return (
        <SidebarGroup>
          <SidebarGroupLabel>Admin Navigation</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <Link to="/user-management" className="flex items-center">
                    <Users className="mr-2 h-4 w-4" />
                    <span>Benutzerverwaltung</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <Link to="/area-management" className="flex items-center">
                    <MapPin className="mr-2 h-4 w-4" />
                    <span>Gebiete verwalten</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <Link to="/team-management" className="flex items-center">
                    <Building className="mr-2 h-4 w-4" />
                    <span>Teams verwalten</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <Link to="/settings" className="flex items-center">
                    <Settings className="mr-2 h-4 w-4" />
                    <span>Einstellungen</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      );
    
    default:
      return null;
  }
};
