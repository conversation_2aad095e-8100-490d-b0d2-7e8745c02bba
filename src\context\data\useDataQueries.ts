
import { 
  Address, 
  House, 
  Visit, 
  Door, 
  ProductEntry 
} from "@/types";
import { 
  getTodaysHousesUtil, 
  getTodaysVisitsUtil, 
  getHousesByAddressUtil 
} from "./dataUtils";

interface DataQueriesProps {
  addresses: Address[];
  houses: House[];
  visits: Visit[];
  doors: Door[];
  products: ProductEntry[];
}

export const useDataQueries = ({
  addresses,
  houses,
  visits,
  doors,
  products,
}: DataQueriesProps) => {
  
  const getVisitsByHouse = (houseId: string): Visit[] => {
    return visits.filter(visit => visit.houseId === houseId);
  };

  const getDoorsByVisit = (visitId: string): Door[] => {
    return doors.filter(door => door.visitId === visitId);
  };

  const getProductsByDoor = (doorId: string): ProductEntry[] => {
    return products.filter(product => product.doorId === doorId);
  };

  const getHousesByAddress = (addressId: string): House[] => {
    return getHousesByAddressUtil(houses, addressId);
  };

  const getTodaysHouses = (): House[] => {
    return getTodaysHousesUtil(houses);
  };

  const getTodaysVisits = (): Visit[] => {
    return getTodaysVisitsUtil(visits);
  };

  const getAddressById = (id: string): Address | undefined => {
    return addresses.find(address => address.id === id);
  };

  const getHouseById = (id: string): House | undefined => {
    return houses.find(house => house.id === id);
  };

  const getVisit = (id: string): Visit | undefined => {
    return visits.find(visit => visit.id === id);
  };

  return {
    getVisitsByHouse,
    getDoorsByVisit,
    getProductsByDoor,
    getHousesByAddress,
    getTodaysHouses,
    getTodaysVisits,
    getAddressById,
    getHouseById,
    getVisit
  };
};
