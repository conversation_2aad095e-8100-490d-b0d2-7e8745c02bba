
import React from 'react';
import { useData } from '@/context/data';

interface MapLegendProps {
  currentLocation: { lat: number; lng: number } | null;
}

export const MapLegend: React.FC<MapLegendProps> = ({ currentLocation }) => {
  const { houses, visits } = useData();

  // Count completed and pending houses
  const completedHouses = houses.filter(house => {
    const houseVisits = visits.filter(v => v.houseId === house.id);
    return houseVisits.some(v => 
      v.status === 'Angetroffen → Sale' || 
      v.status === 'Angetroffen → Kein Interesse'
    );
  }).length;

  const pendingHouses = houses.length - completedHouses;

  return (
    <div className="absolute bottom-4 left-4 flex flex-col space-y-2 bg-white bg-opacity-95 p-3 rounded-lg shadow-lg border">
      <div className="text-xs font-semibold text-gray-700 mb-1"><PERSON>e</div>
      
      <div className="flex items-center space-x-2">
        <div className="w-3 h-3 rounded-full bg-green-500"></div>
        <span className="text-xs">Abgeschlossene Besuche ({completedHouses})</span>
      </div>
      
      <div className="flex items-center space-x-2">
        <div className="w-3 h-3 rounded-full bg-red-500"></div>
        <span className="text-xs">Offene Besuche ({pendingHouses})</span>
      </div>
      
      {currentLocation && (
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 rounded-full bg-blue-500 animate-pulse"></div>
          <span className="text-xs">Ihr Standort</span>
        </div>
      )}

      <div className="border-t pt-2 mt-2">
        <div className="text-xs text-gray-600">
          <div>📍 = Aktuelle Position</div>
          <div>🔴 = Live-Tracking</div>
        </div>
      </div>
    </div>
  );
};
