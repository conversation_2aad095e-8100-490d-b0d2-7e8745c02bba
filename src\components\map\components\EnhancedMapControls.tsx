
import React from 'react';
import { Button } from '@/components/ui/button';
import { Navigation, Locate, Route, Filter } from 'lucide-react';
import { cn } from '@/lib/utils';

interface EnhancedMapControlsProps {
  onLocationClick: () => void;
  onTrackingToggle: (enabled: boolean) => void;
  onRouteToNearest: () => void;
  onToggleFilter: () => void;
  isTracking: boolean;
  currentLocation: { lat: number; lng: number } | null;
  hasRoute: boolean;
  showFiltered: boolean;
}

export const EnhancedMapControls: React.FC<EnhancedMapControlsProps> = ({ 
  onLocationClick, 
  onTrackingToggle, 
  onRouteToNearest,
  onToggleFilter,
  isTracking,
  currentLocation,
  hasRoute,
  showFiltered
}) => {
  const handleTrackingToggle = () => {
    onTrackingToggle(!isTracking);
  };

  return (
    <div className="absolute top-4 left-4 z-10 flex flex-col gap-2">
      <Button
        onClick={onLocationClick}
        className={cn(
          "h-10 w-10 p-0 rounded-full border shadow-md",
          currentLocation 
            ? "bg-blue-500 hover:bg-blue-600 text-white" 
            : "bg-white hover:bg-gray-100 text-gray-800"
        )}
        variant="outline"
        title="Aktuelle Position anzeigen"
      >
        <Navigation className="h-4 w-4" />
      </Button>

      <Button
        onClick={handleTrackingToggle}
        className={cn(
          "h-10 w-10 p-0 rounded-full border shadow-md",
          isTracking 
            ? "bg-green-500 hover:bg-green-600 text-white" 
            : "bg-white hover:bg-gray-100 text-gray-800"
        )}
        variant="outline"
        title={isTracking ? "Tracking deaktivieren" : "Tracking aktivieren"}
      >
        <Locate className="h-4 w-4" />
      </Button>

      <Button
        onClick={onRouteToNearest}
        disabled={!currentLocation}
        className={cn(
          "h-10 w-10 p-0 rounded-full border shadow-md",
          hasRoute
            ? "bg-purple-500 hover:bg-purple-600 text-white" 
            : "bg-white hover:bg-gray-100 text-gray-800"
        )}
        variant="outline"
        title="Route zum nächsten Besuch"
      >
        <Route className="h-4 w-4" />
      </Button>

      <Button
        onClick={onToggleFilter}
        className={cn(
          "h-10 w-10 p-0 rounded-full border shadow-md",
          showFiltered
            ? "bg-orange-500 hover:bg-orange-600 text-white" 
            : "bg-white hover:bg-gray-100 text-gray-800"
        )}
        variant="outline"
        title="Nur offene Besuche anzeigen"
      >
        <Filter className="h-4 w-4" />
      </Button>
    </div>
  );
};
