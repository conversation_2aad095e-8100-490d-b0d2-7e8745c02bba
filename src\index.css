
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 0 72% 51%;  /* Red color */
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 0 72% 51%;  /* Red color for focus rings */

    --radius: 0.75rem;

    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 0 72% 51%;  /* Red color */
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 0 72% 51%;  /* Red color */
  }

  .light {
    --background: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 0 72% 51%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 0 72% 51%;

    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 0 72% 51%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 0 72% 51%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 0 72% 51%;  /* Red color */
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 0 72% 51%;  /* Red color */
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 0 72% 51%;  /* Red color */
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    /* Samsung S25 Ultra optimizations */
    touch-action: manipulation;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: none;
    transition: background-color 0.3s ease, color 0.3s ease;
  }
  
  /* Mobile optimizations for Samsung S25 Ultra */
  html, body {
    @apply touch-manipulation overscroll-none;
    scrollbar-width: none; /* Firefox */
    /* Optimized for 6.8" display */
    font-size: 16px;
  }
  
  html::-webkit-scrollbar, body::-webkit-scrollbar {
    display: none; /* Chrome, Safari, newer versions of Opera */
  }
}

/* Samsung S25 Ultra specific optimizations */
@media (min-width: 400px) and (max-width: 500px) and (min-height: 800px) {
  /* Large mobile display optimizations */
  .responsive-container {
    @apply px-6 py-8;
  }
  
  .responsive-card {
    @apply rounded-3xl shadow-2xl p-8;
  }
  
  .responsive-text {
    @apply text-lg;
  }
  
  .responsive-heading {
    @apply text-3xl font-bold;
  }
  
  /* Touch targets optimized for S25 Ultra */
  button, input, select, textarea, [role="button"] {
    min-height: 56px;
    min-width: 56px;
  }
  
  /* Tab buttons larger on big mobile screens */
  [data-state="active"], [data-state="inactive"] {
    @apply py-5 px-8 text-xl;
  }
}

/* Improved scrollbar hiding for all elements */
.scrollbar-none {
  -ms-overflow-style: none; /* Internet Explorer and Edge */
  scrollbar-width: none; /* Firefox */
  scroll-behavior: smooth;
}

.scrollbar-none::-webkit-scrollbar {
  width: 0;
  height: 0;
  display: none; /* Safari and Chrome */
}

/* Enhanced Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideFromRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideFromLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Enhanced Animation utility classes */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.4s ease-out;
}

.animate-slide-from-right {
  animation: slideFromRight 0.3s ease-out;
}

.animate-slide-from-left {
  animation: slideFromLeft 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

.animate-pulse-slow {
  animation: pulse 4s infinite;
}

.animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.hover-scale {
  transition: transform 0.2s ease-out;
}

.hover-scale:hover {
  transform: scale(1.03);
}

.hover-lift {
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.fade-in-section {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in-section.is-visible {
  opacity: 1;
  transform: translateY(0);
}

/* Glassmorphism effects */
.glass-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.glass-card-dark {
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Enhanced touch and haptic feedback */
.touch-feedback {
  transition: all 0.1s ease;
}

.touch-feedback:active {
  transform: scale(0.98);
  opacity: 0.8;
}

/* Improved status colors for visits */
.status-na {
  @apply bg-gray-100 text-gray-700 border-gray-300;
}

.status-termin {
  @apply bg-yellow-100 text-yellow-800 border-yellow-300;
}

.status-kein-interesse {
  @apply bg-red-100 text-red-800 border-red-300;
}

.status-sale {
  @apply bg-green-100 text-green-800 border-green-300;
}

/* Enhanced input focus effects */
.input-focus-glow:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1),
              0 0 20px rgba(239, 68, 68, 0.2);
}

/* Better loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Progressive enhancement for better performance */
@media (prefers-reduced-motion: reduce) {
  .animate-fade-in,
  .animate-slide-in,
  .animate-scale-in,
  .hover-scale,
  .hover-lift {
    animation: none;
    transition: none;
  }
}

/* Enhanced mobile interactions */
@media (max-width: 500px) {
  /* Larger touch targets for mobile */
  .mobile-touch {
    min-height: 48px;
    min-width: 48px;
  }
  
  /* Better spacing for mobile */
  .mobile-spacing {
    @apply space-y-6;
  }
  
  /* Optimized typography for mobile */
  .mobile-text {
    font-size: clamp(16px, 4vw, 18px);
    line-height: 1.5;
  }
  
  /* Better mobile forms */
  .mobile-input {
    @apply h-14 text-lg rounded-xl;
  }
  
  /* Enhanced mobile buttons */
  .mobile-button {
    @apply h-14 text-lg font-semibold rounded-xl;
  }
}

/* Modern focus indicators for accessibility */
*:focus-visible {
  outline: 2px solid #ef4444;
  outline-offset: 2px;
}

/* Remove default focus for mouse users */
*:focus:not(:focus-visible) {
  outline: none;
}

/* Dark mode specific adjustments */
.dark .bg-gradient-to-br {
  background: linear-gradient(to bottom right, hsl(var(--muted)), hsl(var(--background)), hsl(var(--muted)));
}

.dark .from-red-50 {
  --tw-gradient-from: hsl(var(--muted));
}

.dark .via-white {
  --tw-gradient-via: hsl(var(--background));
}

.dark .to-red-50 {
  --tw-gradient-to: hsl(var(--muted));
}

/* Theme transition for all elements */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}
