
import { majorCitiesStreets } from './majorCities';
import { mediumCitiesStreets } from './mediumCities';
import { smallCitiesStreets } from './smallCities';

// Default streets for cities that don't have specific streets in our database
export const defaultStreets = [
  "Hauptstraße",
  "Bahnhofstraße",
  "Kirchstraße",
  "Schulstraße",
  "Gartenstraße",
  "Bergstraße",
  "Waldstraße",
  "Dorfstraße",
  "Lindenstraße",
  "Ringstraße",
  "Bachstraße",
  "Talstraße",
  "Wiesenweg",
  "Birkenweg",
  "Buchenweg",
  "Eichenweg",
  "Tannenweg",
  "Ahornweg",
  "Erlenweg",
  "Kastanienweg",
  "Feldweg",
  "Am Bach",
  "Am Berg",
  "Mühlenweg",
  "Rosenstraße",
  "Tulpenstraße",
  "Nelkenweg",
  "Lilienweg",
  "Sonnenstraße",
  "Mondstraße"
];

// Combine all street databases
export const streetsByCity: Record<string, string[]> = {
  ...majorCitiesStreets,
  ...mediumCitiesStreets,
  ...smallCitiesStreets
};

// Function to get streets for a city
export const getStreetsByCity = (city: string): string[] => {
  return streetsByCity[city] || defaultStreets;
};

// Function to get street suggestions for a given city and query
export const getStreetSuggestions = (city: string, query: string): string[] => {
  const streets = getStreetsByCity(city);
  if (!query) return streets.slice(0, 15); // Show more suggestions
  
  return streets
    .filter(street => street.toLowerCase().includes(query.toLowerCase()))
    .slice(0, 15); // Show more suggestions
};
