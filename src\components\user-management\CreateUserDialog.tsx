
import React from 'react';
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { User, UserRole } from '@/types';
import { Plus, UserPlus } from 'lucide-react';

interface Team {
  id: string;
  name: string;
}

interface NewUserData {
  name: string;
  email: string;
  password: string;
  role: UserRole;
  teamId: string;
  mentorId: string;
}

interface CreateUserDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  newUser: NewUserData;
  onUpdateNewUser: (user: NewUserData) => void;
  teams: Team[];
  mentors: User[];
  onCreate: () => void;
}

export const CreateUserDialog: React.FC<CreateUserDialogProps> = ({
  isOpen,
  onOpenChange,
  newUser,
  onUpdateNewUser,
  teams,
  mentors,
  onCreate,
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>
        <Button className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300">
          <Plus className="h-4 w-4 mr-2" />
          Neuen Benutzer anlegen
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5 text-blue-600" />
            Neuen Benutzer erstellen
          </DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              value={newUser.name}
              onChange={(e) => onUpdateNewUser({ ...newUser, name: e.target.value })}
              placeholder="Name des Benutzers"
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="email">E-Mail</Label>
            <Input
              id="email"
              type="email"
              value={newUser.email}
              onChange={(e) => onUpdateNewUser({ ...newUser, email: e.target.value })}
              placeholder="<EMAIL>"
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="password">Passwort</Label>
            <Input
              id="password"
              type="password"
              value={newUser.password}
              onChange={(e) => onUpdateNewUser({ ...newUser, password: e.target.value })}
              placeholder="Passwort"
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="role">Rolle</Label>
            <Select
              value={newUser.role}
              onValueChange={(value: UserRole) => onUpdateNewUser({ ...newUser, role: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Rolle auswählen" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="berater">Berater</SelectItem>
                <SelectItem value="mentor">Mentor</SelectItem>
                <SelectItem value="teamleiter">Teamleiter</SelectItem>
                <SelectItem value="gebietsmanager">Gebietsmanager</SelectItem>
                <SelectItem value="admin">Administrator</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-2">
            <Label htmlFor="team">Team</Label>
            <Select
              value={newUser.teamId || ''}
              onValueChange={(value) => onUpdateNewUser({ ...newUser, teamId: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Team auswählen" />
              </SelectTrigger>
              <SelectContent>
                {teams.map((team) => (
                  <SelectItem key={team.id} value={team.id || ''}>
                    {team.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          {newUser.role === 'berater' && (
            <div className="grid gap-2">
              <Label htmlFor="mentor">Mentor</Label>
              <Select
                value={newUser.mentorId || ''}
                onValueChange={(value) => onUpdateNewUser({ ...newUser, mentorId: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Mentor auswählen" />
                </SelectTrigger>
                <SelectContent>
                  {mentors.map((mentor) => (
                    <SelectItem key={mentor.id} value={mentor.id}>
                      {mentor.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </div>
        <Button onClick={onCreate}>Benutzer erstellen</Button>
      </DialogContent>
    </Dialog>
  );
};
