import React from 'react';
import { Check, X } from 'lucide-react';
import { PasswordValidation } from '@/context/auth/types';
import { cn } from '@/lib/utils';

interface PasswordStrengthIndicatorProps {
  validation: PasswordValidation;
  className?: string;
}

const PasswordStrengthIndicator: React.FC<PasswordStrengthIndicatorProps> = ({
  validation,
  className,
}) => {
  const { strength, requirements, suggestions } = validation;

  // Enhanced color mapping for strength levels
  const strengthColors = {
    weak: 'bg-red-500',
    fair: 'bg-orange-500',
    good: 'bg-yellow-500',
    strong: 'bg-green-500',
    excellent: 'bg-emerald-500',
  };

  const strengthLabels = {
    weak: 'Schwach',
    fair: 'Ausreichend',
    good: 'Gut',
    strong: 'Stark',
    excellent: 'Exzellent',
  };

  const strengthTextColors = {
    weak: 'text-red-600',
    fair: 'text-orange-600',
    good: 'text-yellow-600',
    strong: 'text-green-600',
    excellent: 'text-emerald-600',
  };

  // Calculate progress percentage (enhanced scoring)
  const progressPercentage = (validation.score / 7) * 100;

  return (
    <div className={cn('space-y-3', className)}>
      {/* Strength indicator bar */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">
            Passwort-Stärke
          </span>
          <span className={cn('text-sm font-semibold', strengthTextColors[strength])}>
            {strengthLabels[strength]}
          </span>
        </div>
        
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={cn(
              'h-2 rounded-full transition-all duration-300 ease-in-out',
              strengthColors[strength]
            )}
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
      </div>

      {/* Requirements checklist */}
      <div className="space-y-2">
        <h4 className="text-sm font-medium text-gray-700">Anforderungen:</h4>
        <div className="grid grid-cols-1 gap-1">
          <RequirementItem
            met={requirements.minLength}
            text="Mindestens 8 Zeichen"
          />
          <RequirementItem
            met={requirements.hasUppercase}
            text="Großbuchstaben (A-Z)"
          />
          <RequirementItem
            met={requirements.hasLowercase}
            text="Kleinbuchstaben (a-z)"
          />
          <RequirementItem
            met={requirements.hasNumbers}
            text="Zahlen (0-9)"
          />
          <RequirementItem
            met={requirements.hasSpecialChars}
            text="Sonderzeichen (!@#$%^&*)"
          />
          <RequirementItem
            met={requirements.noCommonPatterns}
            text="Keine häufigen Muster"
          />
          <RequirementItem
            met={requirements.noPersonalInfo}
            text="Keine persönlichen Daten"
          />
        </div>
      </div>

      {/* Entropy information for advanced users */}
      {validation.entropy > 0 && (
        <div className="text-xs text-gray-500 mt-2">
          Entropie: {Math.round(validation.entropy)} Bits
        </div>
      )}

      {/* Suggestions */}
      {suggestions.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">Verbesserungen:</h4>
          <ul className="space-y-1">
            {suggestions.map((suggestion, index) => (
              <li
                key={index}
                className="text-sm text-gray-600 flex items-center"
              >
                <span className="w-1.5 h-1.5 bg-gray-400 rounded-full mr-2 flex-shrink-0" />
                {suggestion}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

interface RequirementItemProps {
  met: boolean;
  text: string;
}

const RequirementItem: React.FC<RequirementItemProps> = ({ met, text }) => {
  return (
    <div className="flex items-center space-x-2">
      <div
        className={cn(
          'flex items-center justify-center w-4 h-4 rounded-full transition-colors duration-200',
          met ? 'bg-green-500' : 'bg-gray-300'
        )}
      >
        {met ? (
          <Check className="w-2.5 h-2.5 text-white" />
        ) : (
          <X className="w-2.5 h-2.5 text-gray-500" />
        )}
      </div>
      <span
        className={cn(
          'text-sm transition-colors duration-200',
          met ? 'text-green-600' : 'text-gray-600'
        )}
      >
        {text}
      </span>
    </div>
  );
};

export default PasswordStrengthIndicator;
