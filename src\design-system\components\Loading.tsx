import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react';

// Loading variants for consistent styling
const loadingVariants = cva(
  'flex items-center justify-center',
  {
    variants: {
      variant: {
        default: 'text-blue-600',
        light: 'text-neutral-400',
        dark: 'text-neutral-700',
        success: 'text-green-600',
        warning: 'text-yellow-600',
        error: 'text-red-600',
      },
      size: {
        sm: 'h-4 w-4',
        md: 'h-6 w-6',
        lg: 'h-8 w-8',
        xl: 'h-12 w-12',
      }
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
    },
  }
);

export interface LoadingSpinnerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof loadingVariants> {
  text?: string;
}

const LoadingSpinner = React.forwardRef<HTMLDivElement, LoadingSpinnerProps>(
  ({ className, variant, size, text, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('flex flex-col items-center justify-center space-y-2', className)}
      {...props}
    >
      <Loader2 className={cn(loadingVariants({ variant, size }), 'animate-spin')} />
      {text && (
        <p className={cn(
          'text-sm font-medium',
          variant === 'light' ? 'text-neutral-400' : 
          variant === 'dark' ? 'text-neutral-700' :
          'text-neutral-600'
        )}>
          {text}
        </p>
      )}
    </div>
  )
);

LoadingSpinner.displayName = 'LoadingSpinner';

// Page-level loading component
export const PageLoader: React.FC<{ text?: string }> = ({ text = 'Lädt...' }) => (
  <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
    <div className="text-center">
      <LoadingSpinner size="xl" text={text} />
    </div>
  </div>
);

// Inline loading component
export const InlineLoader: React.FC<{ text?: string; size?: 'sm' | 'md' | 'lg' }> = ({ 
  text = 'Lädt...', 
  size = 'md' 
}) => (
  <div className="flex items-center justify-center p-4">
    <LoadingSpinner size={size} text={text} />
  </div>
);

// Button loading state
export const ButtonLoader: React.FC<{ size?: 'sm' | 'md' | 'lg' }> = ({ size = 'sm' }) => (
  <Loader2 className={cn(loadingVariants({ size }), 'animate-spin')} />
);

// Skeleton loading components
export const SkeletonCard: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn('animate-pulse', className)}>
    <div className="bg-neutral-200 rounded-2xl h-32 w-full"></div>
  </div>
);

export const SkeletonText: React.FC<{ 
  lines?: number; 
  className?: string;
}> = ({ lines = 3, className }) => (
  <div className={cn('animate-pulse space-y-2', className)}>
    {Array.from({ length: lines }, (_, i) => (
      <div 
        key={i}
        className={cn(
          'bg-neutral-200 rounded h-4',
          i === lines - 1 ? 'w-3/4' : 'w-full'
        )}
      />
    ))}
  </div>
);

// Loading overlay for forms
export const LoadingOverlay: React.FC<{ 
  isLoading: boolean; 
  text?: string;
  children: React.ReactNode;
}> = ({ isLoading, text = 'Speichert...', children }) => (
  <div className="relative">
    {children}
    {isLoading && (
      <div className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center rounded-2xl">
        <LoadingSpinner text={text} />
      </div>
    )}
  </div>
);

export { LoadingSpinner, loadingVariants };
export type { LoadingSpinnerProps };
