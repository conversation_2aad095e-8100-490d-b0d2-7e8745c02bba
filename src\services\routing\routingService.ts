
import { toast } from 'sonner';

export interface RouteCoordinate {
  longitude: number;
  latitude: number;
}

export interface RouteResponse {
  distance: number; // in meters
  duration: number; // in seconds
  geometry: RouteCoordinate[];
}

export class RoutingService {
  private accessToken: string;

  constructor(accessToken: string) {
    this.accessToken = accessToken;
  }

  async getRoute(start: RouteCoordinate, end: RouteCoordinate): Promise<RouteResponse | null> {
    try {
      const response = await fetch(
        `https://api.mapbox.com/directions/v5/mapbox/driving/${start.longitude},${start.latitude};${end.longitude},${end.latitude}?access_token=${this.accessToken}&geometries=geojson&overview=full`
      );
      
      if (!response.ok) {
        throw new Error('Routing request failed');
      }

      const data = await response.json();
      
      if (data.routes && data.routes.length > 0) {
        const route = data.routes[0];
        return {
          distance: route.distance,
          duration: route.duration,
          geometry: route.geometry.coordinates.map((coord: number[]) => ({
            longitude: coord[0],
            latitude: coord[1]
          }))
        };
      }
      
      return null;
    } catch (error) {
      console.error('Routing error:', error);
      return null;
    }
  }

  formatDistance(meters: number): string {
    if (meters < 1000) {
      return `${Math.round(meters)} m`;
    }
    return `${(meters / 1000).toFixed(1)} km`;
  }

  formatDuration(seconds: number): string {
    const minutes = Math.round(seconds / 60);
    if (minutes < 60) {
      return `${minutes} min`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}min`;
  }
}
