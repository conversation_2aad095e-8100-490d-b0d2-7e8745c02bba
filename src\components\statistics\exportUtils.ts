
import { Visit, Door, House, Address } from '@/types';

export interface ExportVisitData {
  street: string;
  houseNumber: string;
  city: string;
  zipCode: string;
  status: string;
  visitDate: string;
  appointmentDate?: string;
  appointmentTime?: string;
  doorName?: string;
  type: 'EFH' | 'MFH';
}

export interface ExportData {
  period: string;
  exportDate: string;
  dateRange?: {
    from: string;
    to: string;
  };
  totalVisits: number;
  visits: ExportVisitData[];
}

const mapStatus = (status: string): string => {
  switch (status) {
    case 'N/A':
      return 'N/A';
    case 'Angetroffen → Termin':
      return 'Termin';
    case 'Angetroffen → Kein Interesse':
      return 'Kein Interesse';
    case 'Angetroffen → Sale':
      return 'Sale';
    default:
      return status;
  }
};

const isDateInRange = (dateStr: string, from?: Date, to?: Date): boolean => {
  const date = new Date(dateStr);
  date.setHours(0, 0, 0, 0);
  
  if (from) {
    const fromDate = new Date(from);
    fromDate.setHours(0, 0, 0, 0);
    if (date < fromDate) return false;
  }
  
  if (to) {
    const toDate = new Date(to);
    toDate.setHours(23, 59, 59, 999);
    if (date > toDate) return false;
  }
  
  return true;
};

const getDateRangeForPeriod = (period: string): { from: Date; to: Date } => {
  const today = new Date();
  const from = new Date();
  const to = new Date();
  
  switch (period) {
    case 'day':
      from.setHours(0, 0, 0, 0);
      to.setHours(23, 59, 59, 999);
      break;
    case 'week':
      from.setDate(today.getDate() - 7);
      from.setHours(0, 0, 0, 0);
      to.setHours(23, 59, 59, 999);
      break;
    case 'month':
      from.setDate(today.getDate() - 30);
      from.setHours(0, 0, 0, 0);
      to.setHours(23, 59, 59, 999);
      break;
    default:
      return { from: today, to: today };
  }
  
  return { from, to };
};

export const generateExportData = (
  period: string,
  visits: Visit[],
  doors: Door[],
  houses: House[],
  addresses: Address[],
  customDateRange?: { from: Date | undefined; to: Date | undefined }
): ExportData => {
  let fromDate: Date | undefined;
  let toDate: Date | undefined;
  
  if (period === 'custom' && customDateRange) {
    fromDate = customDateRange.from;
    toDate = customDateRange.to;
  } else if (period !== 'custom') {
    const range = getDateRangeForPeriod(period);
    fromDate = range.from;
    toDate = range.to;
  }
  
  const exportVisits: ExportVisitData[] = [];
  
  // Process EFH visits
  visits.forEach(visit => {
    if (!isDateInRange(visit.timestamp, fromDate, toDate)) return;
    
    const house = houses.find(h => h.id === visit.houseId);
    if (!house) return;
    
    const address = addresses.find(a => a.id === house.addressId);
    if (!address) return;
    
    exportVisits.push({
      street: address.street,
      houseNumber: house.houseNumber,
      city: address.city,
      zipCode: address.zipCode,
      status: mapStatus(visit.status),
      visitDate: visit.timestamp.split('T')[0],
      appointmentDate: visit.appointmentDate,
      appointmentTime: visit.appointmentTime,
      type: 'EFH'
    });
  });
  
  // Process MFH doors
  doors.forEach(door => {
    const visit = visits.find(v => v.id === door.visitId);
    if (!visit || !isDateInRange(visit.timestamp, fromDate, toDate)) return;
    
    const house = houses.find(h => h.id === visit.houseId);
    if (!house) return;
    
    const address = addresses.find(a => a.id === house.addressId);
    if (!address) return;
    
    exportVisits.push({
      street: address.street,
      houseNumber: house.houseNumber,
      city: address.city,
      zipCode: address.zipCode,
      status: mapStatus(door.status),
      visitDate: visit.timestamp.split('T')[0],
      appointmentDate: door.appointmentDate,
      appointmentTime: door.appointmentTime,
      doorName: door.name,
      type: 'MFH'
    });
  });
  
  // Sort by visit date
  exportVisits.sort((a, b) => new Date(b.visitDate).getTime() - new Date(a.visitDate).getTime());
  
  const exportData: ExportData = {
    period: period === 'custom' ? 'Benutzerdefiniert' : period,
    exportDate: new Date().toISOString().split('T')[0],
    totalVisits: exportVisits.length,
    visits: exportVisits
  };
  
  if (period === 'custom' && fromDate && toDate) {
    exportData.dateRange = {
      from: fromDate.toISOString().split('T')[0],
      to: toDate.toISOString().split('T')[0]
    };
  }
  
  return exportData;
};

export const downloadJSON = (data: ExportData): void => {
  const jsonString = JSON.stringify(data, null, 2);
  const blob = new Blob([jsonString], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  
  const filename = data.period === 'Benutzerdefiniert' && data.dateRange
    ? `visits_export_${data.dateRange.from}_to_${data.dateRange.to}.json`
    : `visits_export_${data.exportDate}_${data.period}.json`;
  
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};
