
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Calendar, CalendarRange } from 'lucide-react';
import { DateRangePicker } from './DateRangePicker';
import { StatisticsContent } from './StatisticsContent';

interface StatisticsTabsProps {
  period: 'day' | 'week' | 'month' | 'custom';
  onPeriodChange: (period: 'day' | 'week' | 'month' | 'custom') => void;
  customDateRange: {
    from: Date | undefined;
    to: Date | undefined;
  };
  onCustomDateRangeChange: (range: { from: Date | undefined; to: Date | undefined }) => void;
  totalDoors: number;
  salesCount: number;
  statusData: Array<{ name: string; count: number }>;
  productData: Array<{ name: string; count: number }>;
  isMobile: boolean;
}

export const StatisticsTabs: React.FC<StatisticsTabsProps> = ({
  period,
  onPeriodChange,
  customDateRange,
  onCustomDateRangeChange,
  totalDoors,
  salesCount,
  statusData,
  productData,
  isMobile
}) => {
  return (
    <Card className="glass-card rounded-3xl border-0 shadow-2xl">
      <CardContent className="p-6">
        <Tabs defaultValue="day" onValueChange={(v) => onPeriodChange(v as any)}>
          <TabsList className={`grid w-full grid-cols-4 ${isMobile ? 'h-16' : 'h-12'} bg-gray-100 rounded-2xl p-2`}>
            <TabsTrigger 
              value="day" 
              className={`${isMobile ? 'h-12 text-sm' : 'h-8 text-sm'} rounded-xl font-semibold transition-all duration-200 data-[state=active]:bg-white data-[state=active]:shadow-lg flex items-center gap-2`}
            >
              <Calendar className="h-4 w-4" />
              {!isMobile && <span>Tag</span>}
            </TabsTrigger>
            <TabsTrigger 
              value="week" 
              className={`${isMobile ? 'h-12 text-sm' : 'h-8 text-sm'} rounded-xl font-semibold transition-all duration-200 data-[state=active]:bg-white data-[state=active]:shadow-lg flex items-center gap-2`}
            >
              <CalendarRange className="h-4 w-4" />
              {!isMobile && <span>Woche</span>}
            </TabsTrigger>
            <TabsTrigger 
              value="month" 
              className={`${isMobile ? 'h-12 text-sm' : 'h-8 text-sm'} rounded-xl font-semibold transition-all duration-200 data-[state=active]:bg-white data-[state=active]:shadow-lg flex items-center gap-2`}
            >
              <Calendar className="h-4 w-4" />
              {!isMobile && <span>Monat</span>}
            </TabsTrigger>
            <TabsTrigger 
              value="custom" 
              className={`${isMobile ? 'h-12 text-xs' : 'h-8 text-xs'} rounded-xl font-semibold transition-all duration-200 data-[state=active]:bg-white data-[state=active]:shadow-lg flex items-center gap-1`}
            >
              <CalendarRange className="h-4 w-4" />
              {!isMobile && <span>Custom</span>}
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="day" className="mt-6">
            <StatisticsContent
              totalDoors={totalDoors}
              salesCount={salesCount}
              statusData={statusData}
              productData={productData}
              isMobile={isMobile}
            />
          </TabsContent>

          <TabsContent value="week" className="mt-6">
            <StatisticsContent
              totalDoors={totalDoors}
              salesCount={salesCount}
              statusData={statusData}
              productData={productData}
              isMobile={isMobile}
            />
          </TabsContent>

          <TabsContent value="month" className="mt-6">
            <StatisticsContent
              totalDoors={totalDoors}
              salesCount={salesCount}
              statusData={statusData}
              productData={productData}
              isMobile={isMobile}
            />
          </TabsContent>

          <TabsContent value="custom" className="mt-6">
            <div className="space-y-6">
              <Card className="glass-card hover-lift rounded-3xl border-0 shadow-2xl">
                <CardHeader className="pb-4">
                  <CardTitle className="text-xl font-bold text-gray-800">
                    Datumsbereich auswählen
                  </CardTitle>
                </CardHeader>
                <CardContent className="pb-6">
                  <DateRangePicker
                    dateRange={customDateRange}
                    onDateRangeChange={onCustomDateRangeChange}
                    isMobile={isMobile}
                  />
                </CardContent>
              </Card>
              
              {customDateRange.from && customDateRange.to && (
                <StatisticsContent
                  totalDoors={totalDoors}
                  salesCount={salesCount}
                  statusData={statusData}
                  productData={productData}
                  isMobile={isMobile}
                />
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
