import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

// Card variants for consistent styling
const cardVariants = cva(
  'rounded-2xl border transition-all duration-250',
  {
    variants: {
      variant: {
        default: 'bg-white border-neutral-200 shadow-md hover:shadow-lg',
        elevated: 'bg-white border-neutral-200 shadow-lg hover:shadow-xl',
        outlined: 'bg-white border-2 border-neutral-300 shadow-sm hover:shadow-md',
        glass: 'bg-white/95 backdrop-blur-sm border-white/20 shadow-xl',
        success: 'bg-green-50 border-green-200 shadow-md',
        warning: 'bg-yellow-50 border-yellow-200 shadow-md',
        error: 'bg-red-50 border-red-200 shadow-md',
        info: 'bg-blue-50 border-blue-200 shadow-md',
      },
      padding: {
        none: 'p-0',
        sm: 'p-4',
        md: 'p-6',
        lg: 'p-8',
      },
      interactive: {
        true: 'cursor-pointer hover:scale-[1.02] active:scale-[0.98]',
        false: '',
      }
    },
    defaultVariants: {
      variant: 'default',
      padding: 'md',
      interactive: false,
    },
  }
);

export interface CardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {
  children: React.ReactNode;
}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant, padding, interactive, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(cardVariants({ variant, padding, interactive, className }))}
      {...props}
    >
      {children}
    </div>
  )
);

Card.displayName = 'Card';

// Card Header Component
export interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

const CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(
  ({ className, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('flex flex-col space-y-1.5 pb-4', className)}
      {...props}
    >
      {children}
    </div>
  )
);

CardHeader.displayName = 'CardHeader';

// Card Title Component
export interface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {
  children: React.ReactNode;
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
}

const CardTitle = React.forwardRef<HTMLHeadingElement, CardTitleProps>(
  ({ className, children, as: Component = 'h3', ...props }, ref) => (
    <Component
      ref={ref}
      className={cn('text-xl font-semibold leading-none tracking-tight text-neutral-900', className)}
      {...props}
    >
      {children}
    </Component>
  )
);

CardTitle.displayName = 'CardTitle';

// Card Description Component
export interface CardDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {
  children: React.ReactNode;
}

const CardDescription = React.forwardRef<HTMLParagraphElement, CardDescriptionProps>(
  ({ className, children, ...props }, ref) => (
    <p
      ref={ref}
      className={cn('text-base text-neutral-600', className)}
      {...props}
    >
      {children}
    </p>
  )
);

CardDescription.displayName = 'CardDescription';

// Card Content Component
export interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

const CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(
  ({ className, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('pt-0', className)}
      {...props}
    >
      {children}
    </div>
  )
);

CardContent.displayName = 'CardContent';

// Card Footer Component
export interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

const CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(
  ({ className, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('flex items-center pt-4', className)}
      {...props}
    >
      {children}
    </div>
  )
);

CardFooter.displayName = 'CardFooter';

// Specialized Visit Card Component
export interface VisitCardProps {
  title: string;
  subtitle?: string;
  status?: 'pending' | 'completed' | 'error';
  icon?: React.ReactNode;
  children?: React.ReactNode;
  onClick?: () => void;
  className?: string;
}

export const VisitCard: React.FC<VisitCardProps> = ({
  title,
  subtitle,
  status = 'pending',
  icon,
  children,
  onClick,
  className
}) => {
  const statusVariants = {
    pending: 'info',
    completed: 'success',
    error: 'error',
  } as const;

  return (
    <Card
      variant={statusVariants[status]}
      interactive={!!onClick}
      onClick={onClick}
      className={cn('relative overflow-hidden', className)}
    >
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            {icon && (
              <div className="flex-shrink-0 text-2xl">
                {icon}
              </div>
            )}
            <div>
              <CardTitle className="text-lg">{title}</CardTitle>
              {subtitle && (
                <CardDescription className="text-sm mt-1">
                  {subtitle}
                </CardDescription>
              )}
            </div>
          </div>
          {status === 'completed' && (
            <div className="flex-shrink-0 text-green-600">
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
          )}
        </div>
      </CardHeader>
      {children && (
        <CardContent>
          {children}
        </CardContent>
      )}
    </Card>
  );
};

// Address Card Component for Visit Tracking
export interface AddressCardProps {
  street: string;
  houseNumber: string;
  city: string;
  zipCode: string;
  houseType: 'EFH' | 'MFH';
  step?: number;
  totalSteps?: number;
  className?: string;
}

export const AddressCard: React.FC<AddressCardProps> = ({
  street,
  houseNumber,
  city,
  zipCode,
  houseType,
  step,
  totalSteps,
  className
}) => {
  return (
    <Card variant="glass" className={cn('mb-6', className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">
              {street} {houseNumber}
            </CardTitle>
            <CardDescription>
              {zipCode} {city} • {houseType}
            </CardDescription>
          </div>
          {step && totalSteps && (
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-neutral-600">
                Schritt {step} von {totalSteps}
              </span>
              <div className="flex space-x-1">
                {Array.from({ length: totalSteps }, (_, i) => (
                  <div
                    key={i}
                    className={cn(
                      'w-2 h-2 rounded-full',
                      i < step ? 'bg-blue-600' : 'bg-neutral-300'
                    )}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      </CardHeader>
    </Card>
  );
};

export { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter, cardVariants };
export type { CardProps };
