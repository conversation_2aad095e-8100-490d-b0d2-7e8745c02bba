
import React from 'react';
import { Info } from 'lucide-react';
import { Door } from '@/types';

interface InfoBannersProps {
  isMultiDoor: boolean;
  salesDoors: Door[];
  selectedDoors: string[];
  canSave: boolean;
  isSubmitting: boolean;
}

export const InfoBanners: React.FC<InfoBannersProps> = ({
  isMultiDoor,
  salesDoors,
  selectedDoors,
  canSave,
  isSubmitting
}) => {
  return (
    <>
      {/* No sales doors banner */}
      {salesDoors.length === 0 && (
        <div className="mb-4 p-3 bg-red-50/80 rounded-xl border border-red-200/50">
          <div className="flex items-start gap-3">
            <Info className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-sm font-medium text-red-800 mb-1">
                Keine Sales-Türen verfügbar
              </p>
              <p className="text-xs text-red-700">
                Es wurden keine Türen mit Status "Angetroffen → Sale" gefunden.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Multi-door instruction banner */}
      {isMultiDoor && salesDoors.length > 1 && (
        <div className="mb-4 p-3 bg-blue-50/80 rounded-xl border border-blue-200/50">
          <div className="flex items-start gap-3">
            <Info className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-sm font-medium text-blue-800 mb-1">
                Mehrere Türen verfügbar
              </p>
              <p className="text-xs text-blue-700">
                Wählen Sie eine oder mehrere Türen aus und fügen Sie Produkte hinzu.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Save validation banner */}
      {!canSave && selectedDoors.length === 0 && !isSubmitting && (
        <div className="mb-3 p-2 bg-orange-50/80 rounded-lg border border-orange-200/50">
          <p className="text-xs text-orange-700">
            Wählen Sie mindestens eine Tür aus um zu speichern.
          </p>
        </div>
      )}
    </>
  );
};
