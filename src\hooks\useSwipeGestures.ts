import { useEffect, useRef, useState, useCallback } from 'react';

interface SwipeGestureOptions {
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  threshold?: number; // Minimum distance for swipe
  preventScroll?: boolean;
}

interface TouchPosition {
  x: number;
  y: number;
  time: number;
}

export const useSwipeGestures = (options: SwipeGestureOptions) => {
  const {
    onSwipeLeft,
    onSwipeRight,
    onSwipeUp,
    onSwipeDown,
    threshold = 50,
    preventScroll = false
  } = options;

  const touchStart = useRef<TouchPosition | null>(null);
  const touchEnd = useRef<TouchPosition | null>(null);
  const [isSwipeActive, setIsSwipeActive] = useState(false);

  const handleTouchStart = (e: TouchEvent) => {
    const touch = e.touches[0];
    if (touch) {
      touchStart.current = {
        x: touch.clientX,
        y: touch.clientY,
        time: Date.now()
      };
      touchEnd.current = null;
      setIsSwipeActive(true);
    }
  };

  const handleTouchMove = (e: TouchEvent) => {
    if (!touchStart.current) return;

    const touch = e.touches[0];
    if (touch) {
      touchEnd.current = {
        x: touch.clientX,
        y: touch.clientY,
        time: Date.now()
      };

      // Prevent scroll if needed
      if (preventScroll) {
        const deltaX = Math.abs(touch.clientX - touchStart.current.x);
        const deltaY = Math.abs(touch.clientY - touchStart.current.y);
        
        // If horizontal swipe is more prominent, prevent vertical scroll
        if (deltaX > deltaY && deltaX > 10) {
          e.preventDefault();
        }
      }
    }
  };

  const handleTouchEnd = () => {
    if (!touchStart.current || !touchEnd.current) {
      setIsSwipeActive(false);
      return;
    }

    const deltaX = touchEnd.current.x - touchStart.current.x;
    const deltaY = touchEnd.current.y - touchStart.current.y;
    const deltaTime = touchEnd.current.time - touchStart.current.time;

    // Calculate swipe distance and velocity
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    const velocity = distance / deltaTime;

    // Only trigger if swipe is fast enough and long enough
    if (distance < threshold || velocity < 0.1) {
      setIsSwipeActive(false);
      return;
    }

    // Determine swipe direction
    const absDeltaX = Math.abs(deltaX);
    const absDeltaY = Math.abs(deltaY);

    if (absDeltaX > absDeltaY) {
      // Horizontal swipe
      if (deltaX > 0) {
        onSwipeRight?.();
      } else {
        onSwipeLeft?.();
      }
    } else {
      // Vertical swipe
      if (deltaY > 0) {
        onSwipeDown?.();
      } else {
        onSwipeUp?.();
      }
    }

    setIsSwipeActive(false);
  };

  const ref = useRef<HTMLElement>(null);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    // Add touch event listeners
    element.addEventListener('touchstart', handleTouchStart, { passive: false });
    element.addEventListener('touchmove', handleTouchMove, { passive: false });
    element.addEventListener('touchend', handleTouchEnd, { passive: true });

    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchmove', handleTouchMove);
      element.removeEventListener('touchend', handleTouchEnd);
    };
  }, [onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown, threshold, preventScroll]);

  return {
    ref,
    isSwipeActive
  };
};

// Hook for swipe-to-navigate between steps
export const useSwipeNavigation = (
  currentStep: number,
  totalSteps: number,
  onStepChange: (step: number) => void
) => {
  const swipeGestures = useSwipeGestures({
    onSwipeLeft: () => {
      // Swipe left = next step
      if (currentStep < totalSteps) {
        onStepChange(currentStep + 1);
      }
    },
    onSwipeRight: () => {
      // Swipe right = previous step
      if (currentStep > 1) {
        onStepChange(currentStep - 1);
      }
    },
    threshold: 80,
    preventScroll: true
  });

  return swipeGestures;
};

// Hook for swipe-to-action (e.g., swipe to mark as sale)
export const useSwipeActions = (actions: {
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  leftLabel?: string;
  rightLabel?: string;
}) => {
  const [swipeDirection, setSwipeDirection] = useState<'left' | 'right' | null>(null);
  const [swipeProgress, setSwipeProgress] = useState(0);

  const touchStart = useRef<{ x: number; y: number } | null>(null);

  const handleTouchStart = (e: TouchEvent) => {
    const touch = e.touches[0];
    if (touch) {
      touchStart.current = { x: touch.clientX, y: touch.clientY };
      setSwipeDirection(null);
      setSwipeProgress(0);
    }
  };

  const handleTouchMove = (e: TouchEvent) => {
    if (!touchStart.current) return;

    const touch = e.touches[0];
    if (touch) {
      const deltaX = touch.clientX - touchStart.current.x;
      const deltaY = touch.clientY - touchStart.current.y;

      // Only handle horizontal swipes
      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        const progress = Math.min(Math.abs(deltaX) / 150, 1); // 150px = 100% progress
        setSwipeProgress(progress);
        setSwipeDirection(deltaX > 0 ? 'right' : 'left');

        if (progress > 0.2) {
          e.preventDefault(); // Prevent scroll when swipe is significant
        }
      }
    }
  };

  const handleTouchEnd = () => {
    if (swipeProgress > 0.7) { // 70% threshold to trigger action
      if (swipeDirection === 'left' && actions.onSwipeLeft) {
        actions.onSwipeLeft();
      } else if (swipeDirection === 'right' && actions.onSwipeRight) {
        actions.onSwipeRight();
      }
    }

    // Reset state
    setSwipeDirection(null);
    setSwipeProgress(0);
    touchStart.current = null;
  };

  const ref = useRef<HTMLElement>(null);

  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    element.addEventListener('touchstart', handleTouchStart, { passive: false });
    element.addEventListener('touchmove', handleTouchMove, { passive: false });
    element.addEventListener('touchend', handleTouchEnd, { passive: true });

    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchmove', handleTouchMove);
      element.removeEventListener('touchend', handleTouchEnd);
    };
  }, [actions.onSwipeLeft, actions.onSwipeRight]);

  return {
    ref,
    swipeDirection,
    swipeProgress,
    isSwipeActive: swipeProgress > 0
  };
};

// Enhanced haptic feedback utility
export const triggerHapticFeedback = (type: 'light' | 'medium' | 'heavy' | 'success' | 'warning' | 'error' = 'light') => {
  if ('vibrate' in navigator) {
    const patterns = {
      light: [10],
      medium: [20],
      heavy: [30],
      success: [10, 5, 10],
      warning: [20, 10, 20, 10, 20],
      error: [50, 20, 50]
    };
    navigator.vibrate(patterns[type]);
  }
};

// Touch target size validation utility
export const validateTouchTarget = (element: HTMLElement): boolean => {
  const rect = element.getBoundingClientRect();
  const minSize = 44; // 44px minimum as per Apple/Google guidelines
  return rect.width >= minSize && rect.height >= minSize;
};

// Performance monitoring for mobile
export const measurePerformance = async (label: string, fn: () => void | Promise<void>): Promise<number> => {
  const start = performance.now();
  const result = fn();

  if (result instanceof Promise) {
    await result;
  }

  const end = performance.now();
  const duration = end - start;

  if (duration > 250) {
    console.warn(`Performance warning: ${label} took ${duration.toFixed(2)}ms (target: <250ms)`);
  }

  return duration;
};

// Mobile network optimization
export const isMobileNetwork = (): boolean => {
  if ('connection' in navigator) {
    const connection = (navigator as any).connection;
    return connection.effectiveType === 'slow-2g' ||
           connection.effectiveType === '2g' ||
           connection.effectiveType === '3g';
  }
  return false;
};

// Debounced touch handler for performance
export const useDebouncedTouch = (callback: () => void, delay: number = 100) => {
  const timeoutRef = useRef<NodeJS.Timeout>();

  return useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      callback();
    }, delay);
  }, [callback, delay]);
};
