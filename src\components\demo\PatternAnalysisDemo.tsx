import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useData } from '@/context/data';
import { toast } from 'sonner';
import { 
  Clock, 
  Calendar, 
  TrendingUp, 
  AlertTriangle, 
  Play,
  RotateCcw
} from 'lucide-react';

/**
 * Demo component to showcase the intelligent visit pattern analysis
 * This component creates sample data to demonstrate the functionality
 */
export const PatternAnalysisDemo: React.FC = () => {
  const { 
    addAddress, 
    addHouse, 
    addVisit, 
    addDoor,
    getAddressesRequiringReturnVisits,
    getHighPriorityRecommendations,
    updateVisitPatterns
  } = useData();
  
  const [isGenerating, setIsGenerating] = useState(false);
  const [demoCreated, setDemoCreated] = useState(false);

  const generateDemoData = async () => {
    setIsGenerating(true);
    
    try {
      // Create demo addresses
      const address1 = addAddress({
        street: 'Musterstraße',
        city: 'Stuttgart',
        zipCode: '70173'
      });

      const address2 = addAddress({
        street: 'Beispielweg',
        city: 'Stuttgart', 
        zipCode: '70174'
      });

      // Create houses
      const house1 = addHouse({
        addressId: address1.id,
        houseNumber: '42',
        type: 'EFH',
        latitude: 48.7758,
        longitude: 9.1829
      });

      const house2 = addHouse({
        addressId: address2.id,
        houseNumber: '15',
        type: 'MFH',
        latitude: 48.7758,
        longitude: 9.1829
      });

      // Create failed visits with patterns (work hours)
      const failedVisitTimes = [
        '2024-01-15T09:30:00Z', // Monday 9:30 AM
        '2024-01-16T11:00:00Z', // Tuesday 11:00 AM
        '2024-01-17T14:30:00Z', // Wednesday 2:30 PM
        '2024-01-18T10:15:00Z', // Thursday 10:15 AM
        '2024-01-19T13:45:00Z', // Friday 1:45 PM
      ];

      // Create successful visits (evening times)
      const successfulVisitTimes = [
        '2024-01-20T18:30:00Z', // Saturday 6:30 PM
        '2024-01-21T19:15:00Z', // Sunday 7:15 PM
      ];

      // Generate failed visits for house1 (pattern: work hours)
      for (const timestamp of failedVisitTimes) {
        const visit = addVisit({
          houseId: house1.id,
          timestamp,
        });

        addDoor({
          visitId: visit.id,
          name: 'Haupteingang',
          status: 'N/A'
        });
      }

      // Generate successful visits for house1
      for (const timestamp of successfulVisitTimes) {
        const visit = addVisit({
          houseId: house1.id,
          timestamp,
        });

        addDoor({
          visitId: visit.id,
          name: 'Haupteingang',
          status: 'Angetroffen → Sale'
        });
      }

      // Create different pattern for house2 (weekend preference)
      const house2FailedTimes = [
        '2024-01-15T19:30:00Z', // Monday evening
        '2024-01-16T20:00:00Z', // Tuesday evening
        '2024-01-17T18:45:00Z', // Wednesday evening
      ];

      const house2SuccessfulTimes = [
        '2024-01-20T14:30:00Z', // Saturday afternoon
        '2024-01-21T15:15:00Z', // Sunday afternoon
      ];

      // Generate visits for house2
      for (const timestamp of house2FailedTimes) {
        const visit = addVisit({
          houseId: house2.id,
          timestamp,
        });

        addDoor({
          visitId: visit.id,
          name: 'Wohnung 1',
          status: 'N/A'
        });
      }

      for (const timestamp of house2SuccessfulTimes) {
        const visit = addVisit({
          houseId: house2.id,
          timestamp,
        });

        addDoor({
          visitId: visit.id,
          name: 'Wohnung 1',
          status: 'Angetroffen → Termin'
        });
      }

      // Trigger pattern analysis
      updateVisitPatterns(address1.id);
      updateVisitPatterns(address2.id);

      setDemoCreated(true);
      toast.success('Demo-Daten erfolgreich erstellt! Schauen Sie sich die Empfehlungen in der Tagesübersicht an.');
      
    } catch (error) {
      console.error('Error generating demo data:', error);
      toast.error('Fehler beim Erstellen der Demo-Daten');
    } finally {
      setIsGenerating(false);
    }
  };

  const resetDemo = () => {
    // Note: In a real app, you might want to implement a proper reset function
    // For now, we'll just reload the page
    window.location.reload();
  };

  const addressesRequiringVisits = getAddressesRequiringReturnVisits();
  const highPriorityRecommendations = getHighPriorityRecommendations();

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          Intelligente Besuchsoptimierung - Demo
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-sm text-muted-foreground">
          Diese Demo zeigt die intelligente Zeitstempel-basierte Besuchsoptimierung. 
          Das System analysiert fehlgeschlagene Besuche und generiert datenbasierte Empfehlungen.
        </div>

        {!demoCreated ? (
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-semibold text-blue-800 mb-2">Was wird demonstriert:</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Automatische Zeitstempel-Erfassung bei "nicht angetroffen"</li>
                <li>• Mustererkennung (Arbeitszeiten, Wochenend-Präferenzen)</li>
                <li>• Intelligente Besuchsempfehlungen mit Konfidenz-Scores</li>
                <li>• Priorisierung basierend auf Datenqualität</li>
              </ul>
            </div>

            <Button 
              onClick={generateDemoData} 
              disabled={isGenerating}
              className="w-full"
              size="lg"
            >
              {isGenerating ? (
                <>
                  <RotateCcw className="h-4 w-4 mr-2 animate-spin" />
                  Erstelle Demo-Daten...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  Demo starten
                </>
              )}
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <h4 className="font-semibold text-green-800 mb-2">Demo erfolgreich erstellt!</h4>
              <p className="text-sm text-green-700">
                Die Demo-Daten wurden erstellt und Muster analysiert. 
                Schauen Sie sich die Empfehlungen in der Tagesübersicht an.
              </p>
            </div>

            {/* Current Status */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardContent className="pt-4">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-red-500" />
                    <span className="text-sm font-medium">Adressen mit Empfehlungen</span>
                  </div>
                  <div className="text-2xl font-bold mt-1">
                    {addressesRequiringVisits.length}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-4">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-blue-500" />
                    <span className="text-sm font-medium">Hohe Priorität</span>
                  </div>
                  <div className="text-2xl font-bold mt-1">
                    {highPriorityRecommendations.length}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-4">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-green-500" />
                    <span className="text-sm font-medium">Muster erkannt</span>
                  </div>
                  <div className="text-2xl font-bold mt-1">
                    {addressesRequiringVisits.reduce((acc, addr) => acc + addr.recommendations.length, 0)}
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="flex gap-2">
              <Button 
                variant="outline" 
                onClick={resetDemo}
                className="flex-1"
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Demo zurücksetzen
              </Button>
            </div>
          </div>
        )}

        <div className="text-xs text-muted-foreground border-t pt-4">
          <strong>Hinweis:</strong> Diese Demo erstellt Beispieldaten zur Demonstration der Funktionalität. 
          In der Produktionsumgebung werden echte Besuchsdaten analysiert.
        </div>
      </CardContent>
    </Card>
  );
};

export default PatternAnalysisDemo;
