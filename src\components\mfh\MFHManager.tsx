
import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle,
  CardDescription,
  CardFooter 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Plus, Trash } from 'lucide-react';
import { useData } from '@/context/data';
import { VisitStatus, Door } from '@/types';
import { toast } from 'sonner';

interface DoorForm {
  id?: string;
  name: string;
  floor: string;
  status: VisitStatus;
}

const MFHManager: React.FC = () => {
  const { houseId } = useParams<{ houseId: string }>();
  const navigate = useNavigate();
  const { 
    addVisit, 
    addDoor, 
    getHouseById, 
    getAddressById 
  } = useData();

  const [doors, setDoors] = useState<DoorForm[]>([
    { name: '', floor: '', status: 'N/A' }
  ]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  if (!houseId) {
    navigate('/');
    return null;
  }

  const house = getHouseById(houseId);
  if (!house) {
    navigate('/');
    toast.error('Haus nicht gefunden');
    return null;
  }

  const address = getAddressById(house.addressId);
  if (!address) {
    navigate('/');
    toast.error('Adresse nicht gefunden');
    return null;
  }

  const addDoorForm = () => {
    setDoors([...doors, { name: '', floor: '', status: 'N/A' }]);
  };

  const removeDoorForm = (index: number) => {
    const newDoors = [...doors];
    newDoors.splice(index, 1);
    setDoors(newDoors);
  };

  const updateDoorForm = (index: number, field: keyof DoorForm, value: string) => {
    const newDoors = [...doors];
    newDoors[index][field] = value as any;
    setDoors(newDoors);
  };

  const validateForms = () => {
    if (doors.length === 0) {
      toast.error('Bitte fügen Sie mindestens eine Tür hinzu');
      return false;
    }

    for (let i = 0; i < doors.length; i++) {
      if (!doors[i].name) {
        toast.error(`Bitte geben Sie einen Namen für Tür ${i + 1} ein`);
        return false;
      }
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForms()) return;

    setIsSubmitting(true);

    try {
      // Create visit record
      const visit = addVisit({
        houseId,
        timestamp: new Date().toISOString(),
        status: 'N/A', // Status for MFH is aggregated from doors
      });

      // Add doors
      let hasSales = false;
      doors.forEach(doorForm => {
        const door = addDoor({
          visitId: visit.id,
          name: doorForm.name,
          floor: doorForm.floor || undefined,
          status: doorForm.status,
        });

        if (doorForm.status === 'Angetroffen → Sale') {
          hasSales = true;
        }
      });

      toast.success('Mehrfamilienhaus erfolgreich gespeichert');

      // If any door has a sale, navigate to products
      if (hasSales) {
        navigate(`/products/${visit.id}`);
      } else {
        navigate('/daily-view');
      }

    } catch (error) {
      console.error(error);
      toast.error('Fehler beim Speichern des Mehrfamilienhauses');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Mehrfamilienhaus</CardTitle>
        <CardDescription>
          {address.street} {house.houseNumber}, {address.zipCode} {address.city}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {doors.map((door, index) => (
            <div key={index} className="p-4 border rounded-md relative">
              <div className="absolute top-2 right-2">
                {doors.length > 1 && (
                  <Button 
                    variant="ghost" 
                    size="icon"
                    onClick={() => removeDoorForm(index)}
                  >
                    <Trash size={16} className="text-destructive" />
                  </Button>
                )}
              </div>
              
              <h3 className="font-medium mb-3">Tür {index + 1}</h3>
              
              <div className="space-y-3">
                <div>
                  <Label htmlFor={`name-${index}`}>Name/Beschreibung</Label>
                  <Input
                    id={`name-${index}`}
                    value={door.name}
                    onChange={(e) => updateDoorForm(index, 'name', e.target.value)}
                    placeholder="z.B. 'Müller' oder 'Wohnung 3'"
                  />
                </div>
                
                <div>
                  <Label htmlFor={`floor-${index}`}>Stockwerk (optional)</Label>
                  <Input
                    id={`floor-${index}`}
                    value={door.floor}
                    onChange={(e) => updateDoorForm(index, 'floor', e.target.value)}
                    placeholder="z.B. '1. OG' oder 'EG'"
                  />
                </div>
                
                <div>
                  <Label htmlFor={`status-${index}`}>Status</Label>
                  <Select 
                    value={door.status} 
                    onValueChange={(value) => updateDoorForm(index, 'status', value as VisitStatus)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="N/A">N/A (Nicht angetroffen)</SelectItem>
                      <SelectItem value="Angetroffen → Termin">Angetroffen → Termin</SelectItem>
                      <SelectItem value="Angetroffen → Kein Interesse">Angetroffen → Kein Interesse</SelectItem>
                      <SelectItem value="Angetroffen → Sale">Angetroffen → Sale</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          ))}
          
          <Button 
            variant="outline" 
            onClick={addDoorForm}
            className="w-full"
          >
            <Plus size={16} className="mr-2" />
            Tür hinzufügen
          </Button>
        </div>
      </CardContent>
      <CardFooter>
        <Button 
          onClick={handleSubmit}
          className="w-full" 
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Speichern...' : 'Speichern und abschließen'}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default MFHManager;
