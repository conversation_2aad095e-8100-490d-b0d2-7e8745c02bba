
import React from 'react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Search, Filter, X } from 'lucide-react';
import { UserRole } from '@/types';

interface Team {
  id: string;
  name: string;
}

interface UserFiltersProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  selectedRole: string;
  onRoleChange: (value: string) => void;
  selectedTeam: string;
  onTeamChange: (value: string) => void;
  selectedStatus: string;
  onStatusChange: (value: string) => void;
  teams: Team[];
  filteredCount: number;
  totalCount: number;
  onResetFilters: () => void;
  hasActiveFilters: boolean;
}

export const UserFilters: React.FC<UserFiltersProps> = ({
  searchTerm,
  onSearchChange,
  selectedRole,
  onRoleChange,
  selectedTeam,
  onTeamChange,
  selectedStatus,
  onStatusChange,
  teams,
  filteredCount,
  totalCount,
  onResetFilters,
  hasActiveFilters,
}) => {
  return (
    <Card className="mb-6">
      <CardContent className="p-4">
        {/* Search Bar */}
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Suche nach Name, E-Mail oder Rolle..."
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Filters Row */}
        <div className="flex flex-wrap gap-4 items-center mb-4">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Filter:</span>
          </div>

          <Select value={selectedRole} onValueChange={onRoleChange}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Alle Rollen" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Alle Rollen</SelectItem>
              <SelectItem value="berater">Berater</SelectItem>
              <SelectItem value="mentor">Mentor</SelectItem>
              <SelectItem value="teamleiter">Teamleiter</SelectItem>
              <SelectItem value="gebietsmanager">Gebietsmanager</SelectItem>
              <SelectItem value="admin">Administrator</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedTeam} onValueChange={onTeamChange}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Alle Teams" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Alle Teams</SelectItem>
              {teams.map((team) => (
                <SelectItem key={team.id} value={team.id}>
                  {team.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={selectedStatus} onValueChange={onStatusChange}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Alle</SelectItem>
              <SelectItem value="active">Aktiv</SelectItem>
              <SelectItem value="inactive">Inaktiv</SelectItem>
            </SelectContent>
          </Select>

          {hasActiveFilters && (
            <Button variant="outline" size="sm" onClick={onResetFilters}>
              <X className="h-3 w-3 mr-1" />
              Filter zurücksetzen
            </Button>
          )}
        </div>

        {/* Active Filters and Count */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {hasActiveFilters && (
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">Aktive Filter:</span>
                {searchTerm && (
                  <Badge variant="secondary" className="text-xs">
                    Suche: "{searchTerm}"
                  </Badge>
                )}
                {selectedRole !== 'all' && (
                  <Badge variant="secondary" className="text-xs">
                    Rolle: {selectedRole}
                  </Badge>
                )}
                {selectedTeam !== 'all' && (
                  <Badge variant="secondary" className="text-xs">
                    Team: {teams.find(t => t.id === selectedTeam)?.name}
                  </Badge>
                )}
                {selectedStatus !== 'all' && (
                  <Badge variant="secondary" className="text-xs">
                    Status: {selectedStatus === 'active' ? 'Aktiv' : 'Inaktiv'}
                  </Badge>
                )}
              </div>
            )}
          </div>
          <div className="text-sm text-gray-600">
            {filteredCount} von {totalCount} Benutzern
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
