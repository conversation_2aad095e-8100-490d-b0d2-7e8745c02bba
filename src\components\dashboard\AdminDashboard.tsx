
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { useAuth } from '@/context/auth';
import { useData } from '@/context/data';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { useIsMobile } from '@/hooks/use-mobile';
import { UserRole } from '@/types';
import { 
  Settings, 
  Users, 
  Activity, 
  Database, 
  MapPin, 
  Building, 
  TrendingUp, 
  AlertTriangle,
  Download,
  Upload,
  RotateCcw,
  Shield
} from 'lucide-react';
import { getAreas, getTeams, getAuditLogs, getUserProfiles } from '@/integrations/supabase/functions';
import { toast } from 'sonner';

const AdminDashboard: React.FC = () => {
  const { users } = useAuth();
  const { addresses, houses, visits, doors, products } = useData();
  const isMobile = useIsMobile();
  const [areas, setAreas] = useState<any[]>([]);
  const [teams, setTeams] = useState<any[]>([]);
  const [auditLogs, setAuditLogs] = useState<any[]>([]);
  const [userProfiles, setUserProfiles] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadAdminData();
  }, []);

  const loadAdminData = async () => {
    try {
      const [areasData, teamsData, auditData, profilesData] = await Promise.all([
        getAreas(),
        getTeams(),
        getAuditLogs(20),
        getUserProfiles()
      ]);
      
      setAreas(areasData || []);
      setTeams(teamsData || []);
      setAuditLogs(auditData || []);
      setUserProfiles(profilesData || []);
    } catch (error) {
      console.error('Error loading admin data:', error);
      toast.error('Fehler beim Laden der Admin-Daten');
    } finally {
      setLoading(false);
    }
  };
  
  const userCountByRole = {
    admin: users.filter(u => u.role === 'admin').length,
    gebietsmanager: users.filter(u => u.role === 'gebietsmanager').length,
    teamleiter: users.filter(u => u.role === 'teamleiter').length,
    mentor: users.filter(u => u.role === 'mentor').length,
    berater: users.filter(u => u.role === 'berater').length,
  };

  const productsByCategory = {
    kip: products.filter(p => p.category === 'KIP').length,
    tv: products.filter(p => p.category === 'TV').length,
    mobile: products.filter(p => p.category === 'Mobile').length,
  };

  const systemStats = [
    {
      title: "Gebiete",
      value: areas.length,
      icon: MapPin,
      color: "from-blue-500 to-blue-600",
      textColor: "text-blue-600"
    },
    {
      title: "Teams",
      value: teams.length,
      icon: Building,
      color: "from-purple-500 to-purple-600",
      textColor: "text-purple-600"
    },
    {
      title: "Aktive Benutzer",
      value: userProfiles.filter(p => p.is_active !== false).length,
      icon: Users,
      color: "from-green-500 to-green-600",
      textColor: "text-green-600"
    },
    {
      title: "Admin Aktionen",
      value: auditLogs.length,
      icon: Shield,
      color: "from-orange-500 to-orange-600",
      textColor: "text-orange-600"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 p-4 md:p-6">
      <div className="max-w-7xl mx-auto space-y-6 md:space-y-8">
        {/* Header */}
        <div className="text-center md:text-left space-y-2 md:space-y-4 animate-fade-in">
          <h1 className={`font-bold text-gray-800 ${isMobile ? 'text-2xl' : 'text-4xl'}`}>
            Admin Dashboard
          </h1>
          <p className={`text-gray-600 ${isMobile ? 'text-sm' : 'text-lg'}`}>
            Systemverwaltung und Übersichten
          </p>
          <p className={`text-gray-500 ${isMobile ? 'text-xs' : 'text-sm'}`}>
            {format(new Date(), "'Stand:' d. MMMM yyyy", { locale: de })}
          </p>
        </div>

        {/* System Stats Cards */}
        <div className={`grid gap-4 md:gap-6 ${isMobile ? 'grid-cols-2' : 'grid-cols-2 md:grid-cols-4'}`}>
          {systemStats.map((stat, index) => (
            <Card key={stat.title} className={`glass-card hover-lift ${isMobile ? 'p-3' : 'p-4'} rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-scale-in`} style={{ animationDelay: `${index * 0.1}s` }}>
              <CardContent className="p-0">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className={`font-medium text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                      {stat.title}
                    </p>
                    <p className={`font-bold ${stat.textColor} ${isMobile ? 'text-xl' : 'text-3xl'}`}>
                      {stat.value}
                    </p>
                  </div>
                  <div className={`rounded-2xl bg-gradient-to-br ${stat.color} p-2 shadow-lg`}>
                    <stat.icon className={`${isMobile ? 'h-5 w-5' : 'h-6 w-6'} text-white`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        
        <Tabs defaultValue="users" className="w-full">
          <TabsList className={`grid w-full ${isMobile ? 'grid-cols-2' : 'grid-cols-5'} glass-card rounded-2xl bg-white/90 backdrop-blur-sm`}>
            <TabsTrigger value="users" className="flex items-center space-x-2 rounded-xl">
              <Users className="h-4 w-4" />
              <span className={isMobile ? 'hidden' : 'inline'}>Benutzer</span>
            </TabsTrigger>
            <TabsTrigger value="activity" className="flex items-center space-x-2 rounded-xl">
              <Activity className="h-4 w-4" />
              <span className={isMobile ? 'hidden' : 'inline'}>Aktivitäten</span>
            </TabsTrigger>
            <TabsTrigger value="data" className="flex items-center space-x-2 rounded-xl">
              <Database className="h-4 w-4" />
              <span className={isMobile ? 'hidden' : 'inline'}>Daten</span>
            </TabsTrigger>
            <TabsTrigger value="audit" className="flex items-center space-x-2 rounded-xl">
              <Shield className="h-4 w-4" />
              <span className={isMobile ? 'hidden' : 'inline'}>Audit</span>
            </TabsTrigger>
            <TabsTrigger value="system" className="flex items-center space-x-2 rounded-xl">
              <Settings className="h-4 w-4" />
              <span className={isMobile ? 'hidden' : 'inline'}>System</span>
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="users" className="space-y-4 mt-4">
            <div className={`grid gap-4 ${isMobile ? 'grid-cols-2' : 'grid-cols-5'}`}>
              {Object.entries(userCountByRole).map(([role, count]) => (
                <Card key={role} className="glass-card rounded-2xl border-0 shadow-lg bg-white/90 backdrop-blur-sm">
                  <CardHeader className="pb-2">
                    <CardTitle className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-gray-600 capitalize`}>
                      {role}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className={`${isMobile ? 'text-2xl' : 'text-3xl'} font-bold text-blue-600`}>{count}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
            
            <Card className="glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-blue-600" />
                  Benutzerübersicht
                </CardTitle>
                <CardDescription>
                  Verwaltung aller Benutzer im System
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-gray-700`}>Name</TableHead>
                        <TableHead className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-gray-700`}>E-Mail</TableHead>
                        <TableHead className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-gray-700`}>Rolle</TableHead>
                        <TableHead className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-gray-700`}>Team</TableHead>
                        <TableHead className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-gray-700`}>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {users.slice(0, 10).map(user => {
                        const profile = userProfiles.find(p => p.id === user.id);
                        return (
                          <TableRow key={user.id} className="hover:bg-blue-50/50 transition-colors">
                            <TableCell className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium`}>{user.name}</TableCell>
                            <TableCell className={`${isMobile ? 'text-xs' : 'text-sm'}`}>{user.email}</TableCell>
                            <TableCell>
                              <Badge variant="outline" className="capitalize text-xs">
                                {user.role}
                              </Badge>
                            </TableCell>
                            <TableCell className={`${isMobile ? 'text-xs' : 'text-sm'}`}>{user.teamId || '-'}</TableCell>
                            <TableCell>
                              <Badge variant={profile?.is_active !== false ? 'default' : 'secondary'} className="text-xs">
                                {profile?.is_active !== false ? 'Aktiv' : 'Inaktiv'}
                              </Badge>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="activity" className="space-y-4 mt-4">
            <div className={`grid gap-4 ${isMobile ? 'grid-cols-1' : 'grid-cols-3'}`}>
              <Card className="glass-card rounded-2xl border-0 shadow-lg bg-white/90 backdrop-blur-sm">
                <CardHeader className="pb-2">
                  <CardTitle className={`${isMobile ? 'text-sm' : 'text-sm'} font-medium text-gray-600`}>Besuche</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className={`${isMobile ? 'text-2xl' : 'text-3xl'} font-bold text-blue-600`}>{visits.length}</p>
                </CardContent>
              </Card>
              
              <Card className="glass-card rounded-2xl border-0 shadow-lg bg-white/90 backdrop-blur-sm">
                <CardHeader className="pb-2">
                  <CardTitle className={`${isMobile ? 'text-sm' : 'text-sm'} font-medium text-gray-600`}>Türen</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className={`${isMobile ? 'text-2xl' : 'text-3xl'} font-bold text-purple-600`}>{doors.length}</p>
                </CardContent>
              </Card>
              
              <Card className="glass-card rounded-2xl border-0 shadow-lg bg-white/90 backdrop-blur-sm">
                <CardHeader className="pb-2">
                  <CardTitle className={`${isMobile ? 'text-sm' : 'text-sm'} font-medium text-gray-600`}>Verkäufe</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className={`${isMobile ? 'text-2xl' : 'text-3xl'} font-bold text-green-600`}>{products.length}</p>
                </CardContent>
              </Card>
            </div>
            
            <Card className="glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                  Produktverteilung
                </CardTitle>
                <CardDescription>
                  Übersicht aller verkauften Produkte nach Kategorie
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className={`grid gap-4 ${isMobile ? 'grid-cols-1' : 'grid-cols-3'}`}>
                  {Object.entries(productsByCategory).map(([category, count]) => (
                    <Card key={category} className="glass-card rounded-2xl border-0 shadow-lg bg-white/90 backdrop-blur-sm">
                      <CardHeader className="pb-2">
                        <CardTitle className={`${isMobile ? 'text-sm' : 'text-sm'} font-medium text-gray-600 uppercase`}>{category}</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className={`${isMobile ? 'text-2xl' : 'text-3xl'} font-bold text-green-600`}>{count}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="data" className="space-y-4 mt-4">
            <div className={`grid gap-4 ${isMobile ? 'grid-cols-2' : 'grid-cols-4'}`}>
              <Card className="glass-card rounded-2xl border-0 shadow-lg bg-white/90 backdrop-blur-sm">
                <CardHeader className="pb-2">
                  <CardTitle className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-gray-600`}>Adressen</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className={`${isMobile ? 'text-2xl' : 'text-3xl'} font-bold text-blue-600`}>{addresses.length}</p>
                </CardContent>
              </Card>
              
              <Card className="glass-card rounded-2xl border-0 shadow-lg bg-white/90 backdrop-blur-sm">
                <CardHeader className="pb-2">
                  <CardTitle className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-gray-600`}>Häuser</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className={`${isMobile ? 'text-2xl' : 'text-3xl'} font-bold text-purple-600`}>{houses.length}</p>
                </CardContent>
              </Card>
              
              <Card className="glass-card rounded-2xl border-0 shadow-lg bg-white/90 backdrop-blur-sm">
                <CardHeader className="pb-2">
                  <CardTitle className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-gray-600`}>EFH</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className={`${isMobile ? 'text-2xl' : 'text-3xl'} font-bold text-green-600`}>{houses.filter(h => h.type === 'EFH').length}</p>
                </CardContent>
              </Card>
              
              <Card className="glass-card rounded-2xl border-0 shadow-lg bg-white/90 backdrop-blur-sm">
                <CardHeader className="pb-2">
                  <CardTitle className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-gray-600`}>MFH</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className={`${isMobile ? 'text-2xl' : 'text-3xl'} font-bold text-orange-600`}>{houses.filter(h => h.type === 'MFH').length}</p>
                </CardContent>
              </Card>
            </div>
            
            <Card className="glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5 text-blue-600" />
                  Datenübersicht
                </CardTitle>
                <CardDescription>
                  {format(new Date(), "'Stand:' d. MMMM yyyy", { locale: de })}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p className={`${isMobile ? 'text-sm' : 'text-base'}`}>In der Datenbank befinden sich:</p>
                  <ul className={`list-disc list-inside space-y-1 ${isMobile ? 'text-sm' : 'text-base'}`}>
                    <li>{addresses.length} Adressen</li>
                    <li>{houses.length} Häuser ({houses.filter(h => h.type === 'EFH').length} EFH, {houses.filter(h => h.type === 'MFH').length} MFH)</li>
                    <li>{visits.length} Besuche</li>
                    <li>{doors.length} erfasste Türen</li>
                    <li>{products.length} verkaufte Produkte</li>
                    <li>{areas.length} Gebiete</li>
                    <li>{teams.length} Teams</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="audit" className="space-y-4 mt-4">
            <Card className="glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5 text-orange-600" />
                  Admin-Aktionen Protokoll
                </CardTitle>
                <CardDescription>
                  Letzte {auditLogs.length} Admin-Aktionen im System
                </CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="text-center py-8">
                    <p className="text-gray-500">Lade Audit-Logs...</p>
                  </div>
                ) : auditLogs.length === 0 ? (
                  <div className="text-center py-8">
                    <Shield className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-500">Noch keine Admin-Aktionen protokolliert</p>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-gray-700`}>Aktion</TableHead>
                          <TableHead className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-gray-700`}>Typ</TableHead>
                          <TableHead className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-gray-700`}>Beschreibung</TableHead>
                          <TableHead className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-gray-700`}>Datum</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {auditLogs.map((log) => (
                          <TableRow key={log.id} className="hover:bg-blue-50/50 transition-colors">
                            <TableCell>
                              <Badge variant="outline" className="text-xs">
                                {log.action_type}
                              </Badge>
                            </TableCell>
                            <TableCell className={`${isMobile ? 'text-xs' : 'text-sm'}`}>
                              {log.target_type}
                            </TableCell>
                            <TableCell className={`${isMobile ? 'text-xs' : 'text-sm'}`}>
                              {log.description || '-'}
                            </TableCell>
                            <TableCell className={`${isMobile ? 'text-xs' : 'text-sm'}`}>
                              {format(new Date(log.created_at), 'dd.MM.yyyy HH:mm', { locale: de })}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="system" className="space-y-4 mt-4">
            <Card className="glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5 text-blue-600" />
                  Systemkonfiguration
                </CardTitle>
                <CardDescription>
                  Verwaltung der Systemeinstellungen und Wartungsoptionen
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid gap-2 md:grid-cols-2">
                    <Button variant="outline" className="flex items-center gap-2">
                      <Download className="h-4 w-4" />
                      Daten exportieren
                    </Button>
                    <Button variant="outline" className="flex items-center gap-2">
                      <Upload className="h-4 w-4" />
                      Daten importieren
                    </Button>
                    <Button variant="outline" className="flex items-center gap-2 text-orange-600 border-orange-200 hover:bg-orange-50">
                      <RotateCcw className="h-4 w-4" />
                      Cache leeren
                    </Button>
                    <Button variant="outline" className="flex items-center gap-2 text-red-600 border-red-200 hover:bg-red-50">
                      <AlertTriangle className="h-4 w-4" />
                      System zurücksetzen
                    </Button>
                  </div>
                  
                  <div className="border-t pt-4">
                    <h4 className="font-medium text-gray-800 mb-2">Schnellzugriff</h4>
                    <div className="grid gap-2 md:grid-cols-2">
                      <Button 
                        variant="ghost" 
                        className="justify-start"
                        onClick={() => window.open('/area-management', '_blank')}
                      >
                        <MapPin className="h-4 w-4 mr-2" />
                        Gebiete verwalten
                      </Button>
                      <Button 
                        variant="ghost" 
                        className="justify-start"
                        onClick={() => window.open('/team-management', '_blank')}
                      >
                        <Building className="h-4 w-4 mr-2" />
                        Teams verwalten
                      </Button>
                      <Button 
                        variant="ghost" 
                        className="justify-start"
                        onClick={() => window.open('/user-management', '_blank')}
                      >
                        <Users className="h-4 w-4 mr-2" />
                        Benutzer verwalten
                      </Button>
                      <Button 
                        variant="ghost" 
                        className="justify-start"
                        onClick={() => window.open('/settings', '_blank')}
                      >
                        <Settings className="h-4 w-4 mr-2" />
                        Einstellungen
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default AdminDashboard;
