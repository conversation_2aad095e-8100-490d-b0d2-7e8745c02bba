
import React, { useEffect, useState } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { useAuth } from '@/context/auth';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { useData } from '@/context/data';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useIsMobile } from '@/hooks/use-mobile';
import { Users, TrendingUp, Building } from 'lucide-react';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';

const TeamsStatisticsPage: React.FC = () => {
  const { user, users } = useAuth();
  const { visits, products } = useData();
  const [activeTeam, setActiveTeam] = useState<string | null>(null);
  const isMobile = useIsMobile();

  // Define team data
  const teams = [
    { id: 'team-ulm', name: 'Team Ulm' },
    { id: 'team-stuttgart', name: 'Team Stuttgart' },
    { id: 'team-ludwigsburg', name: 'Team Ludwigsburg' }
  ];
  
  // Set initial active team
  useEffect(() => {
    if (user?.teamId && !activeTeam) {
      setActiveTeam(user.teamId);
    } else if (!activeTeam && teams.length > 0) {
      setActiveTeam(teams[0].id);
    }
  }, [user, activeTeam, teams]);

  if (!user || user.role !== 'teamleiter') {
    return (
      <MainLayout title="Teams Statistiken">
        <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 flex items-center justify-center p-4">
          <div className="text-center">
            <p className="text-gray-600">Sie haben keine Berechtigung, diese Seite anzuzeigen.</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  // Get selected team data
  const selectedTeam = teams.find(t => t.id === activeTeam) || teams[0];

  // Mock data for roles by team
  const getRoleData = (teamId: string) => {
    const roleMultipliers: Record<string, number> = {
      'team-ulm': 1,
      'team-stuttgart': 1.5,
      'team-ludwigsburg': 0.8
    };
    
    const multiplier = roleMultipliers[teamId] || 1;
    
    return [
      { name: 'Berater', value: Math.round(8 * multiplier) },
      { name: 'Mentoren', value: Math.round(4 * multiplier) },
      { name: 'Teamleiter', value: 1 }
    ];
  };

  const visitsByRole = getRoleData(activeTeam || '');
  const COLORS = ['#3b82f6', '#10b981', '#f59e0b'];

  // Product statistics by category
  const getProductData = (teamId: string) => {
    const productMultipliers: Record<string, number> = {
      'team-ulm': 1.2,
      'team-stuttgart': 0.9,
      'team-ludwigsburg': 1.5
    };
    
    const multiplier = productMultipliers[teamId] || 1;
    
    return [
      { name: 'KIP', value: Math.round(15 * multiplier) },
      { name: 'TV', value: Math.round(10 * multiplier) },
      { name: 'Mobile', value: Math.round(20 * multiplier) }
    ];
  };
  
  const productsByCategory = getProductData(activeTeam || '');

  return (
    <MainLayout title="Teams Statistiken">
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 p-4 md:p-6">
        <div className="max-w-7xl mx-auto space-y-6 md:space-y-8">
          {/* Header Section */}
          <div className="text-center md:text-left space-y-2 md:space-y-4 animate-fade-in">
            <h1 className={`font-bold text-gray-800 ${isMobile ? 'text-2xl' : 'text-4xl'}`}>
              Teams Statistiken
            </h1>
            <p className={`text-gray-600 ${isMobile ? 'text-sm' : 'text-lg'}`}>
              Statistiken für {selectedTeam.name}
            </p>
            <p className={`text-gray-500 ${isMobile ? 'text-xs' : 'text-sm'}`}>
              {format(new Date(), "'Stand:' d. MMMM yyyy", { locale: de })}
            </p>
          </div>

          {/* Team Selection */}
          <Card className="glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-slide-in">
            <CardHeader className={`${isMobile ? 'p-4 pb-2' : 'p-6 pb-4'}`}>
              <CardTitle className={`flex items-center gap-3 ${isMobile ? 'text-lg' : 'text-xl'} font-bold text-gray-800`}>
                <Building className={`${isMobile ? 'h-5 w-5' : 'h-6 w-6'} text-blue-600`} />
                Team auswählen
              </CardTitle>
            </CardHeader>
            <CardContent className={`${isMobile ? 'p-4 pt-0' : 'p-6 pt-0'}`}>
              <div className={`flex flex-wrap gap-2 ${isMobile ? 'gap-2' : 'gap-3'}`}>
                {teams.map(team => (
                  <button
                    key={team.id}
                    className={`${isMobile ? 'px-3 py-2 text-sm' : 'px-4 py-2.5 text-base'} rounded-2xl font-medium transition-all duration-300 hover-scale ${
                      activeTeam === team.id
                        ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                    onClick={() => setActiveTeam(team.id)}
                  >
                    {team.name}
                  </button>
                ))}
              </div>
            </CardContent>
          </Card>
          
          {/* Charts Section */}
          <div className={`grid gap-6 ${isMobile ? 'grid-cols-1' : 'grid-cols-1 lg:grid-cols-2'}`}>
            {/* Team by Role Chart */}
            <Card className="glass-card hover-lift rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-slide-in">
              <CardHeader className={`${isMobile ? 'p-4 pb-2' : 'p-6 pb-4'}`}>
                <CardTitle className={`flex items-center gap-3 ${isMobile ? 'text-lg' : 'text-xl'} font-bold text-gray-800`}>
                  <Users className={`${isMobile ? 'h-5 w-5' : 'h-6 w-6'} text-blue-600`} />
                  Team nach Rolle
                </CardTitle>
                <CardDescription className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-600`}>
                  Verteilung der Rollen im Team
                </CardDescription>
              </CardHeader>
              <CardContent className={`${isMobile ? 'p-4 pt-0' : 'p-6 pt-0'}`}>
                <div style={{ height: isMobile ? '280px' : '350px' }} className="w-full">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={visitsByRole}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={isMobile ? 70 : 90}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {visitsByRole.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip 
                        contentStyle={{
                          backgroundColor: 'rgba(255, 255, 255, 0.95)',
                          border: 'none',
                          borderRadius: '12px',
                          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
                        }}
                      />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            {/* Sales by Category Chart */}
            <Card className="glass-card hover-lift rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-slide-in">
              <CardHeader className={`${isMobile ? 'p-4 pb-2' : 'p-6 pb-4'}`}>
                <CardTitle className={`flex items-center gap-3 ${isMobile ? 'text-lg' : 'text-xl'} font-bold text-gray-800`}>
                  <TrendingUp className={`${isMobile ? 'h-5 w-5' : 'h-6 w-6'} text-green-600`} />
                  Verkäufe nach Kategorie
                </CardTitle>
                <CardDescription className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-600`}>
                  Produktverkäufe pro Kategorie
                </CardDescription>
              </CardHeader>
              <CardContent className={`${isMobile ? 'p-4 pt-0' : 'p-6 pt-0'}`}>
                <div style={{ height: isMobile ? '280px' : '350px' }} className="w-full">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={productsByCategory} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                      <XAxis 
                        dataKey="name" 
                        tick={{ fontSize: isMobile ? 12 : 14 }}
                        stroke="#64748b"
                      />
                      <YAxis 
                        tick={{ fontSize: isMobile ? 12 : 14 }}
                        stroke="#64748b"
                      />
                      <Tooltip 
                        contentStyle={{
                          backgroundColor: 'rgba(255, 255, 255, 0.95)',
                          border: 'none',
                          borderRadius: '12px',
                          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
                        }}
                      />
                      <Legend />
                      <Bar 
                        dataKey="value" 
                        name="Anzahl" 
                        fill="#8b5cf6" 
                        radius={[4, 4, 0, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default TeamsStatisticsPage;
