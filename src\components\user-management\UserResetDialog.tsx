
import React from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { User } from '@/types';
import { RotateCcw, AlertTriangle } from 'lucide-react';

interface UserResetDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  user: User | null;
  onConfirm: () => void;
}

export const UserResetDialog: React.FC<UserResetDialogProps> = ({
  isOpen,
  onOpenChange,
  user,
  onConfirm,
}) => {
  if (!user) return null;

  return (
    <AlertDialog open={isOpen} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            Statistiken zurücksetzen
          </AlertDialogTitle>
          <AlertDialogDescription>
            <div className="space-y-3">
              <p>
                Möchten Sie wirklich die Statistiken von <strong>{user.name}</strong> zurücksetzen?
              </p>
              
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
                <div className="flex items-start gap-2">
                  <RotateCcw className="h-4 w-4 text-orange-600 mt-0.5" />
                  <div className="text-sm">
                    <p className="font-medium text-orange-800 mb-1">
                      Folgende Daten werden zurückgesetzt:
                    </p>
                    <ul className="text-orange-700 space-y-1">
                      <li>• Gesamte Besuche → 0</li>
                      <li>• Gesamte Verkäufe → 0</li>
                      <li>• Reset-Zähler wird um 1 erhöht</li>
                      <li>• Zeitstempel des letzten Resets wird aktualisiert</li>
                    </ul>
                  </div>
                </div>
              </div>

              <p className="text-sm text-gray-600">
                <strong>Hinweis:</strong> Diese Aktion kann nicht rückgängig gemacht werden. 
                Alle bisherigen Statistiken gehen unwiderruflich verloren.
              </p>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Abbrechen</AlertDialogCancel>
          <AlertDialogAction 
            onClick={onConfirm}
            className="bg-orange-600 hover:bg-orange-700"
          >
            Statistiken zurücksetzen
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
