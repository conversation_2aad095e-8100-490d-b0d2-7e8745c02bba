
import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle,
  CardDescription,
  CardFooter 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Plus, Trash, GripVertical, Copy, Home, User } from 'lucide-react';
import { useData } from '@/context/data';
import { VisitStatus } from '@/types';
import { toast } from 'sonner';

interface DoorForm {
  id: string;
  name: string;
  floor: string;
  status: VisitStatus;
}

const ModernMFHManager: React.FC = () => {
  const { houseId } = useParams<{ houseId: string }>();
  const navigate = useNavigate();
  const { 
    addVisit, 
    addDoor, 
    getHouseById, 
    getAddressById 
  } = useData();

  const [doors, setDoors] = useState<DoorForm[]>([
    { id: '1', name: '', floor: '', status: 'N/A' }
  ]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  if (!houseId) {
    navigate('/');
    return null;
  }

  const house = getHouseById(houseId);
  if (!house) {
    navigate('/');
    toast.error('Haus nicht gefunden');
    return null;
  }

  const address = getAddressById(house.addressId);
  if (!address) {
    navigate('/');
    toast.error('Adresse nicht gefunden');
    return null;
  }

  const addDoorForm = () => {
    const newId = (Math.max(...doors.map(d => parseInt(d.id))) + 1).toString();
    setDoors([...doors, { id: newId, name: '', floor: '', status: 'N/A' }]);
  };

  const removeDoorForm = (id: string) => {
    if (doors.length > 1) {
      setDoors(doors.filter(door => door.id !== id));
    }
  };

  const duplicateDoorForm = (index: number) => {
    const doorToCopy = doors[index];
    const newId = (Math.max(...doors.map(d => parseInt(d.id))) + 1).toString();
    const newDoor = { 
      ...doorToCopy, 
      id: newId,
      name: doorToCopy.name + ' (Kopie)',
      status: 'N/A' as VisitStatus
    };
    setDoors([...doors.slice(0, index + 1), newDoor, ...doors.slice(index + 1)]);
  };

  const updateDoorForm = (id: string, field: keyof Omit<DoorForm, 'id'>, value: string) => {
    setDoors(doors.map(door => 
      door.id === id ? { ...door, [field]: value } : door
    ));
  };

  const moveDoor = (fromIndex: number, toIndex: number) => {
    const newDoors = [...doors];
    const [movedDoor] = newDoors.splice(fromIndex, 1);
    newDoors.splice(toIndex, 0, movedDoor);
    setDoors(newDoors);
  };

  // Quick templates for common MFH layouts
  const applyTemplate = (template: 'small' | 'medium' | 'large') => {
    let templateDoors: Omit<DoorForm, 'id'>[] = [];
    
    switch (template) {
      case 'small':
        templateDoors = [
          { name: 'EG links', floor: 'EG', status: 'N/A' },
          { name: 'EG rechts', floor: 'EG', status: 'N/A' },
          { name: '1. OG links', floor: '1. OG', status: 'N/A' },
          { name: '1. OG rechts', floor: '1. OG', status: 'N/A' },
        ];
        break;
      case 'medium':
        templateDoors = [
          { name: 'EG Wohnung 1', floor: 'EG', status: 'N/A' },
          { name: 'EG Wohnung 2', floor: 'EG', status: 'N/A' },
          { name: '1. OG Wohnung 3', floor: '1. OG', status: 'N/A' },
          { name: '1. OG Wohnung 4', floor: '1. OG', status: 'N/A' },
          { name: '2. OG Wohnung 5', floor: '2. OG', status: 'N/A' },
          { name: '2. OG Wohnung 6', floor: '2. OG', status: 'N/A' },
        ];
        break;
      case 'large':
        templateDoors = Array.from({ length: 12 }, (_, i) => ({
          name: `Wohnung ${i + 1}`,
          floor: i < 3 ? 'EG' : i < 6 ? '1. OG' : i < 9 ? '2. OG' : '3. OG',
          status: 'N/A' as VisitStatus
        }));
        break;
    }
    
    setDoors(templateDoors.map((door, index) => ({ ...door, id: (index + 1).toString() })));
    toast.success(`${template === 'small' ? 'Kleine' : template === 'medium' ? 'Mittlere' : 'Große'} MFH-Vorlage angewendet`);
  };

  const validateForms = () => {
    if (doors.length === 0) {
      toast.error('Bitte fügen Sie mindestens eine Tür hinzu');
      return false;
    }

    const emptyDoors = doors.filter(door => !door.name.trim());
    if (emptyDoors.length > 0) {
      toast.error(`Bitte geben Sie Namen für alle ${emptyDoors.length} Türen ein`);
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForms()) return;

    setIsSubmitting(true);

    try {
      // Create visit record
      const visit = addVisit({
        houseId,
        timestamp: new Date().toISOString(),
        status: 'N/A',
      });

      // Add doors
      let hasSales = false;
      doors.forEach(doorForm => {
        addDoor({
          visitId: visit.id,
          name: doorForm.name,
          floor: doorForm.floor || undefined,
          status: doorForm.status,
        });

        if (doorForm.status === 'Angetroffen → Sale') {
          hasSales = true;
        }
      });

      toast.success(`Mehrfamilienhaus mit ${doors.length} Türen erfolgreich gespeichert`);

      // Navigate based on sales
      if (hasSales) {
        navigate(`/products/${visit.id}`);
      } else {
        navigate('/daily-view');
      }

    } catch (error) {
      console.error(error);
      toast.error('Fehler beim Speichern des Mehrfamilienhauses');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStatusColor = (status: VisitStatus) => {
    switch (status) {
      case 'N/A': return 'bg-gray-50 border-gray-200 text-gray-700';
      case 'Angetroffen → Termin': return 'bg-yellow-50 border-yellow-200 text-yellow-700';
      case 'Angetroffen → Kein Interesse': return 'bg-red-50 border-red-200 text-red-700';
      case 'Angetroffen → Sale': return 'bg-green-50 border-green-200 text-green-700';
      default: return 'bg-gray-50 border-gray-200 text-gray-700';
    }
  };

  return (
    <Card className="w-full bg-white/95 backdrop-blur-sm border-0 shadow-2xl rounded-3xl overflow-hidden animate-fade-in">
      <CardHeader className="text-center pb-6 pt-8 bg-gradient-to-b from-red-50/50 to-transparent">
        <div className="flex items-center justify-center mb-4">
          <Home className="h-8 w-8 text-red-600" />
        </div>
        <CardTitle className="text-2xl font-bold text-gray-800">Mehrfamilienhaus</CardTitle>
        <CardDescription className="text-lg mt-2">
          {address.street} {house.houseNumber}, {address.zipCode} {address.city}
        </CardDescription>
      </CardHeader>
      
      <CardContent className="px-8 pt-6">
        {/* Quick Templates */}
        <div className="mb-8 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200">
          <h3 className="font-semibold text-gray-800 mb-3 flex items-center gap-2">
            <Copy className="h-4 w-4" />
            Schnell-Vorlagen
          </h3>
          <div className="grid grid-cols-3 gap-2">
            <Button 
              type="button"
              variant="outline" 
              size="sm"
              onClick={() => applyTemplate('small')}
              className="text-xs hover:bg-blue-100"
            >
              Klein (4 Türen)
            </Button>
            <Button 
              type="button"
              variant="outline" 
              size="sm"
              onClick={() => applyTemplate('medium')}
              className="text-xs hover:bg-purple-100"
            >
              Mittel (6 Türen)
            </Button>
            <Button 
              type="button"
              variant="outline" 
              size="sm"
              onClick={() => applyTemplate('large')}
              className="text-xs hover:bg-indigo-100"
            >
              Groß (12 Türen)
            </Button>
          </div>
        </div>

        <div className="space-y-4">
          {doors.map((door, index) => (
            <div 
              key={door.id} 
              className={`p-6 border-2 rounded-2xl relative transition-all duration-200 hover:shadow-lg ${getStatusColor(door.status)}`}
            >
              {/* Header with controls */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <GripVertical className="h-5 w-5 text-gray-400 cursor-move" />
                  <User className="h-5 w-5 text-red-500" />
                  <h3 className="font-semibold text-lg">Tür {index + 1}</h3>
                </div>
                <div className="flex items-center gap-2">
                  <Button 
                    type="button"
                    variant="ghost" 
                    size="icon"
                    onClick={() => duplicateDoorForm(index)}
                    className="h-8 w-8 hover:bg-blue-100"
                    title="Tür duplizieren"
                  >
                    <Copy size={14} />
                  </Button>
                  {doors.length > 1 && (
                    <Button 
                      type="button"
                      variant="ghost" 
                      size="icon"
                      onClick={() => removeDoorForm(door.id)}
                      className="h-8 w-8 hover:bg-red-100"
                      title="Tür entfernen"
                    >
                      <Trash size={14} className="text-red-500" />
                    </Button>
                  )}
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="md:col-span-2">
                  <Label htmlFor={`name-${door.id}`} className="text-sm font-semibold text-gray-700">
                    Name/Bewohner
                  </Label>
                  <Input
                    id={`name-${door.id}`}
                    value={door.name}
                    onChange={(e) => updateDoorForm(door.id, 'name', e.target.value)}
                    placeholder="z.B. 'Familie Müller' oder 'Wohnung 3A'"
                    className="mt-1 h-12 text-base border-2 rounded-lg"
                  />
                </div>
                
                <div>
                  <Label htmlFor={`floor-${door.id}`} className="text-sm font-semibold text-gray-700">
                    Stockwerk
                  </Label>
                  <Select 
                    value={door.floor} 
                    onValueChange={(value) => updateDoorForm(door.id, 'floor', value)}
                  >
                    <SelectTrigger className="mt-1 h-12 text-base border-2 rounded-lg">
                      <SelectValue placeholder="Wählen..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="KG">Keller</SelectItem>
                      <SelectItem value="EG">Erdgeschoss</SelectItem>
                      <SelectItem value="1. OG">1. Obergeschoss</SelectItem>
                      <SelectItem value="2. OG">2. Obergeschoss</SelectItem>
                      <SelectItem value="3. OG">3. Obergeschoss</SelectItem>
                      <SelectItem value="4. OG">4. Obergeschoss</SelectItem>
                      <SelectItem value="DG">Dachgeschoss</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="mt-4">
                <Label htmlFor={`status-${door.id}`} className="text-sm font-semibold text-gray-700">
                  Besuchsstatus
                </Label>
                <Select 
                  value={door.status} 
                  onValueChange={(value) => updateDoorForm(door.id, 'status', value as VisitStatus)}
                >
                  <SelectTrigger className="mt-1 h-12 text-base border-2 rounded-lg">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="N/A">N/A (Nicht angetroffen)</SelectItem>
                    <SelectItem value="Angetroffen → Termin">Angetroffen → Termin</SelectItem>
                    <SelectItem value="Angetroffen → Kein Interesse">Angetroffen → Kein Interesse</SelectItem>
                    <SelectItem value="Angetroffen → Sale">Angetroffen → Sale</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          ))}
          
          <Button 
            type="button"
            variant="outline" 
            onClick={addDoorForm}
            className="w-full h-16 text-lg border-2 border-dashed border-gray-300 hover:border-red-400 hover:bg-red-50 rounded-xl transition-all duration-200"
          >
            <Plus size={20} className="mr-2" />
            Weitere Tür hinzufügen
          </Button>
        </div>
      </CardContent>
      
      <CardFooter className="px-8 pb-8">
        <div className="w-full space-y-4">
          <div className="text-center text-sm text-gray-600">
            {doors.length} {doors.length === 1 ? 'Tür' : 'Türen'} erfasst
          </div>
          <Button 
            onClick={handleSubmit}
            className="w-full h-14 text-lg font-semibold bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 rounded-xl transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl disabled:opacity-50" 
            disabled={isSubmitting || doors.some(d => !d.name.trim())}
          >
            {isSubmitting ? (
              <div className="flex items-center">
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-3"></div>
                Speichern...
              </div>
            ) : (
              'Besuch abschließen und speichern'
            )}
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};

export default ModernMFHManager;
