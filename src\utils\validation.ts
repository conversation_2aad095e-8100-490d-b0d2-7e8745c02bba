import { ValidationError, PasswordValidation, PasswordStrength, PasswordRequirements, FormValidator } from '@/context/auth/types';

// Enhanced validation constants
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
const PASSWORD_MIN_LENGTH = 8;
const SPECIAL_CHARS_REGEX = /[!@#$%^&*(),.?":{}|<>]/;
const UPPERCASE_REGEX = /[A-Z]/;
const LOWERCASE_REGEX = /[a-z]/;
const NUMBERS_REGEX = /[0-9]/;

// Common weak passwords to check against
const COMMON_PASSWORDS = [
  'password', '123456', '123456789', 'qwerty', 'abc123', 'password123',
  'admin', 'letmein', 'welcome', 'monkey', '1234567890', 'password1'
];

// Sequential patterns to detect
const SEQUENTIAL_PATTERNS = [
  '123', '234', '345', '456', '567', '678', '789', '890',
  'abc', 'bcd', 'cde', 'def', 'efg', 'fgh', 'ghi', 'hij',
  'qwe', 'wer', 'ert', 'rty', 'tyu', 'yui', 'uio', 'iop'
];

/**
 * Helper function to check for common password patterns
 */
const hasCommonPatterns = (password: string): boolean => {
  const lowerPassword = password.toLowerCase();

  // Check against common passwords
  if (COMMON_PASSWORDS.some(common => lowerPassword.includes(common))) {
    return true;
  }

  // Check for sequential patterns
  if (SEQUENTIAL_PATTERNS.some(pattern => lowerPassword.includes(pattern))) {
    return true;
  }

  // Check for repeated characters (3 or more)
  if (/(.)\1{2,}/.test(password)) {
    return true;
  }

  return false;
};

/**
 * Helper function to check for personal information in password
 */
const hasPersonalInfo = (password: string, personalInfo?: { name?: string; email?: string }): boolean => {
  if (!personalInfo) return false;

  const lowerPassword = password.toLowerCase();

  // Check name
  if (personalInfo.name) {
    const nameParts = personalInfo.name.toLowerCase().split(' ');
    if (nameParts.some(part => part.length > 2 && lowerPassword.includes(part))) {
      return true;
    }
  }

  // Check email parts
  if (personalInfo.email) {
    const emailParts = personalInfo.email.toLowerCase().split('@')[0];
    if (emailParts && emailParts.length > 2 && lowerPassword.includes(emailParts)) {
      return true;
    }
  }

  return false;
};

/**
 * Calculate password entropy for strength assessment
 */
const calculatePasswordEntropy = (password: string): number => {
  let charsetSize = 0;

  if (/[a-z]/.test(password)) charsetSize += 26;
  if (/[A-Z]/.test(password)) charsetSize += 26;
  if (/[0-9]/.test(password)) charsetSize += 10;
  if (/[^a-zA-Z0-9]/.test(password)) charsetSize += 32; // Approximate special chars

  return password.length * Math.log2(charsetSize);
};

/**
 * Validates email format
 */
export const validateEmail = (email: string): ValidationError | null => {
  if (!email.trim()) {
    return { field: 'email', message: 'E-Mail-Adresse ist erforderlich' };
  }
  
  if (!EMAIL_REGEX.test(email)) {
    return { field: 'email', message: 'Bitte geben Sie eine gültige E-Mail-Adresse ein' };
  }
  
  return null;
};

/**
 * Validates name format
 */
export const validateName = (name: string): ValidationError | null => {
  if (!name.trim()) {
    return { field: 'name', message: 'Name ist erforderlich' };
  }
  
  if (name.trim().length < 2) {
    return { field: 'name', message: 'Name muss mindestens 2 Zeichen lang sein' };
  }
  
  if (name.trim().length > 50) {
    return { field: 'name', message: 'Name darf maximal 50 Zeichen lang sein' };
  }
  
  return null;
};

/**
 * Validates password and returns detailed analysis
 */
export const validatePassword = (password: string, personalInfo?: { name?: string; email?: string }): PasswordValidation => {
  // Check basic requirements
  const requirements: PasswordRequirements = {
    minLength: password.length >= PASSWORD_MIN_LENGTH,
    hasUppercase: UPPERCASE_REGEX.test(password),
    hasLowercase: LOWERCASE_REGEX.test(password),
    hasNumbers: NUMBERS_REGEX.test(password),
    hasSpecialChars: SPECIAL_CHARS_REGEX.test(password),
    noCommonPatterns: !hasCommonPatterns(password),
    noPersonalInfo: !hasPersonalInfo(password, personalInfo),
  };

  // Calculate score based on requirements met
  const basicScore = Object.values(requirements).filter(Boolean).length;

  // Calculate entropy for additional scoring
  const entropy = calculatePasswordEntropy(password);

  // Enhanced scoring with entropy consideration
  let score = basicScore;
  if (entropy > 50) score += 1; // Bonus for high entropy
  if (password.length > 12) score += 1; // Bonus for length

  // Determine strength with enhanced levels
  let strength: PasswordStrength;
  if (score <= 3) {
    strength = 'weak';
  } else if (score <= 4) {
    strength = 'fair';
  } else if (score <= 5) {
    strength = 'good';
  } else if (score <= 6) {
    strength = 'strong';
  } else {
    strength = 'excellent';
  }

  // Generate contextual suggestions
  const suggestions: string[] = [];
  if (!requirements.minLength) {
    suggestions.push(`Mindestens ${PASSWORD_MIN_LENGTH} Zeichen verwenden`);
  }
  if (!requirements.hasUppercase) {
    suggestions.push('Großbuchstaben hinzufügen (A-Z)');
  }
  if (!requirements.hasLowercase) {
    suggestions.push('Kleinbuchstaben hinzufügen (a-z)');
  }
  if (!requirements.hasNumbers) {
    suggestions.push('Zahlen hinzufügen (0-9)');
  }
  if (!requirements.hasSpecialChars) {
    suggestions.push('Sonderzeichen hinzufügen (!@#$%^&*)');
  }
  if (!requirements.noCommonPatterns) {
    suggestions.push('Vermeiden Sie häufige Muster oder Wörter');
  }
  if (!requirements.noPersonalInfo) {
    suggestions.push('Vermeiden Sie persönliche Informationen');
  }
  if (password.length < 12) {
    suggestions.push('Längere Passwörter sind sicherer (12+ Zeichen)');
  }

  return {
    strength,
    score: Math.min(score, 7), // Cap at 7 for UI purposes
    requirements,
    suggestions,
    isValid: basicScore >= 5 && requirements.minLength, // Stricter validation
    entropy,
  };
};

/**
 * Validates password confirmation
 */
export const validatePasswordConfirmation = (
  password: string,
  confirmPassword: string
): ValidationError | null => {
  if (!confirmPassword.trim()) {
    return { field: 'confirmPassword', message: 'Passwort-Bestätigung ist erforderlich' };
  }
  
  if (password !== confirmPassword) {
    return { field: 'confirmPassword', message: 'Passwörter stimmen nicht überein' };
  }
  
  return null;
};

/**
 * Validates login credentials
 */
export const validateLoginCredentials = (
  emailOrName: string,
  password: string
): ValidationError[] => {
  const errors: ValidationError[] = [];
  
  if (!emailOrName.trim()) {
    errors.push({ field: 'emailOrName', message: 'E-Mail oder Name ist erforderlich' });
  }
  
  if (!password.trim()) {
    errors.push({ field: 'password', message: 'Passwort ist erforderlich' });
  }
  
  return errors;
};

/**
 * Validates registration form data
 */
export const validateRegistrationData = (
  name: string,
  email: string,
  password: string,
  confirmPassword: string
): ValidationError[] => {
  const errors: ValidationError[] = [];
  
  // Validate name
  const nameError = validateName(name);
  if (nameError) errors.push(nameError);
  
  // Validate email
  const emailError = validateEmail(email);
  if (emailError) errors.push(emailError);
  
  // Validate password
  const passwordValidation = validatePassword(password);
  if (passwordValidation.strength === 'weak') {
    errors.push({ 
      field: 'password', 
      message: 'Passwort ist zu schwach. Bitte folgen Sie den Empfehlungen.' 
    });
  }
  
  // Validate password confirmation
  const confirmError = validatePasswordConfirmation(password, confirmPassword);
  if (confirmError) errors.push(confirmError);
  
  return errors;
};

/**
 * Debounced validation function
 */
export const createDebouncedValidator = <T extends any[]>(
  validator: (...args: T) => ValidationError | ValidationError[] | null,
  delay: number = 300
) => {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: T): Promise<ValidationError | ValidationError[] | null> => {
    return new Promise((resolve) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        resolve(validator(...args));
      }, delay);
    });
  };
};

/**
 * Enhanced real-time form validation helper
 */
export const createFormValidator = (): FormValidator => {
  const touched: Record<string, boolean> = {};
  const errors: Record<string, ValidationError | null> = {};

  const setTouched = (field: string) => {
    touched[field] = true;
  };

  const setError = (error: ValidationError) => {
    errors[error.field] = error;
  };

  const clearError = (field: string) => {
    errors[field] = null;
  };

  const getFieldError = (field: string): ValidationError | null => {
    return touched[field] ? errors[field] || null : null;
  };

  const hasErrors = (): boolean => {
    return Object.values(errors).some(error => error !== null);
  };

  const reset = () => {
    Object.keys(touched).forEach(key => delete touched[key]);
    Object.keys(errors).forEach(key => delete errors[key]);
  };

  const validateField = (field: string, value: string): ValidationError | null => {
    let error: ValidationError | null = null;

    switch (field) {
      case 'email':
        error = validateEmail(value);
        break;
      case 'name':
        error = validateName(value);
        break;
      case 'password':
        const passwordValidation = validatePassword(value);
        if (!passwordValidation.isValid) {
          error = { field: 'password', message: 'Passwort erfüllt nicht die Sicherheitsanforderungen' };
        }
        break;
      case 'emailOrName':
        if (!value.trim()) {
          error = { field: 'emailOrName', message: 'E-Mail oder Name ist erforderlich' };
        }
        break;
    }

    if (error) {
      setError(error);
    } else {
      clearError(field);
    }

    return error;
  };

  const validateAll = (): boolean => {
    return !hasErrors();
  };

  return {
    errors,
    touched,
    setError,
    clearError,
    setTouched,
    getFieldError,
    hasErrors,
    reset,
    validateField,
    validateAll,
  };
};
