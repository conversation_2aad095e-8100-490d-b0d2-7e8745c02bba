import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useData } from '@/context/data';
import { HouseType } from '@/types';
import { toast } from 'sonner';
import AddressAutocomplete from './AddressAutocomplete';
import { 
  getCityByPostalCode, 
  getPostalCodeSuggestions,
  getStreetSuggestions 
} from '@/services/address';

interface AddressFormProps {
  houseType: HouseType;
}

const AddressForm: React.FC<AddressFormProps> = ({ houseType }) => {
  const navigate = useNavigate();
  const { addAddress, addHouse, addresses } = useData();
  
  const [zipCode, setZipCode] = useState('');
  const [zipCodeSuggestions, setZipCodeSuggestions] = useState<string[]>([]);
  
  const [city, setCity] = useState('');
  
  const [street, setStreet] = useState('');
  const [streetSuggestions, setStreetSuggestions] = useState<string[]>([]);
  
  const [houseNumber, setHouseNumber] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Update city when zip code changes
  useEffect(() => {
    if (zipCode.length === 5) {
      const matchedCity = getCityByPostalCode(zipCode);
      if (matchedCity) {
        setCity(matchedCity);
      }
    }
  }, [zipCode]);

  // Update street suggestions when city changes
  useEffect(() => {
    if (city) {
      setStreetSuggestions(getStreetSuggestions(city, street));
    }
  }, [city, street]);

  // Update zip code suggestions when typing
  useEffect(() => {
    if (zipCode.length >= 2) {
      setZipCodeSuggestions(getPostalCodeSuggestions(zipCode));
    } else {
      setZipCodeSuggestions([]);
    }
  }, [zipCode]);

  const handleZipCodeChange = (value: string) => {
    setZipCode(value);
    if (value.length !== 5) {
      setCity('');
    }
  };

  const validateForm = () => {
    if (!zipCode || zipCode.length < 4) {
      toast.error('Bitte geben Sie eine gültige Postleitzahl ein');
      return false;
    }
    if (!city) {
      toast.error('Bitte geben Sie eine Stadt ein');
      return false;
    }
    if (!street) {
      toast.error('Bitte geben Sie eine Straße ein');
      return false;
    }
    if (!houseNumber) {
      toast.error('Bitte geben Sie eine Hausnummer ein');
      return false;
    }
    return true;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    setIsSubmitting(true);
    
    try {
      // Check if address already exists
      let addressId: string;
      const existingAddress = addresses.find(
        addr => addr.zipCode === zipCode && 
               addr.city.toLowerCase() === city.toLowerCase() && 
               addr.street.toLowerCase() === street.toLowerCase()
      );
      
      if (existingAddress) {
        addressId = existingAddress.id;
      } else {
        // Create new address
        const newAddress = addAddress({
          zipCode,
          city,
          street
        });
        addressId = newAddress.id;
      }
      
      // Create house
      const house = addHouse({
        addressId,
        houseNumber,
        type: houseType,
        // In a real app, we'd get actual coordinates, using mock values for now
        latitude: 48.1351 + (Math.random() * 0.01),
        longitude: 11.5820 + (Math.random() * 0.01),
      });
      
      toast.success('Adresse erfolgreich hinzugefügt');
      
      // Navigate to visit status page
      if (houseType === 'EFH') {
        navigate(`/visit-status/${house.id}`);
      } else {
        navigate(`/mfh/${house.id}`);
      }
    } catch (error) {
      toast.error('Fehler beim Speichern der Adresse');
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{houseType} - Adresse eingeben</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <AddressAutocomplete
              id="zipCode"
              label="Postleitzahl"
              value={zipCode}
              onChange={handleZipCodeChange}
              suggestions={zipCodeSuggestions}
              placeholder="PLZ"
              required
            />
            <div className="space-y-2">
              <Label htmlFor="city">Stadt</Label>
              <Input
                id="city"
                value={city}
                onChange={(e) => setCity(e.target.value)}
                placeholder="Stadt"
                required
                readOnly={!!city}
                className={city ? "bg-gray-50" : ""}
              />
            </div>
          </div>

          <AddressAutocomplete
            id="street"
            label="Straße"
            value={street}
            onChange={setStreet}
            suggestions={streetSuggestions}
            placeholder="Straße"
            required
          />

          <div className="space-y-2">
            <Label htmlFor="houseNumber">Hausnummer</Label>
            <Input
              id="houseNumber"
              value={houseNumber}
              onChange={(e) => setHouseNumber(e.target.value)}
              placeholder="Hausnummer"
              required
            />
          </div>
          
          <CardFooter className="px-0 pt-4">
            <Button
              type="submit"
              className="w-full"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Speichern...' : 'Weiter'}
            </Button>
          </CardFooter>
        </form>
      </CardContent>
    </Card>
  );
};

export default AddressForm;
