import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AuthProvider, useAuth } from '@/context/auth/AuthContext';
import { mockLocalStorage } from '@/test/test-utils';
import { RegisterData } from '@/context/auth/types';

// Mock toast notifications
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn(),
  },
}));

// Mock haptic feedback
Object.defineProperty(navigator, 'vibrate', {
  writable: true,
  value: vi.fn(),
});

// Test component to access auth context
const TestAuthComponent = () => {
  const {
    user,
    login,
    register,
    logout,
    isLoading,
    error,
    clearError,
    isAuthenticated,
    sessionExpiry,
    refreshSession,
  } = useAuth();

  const handleLogin = async () => {
    const result = await login('<EMAIL>', 'test123', true);
    return result;
  };

  const handleRegister = async () => {
    const registerData: RegisterData = {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'SecurePass123!',
      confirmPassword: 'SecurePass123!'
    };
    const result = await register(registerData);
    return result;
  };

  const handleRefreshSession = async () => {
    const result = await refreshSession();
    return result;
  };

  return (
    <div>
      <div data-testid="user-info">
        {user ? `Logged in as: ${user.name}` : 'Not logged in'}
      </div>
      <div data-testid="loading-state">
        {isLoading ? 'Loading...' : 'Ready'}
      </div>
      <div data-testid="auth-status">
        {isAuthenticated ? 'Authenticated' : 'Not authenticated'}
      </div>
      <div data-testid="session-expiry">
        {sessionExpiry ? sessionExpiry.toISOString() : 'No session'}
      </div>
      {error && (
        <div data-testid="error-message">
          {error.message} (Type: {error.type})
        </div>
      )}
      <button onClick={handleLogin} data-testid="login-button">
        Login
      </button>
      <button onClick={handleRegister} data-testid="register-button">
        Register
      </button>
      <button onClick={logout} data-testid="logout-button">
        Logout
      </button>
      <button onClick={clearError} data-testid="clear-error-button">
        Clear Error
      </button>
      <button onClick={handleRefreshSession} data-testid="refresh-session-button">
        Refresh Session
      </button>
    </div>
  );
};

describe('Enhanced Authentication Flow', () => {
  beforeEach(() => {
    mockLocalStorage();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Login Flow', () => {
    it('should successfully login with valid credentials and return AuthResult', async () => {
      const user = userEvent.setup();
      
      render(
        <AuthProvider>
          <TestAuthComponent />
        </AuthProvider>
      );

      // Wait for initial loading to complete
      await waitFor(() => {
        expect(screen.getByTestId('loading-state')).toHaveTextContent('Ready');
      });

      // Click login button
      await user.click(screen.getByTestId('login-button'));

      // Wait for login to complete
      await waitFor(() => {
        expect(screen.getByTestId('user-info')).toHaveTextContent('Logged in as: Test Berater');
        expect(screen.getByTestId('auth-status')).toHaveTextContent('Authenticated');
      });

      // Check session expiry is set
      const sessionExpiry = screen.getByTestId('session-expiry').textContent;
      expect(sessionExpiry).not.toBe('No session');
    });

    it('should handle login failure with proper error handling', async () => {
      const user = userEvent.setup();
      
      render(
        <AuthProvider>
          <TestAuthComponent />
        </AuthProvider>
      );

      // Mock login with invalid credentials
      const TestComponentWithInvalidLogin = () => {
        const { login } = useAuth();
        
        const handleInvalidLogin = async () => {
          await login('<EMAIL>', 'wrongpassword', false);
        };

        return (
          <button onClick={handleInvalidLogin} data-testid="invalid-login-button">
            Invalid Login
          </button>
        );
      };

      render(
        <AuthProvider>
          <TestComponentWithInvalidLogin />
          <TestAuthComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('loading-state')).toHaveTextContent('Ready');
      });

      await user.click(screen.getByTestId('invalid-login-button'));

      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toHaveTextContent('Ungültige Anmeldeinformationen');
        expect(screen.getByTestId('auth-status')).toHaveTextContent('Not authenticated');
      });
    });

    it('should support remember me functionality', async () => {
      const user = userEvent.setup();
      
      render(
        <AuthProvider>
          <TestAuthComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('loading-state')).toHaveTextContent('Ready');
      });

      await user.click(screen.getByTestId('login-button'));

      await waitFor(() => {
        expect(screen.getByTestId('auth-status')).toHaveTextContent('Authenticated');
      });

      // Check that remember me is stored in localStorage
      expect(localStorage.getItem('rememberMe')).toBe('true');
      expect(localStorage.getItem('sessionExpiry')).toBeTruthy();
    });
  });

  describe('Registration Flow', () => {
    it('should successfully register a new user and auto-login', async () => {
      const user = userEvent.setup();
      
      render(
        <AuthProvider>
          <TestAuthComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('loading-state')).toHaveTextContent('Ready');
      });

      await user.click(screen.getByTestId('register-button'));

      await waitFor(() => {
        expect(screen.getByTestId('user-info')).toHaveTextContent('Logged in as: Test User');
        expect(screen.getByTestId('auth-status')).toHaveTextContent('Authenticated');
      });

      // Check that user is automatically logged in after registration
      expect(localStorage.getItem('currentUser')).toBeTruthy();
      expect(localStorage.getItem('sessionExpiry')).toBeTruthy();
    });

    it('should handle registration with existing email', async () => {
      const user = userEvent.setup();
      
      const TestComponentWithExistingEmail = () => {
        const { register } = useAuth();
        
        const handleRegisterExisting = async () => {
          const registerData: RegisterData = {
            name: 'Another User',
            email: '<EMAIL>', // This email already exists in mock data
            password: 'SecurePass123!',
            confirmPassword: 'SecurePass123!'
          };
          await register(registerData);
        };

        return (
          <button onClick={handleRegisterExisting} data-testid="register-existing-button">
            Register Existing
          </button>
        );
      };

      render(
        <AuthProvider>
          <TestComponentWithExistingEmail />
          <TestAuthComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('loading-state')).toHaveTextContent('Ready');
      });

      await user.click(screen.getByTestId('register-existing-button'));

      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toHaveTextContent('Diese E-Mail-Adresse wird bereits verwendet');
      });
    });
  });

  describe('Session Management', () => {
    it('should refresh session when close to expiry', async () => {
      const user = userEvent.setup();
      
      render(
        <AuthProvider>
          <TestAuthComponent />
        </AuthProvider>
      );

      // Login first
      await waitFor(() => {
        expect(screen.getByTestId('loading-state')).toHaveTextContent('Ready');
      });

      await user.click(screen.getByTestId('login-button'));

      await waitFor(() => {
        expect(screen.getByTestId('auth-status')).toHaveTextContent('Authenticated');
      });

      // Test session refresh
      await user.click(screen.getByTestId('refresh-session-button'));

      // Session should still be valid
      await waitFor(() => {
        expect(screen.getByTestId('auth-status')).toHaveTextContent('Authenticated');
      });
    });

    it('should handle session expiry correctly', async () => {
      // Mock expired session
      const expiredDate = new Date(Date.now() - 1000 * 60 * 60); // 1 hour ago
      localStorage.setItem('sessionExpiry', expiredDate.toISOString());
      localStorage.setItem('currentUser', JSON.stringify({ id: '1', name: 'Test User' }));

      render(
        <AuthProvider>
          <TestAuthComponent />
        </AuthProvider>
      );

      // Should not be authenticated due to expired session
      await waitFor(() => {
        expect(screen.getByTestId('auth-status')).toHaveTextContent('Not authenticated');
      });
    });
  });

  describe('Error Handling', () => {
    it('should clear errors when clearError is called', async () => {
      const user = userEvent.setup();
      
      render(
        <AuthProvider>
          <TestAuthComponent />
        </AuthProvider>
      );

      // First trigger an error by attempting invalid login
      const TestComponentWithError = () => {
        const { login } = useAuth();
        
        const handleErrorLogin = async () => {
          await login('', '', false); // Empty credentials should trigger validation error
        };

        return (
          <button onClick={handleErrorLogin} data-testid="error-login-button">
            Error Login
          </button>
        );
      };

      render(
        <AuthProvider>
          <TestComponentWithError />
          <TestAuthComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('loading-state')).toHaveTextContent('Ready');
      });

      await user.click(screen.getByTestId('error-login-button'));

      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toBeInTheDocument();
      });

      // Clear the error
      await user.click(screen.getByTestId('clear-error-button'));

      await waitFor(() => {
        expect(screen.queryByTestId('error-message')).not.toBeInTheDocument();
      });
    });
  });

  describe('Logout Flow', () => {
    it('should successfully logout and clear session data', async () => {
      const user = userEvent.setup();
      
      render(
        <AuthProvider>
          <TestAuthComponent />
        </AuthProvider>
      );

      // Login first
      await waitFor(() => {
        expect(screen.getByTestId('loading-state')).toHaveTextContent('Ready');
      });

      await user.click(screen.getByTestId('login-button'));

      await waitFor(() => {
        expect(screen.getByTestId('auth-status')).toHaveTextContent('Authenticated');
      });

      // Now logout
      await user.click(screen.getByTestId('logout-button'));

      await waitFor(() => {
        expect(screen.getByTestId('auth-status')).toHaveTextContent('Not authenticated');
        expect(screen.getByTestId('user-info')).toHaveTextContent('Not logged in');
        expect(screen.getByTestId('session-expiry')).toHaveTextContent('No session');
      });

      // Check that localStorage is cleared
      expect(localStorage.getItem('currentUser')).toBeNull();
      expect(localStorage.getItem('sessionExpiry')).toBeNull();
      expect(localStorage.getItem('rememberMe')).toBeNull();
    });
  });
});
