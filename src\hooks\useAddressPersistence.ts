import { useState, useEffect } from 'react';

interface PersistedAddressData {
  zipCode: string;
  city: string;
  street: string;
  lastUsed: number;
}

const STORAGE_KEY = 'visit-flow-last-address';
const PERSISTENCE_DURATION = 24 * 60 * 60 * 1000; // 24 Stunden

export const useAddressPersistence = () => {
  const [persistedAddress, setPersistedAddress] = useState<PersistedAddressData | null>(null);

  // Lade gespeicherte Adresse beim Start
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const data: PersistedAddressData = JSON.parse(stored);
        
        // Prüfe ob Daten noch gültig sind (nicht älter als 24h)
        const now = Date.now();
        if (now - data.lastUsed < PERSISTENCE_DURATION) {
          setPersistedAddress(data);
        } else {
          // Alte Daten löschen
          localStorage.removeItem(STORAGE_KEY);
        }
      }
    } catch (error) {
      console.error('<PERSON><PERSON> beim <PERSON> der gespeicherten Adresse:', error);
      localStorage.removeItem(STORAGE_KEY);
    }
  }, []);

  // Speichere Adresse
  const saveAddress = (zipCode: string, city: string, street: string) => {
    if (!zipCode || !city || !street) return;
    
    const data: PersistedAddressData = {
      zipCode,
      city,
      street,
      lastUsed: Date.now()
    };
    
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
      setPersistedAddress(data);
    } catch (error) {
      console.error('Fehler beim Speichern der Adresse:', error);
    }
  };

  // Lösche gespeicherte Adresse
  const clearAddress = () => {
    try {
      localStorage.removeItem(STORAGE_KEY);
      setPersistedAddress(null);
    } catch (error) {
      console.error('Fehler beim Löschen der gespeicherten Adresse:', error);
    }
  };

  // Prüfe ob aktuelle Adresse mit gespeicherter übereinstimmt
  const isCurrentAddress = (zipCode: string, city: string, street: string) => {
    if (!persistedAddress) return false;
    
    return (
      persistedAddress.zipCode === zipCode &&
      persistedAddress.city.toLowerCase() === city.toLowerCase() &&
      persistedAddress.street.toLowerCase() === street.toLowerCase()
    );
  };

  return {
    persistedAddress,
    saveAddress,
    clearAddress,
    isCurrentAddress,
    hasPersisted: !!persistedAddress
  };
};
