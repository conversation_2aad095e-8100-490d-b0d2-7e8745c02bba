// Extended Baden-Württemberg postal code database
export const bwPostalCodes: Record<string, string> = {
  // Stuttgart (expanding postal codes)
  "70173": "Stuttgart",
  "70174": "Stuttgart",
  "70175": "Stuttgart",
  "70176": "Stuttgart",
  "70177": "Stuttgart",
  "70178": "Stuttgart",
  "70179": "Stuttgart",
  "70180": "Stuttgart",
  "70181": "Stuttgart",
  "70182": "Stuttgart",
  "70183": "Stuttgart",
  "70184": "Stuttgart",
  "70185": "Stuttgart",
  "70186": "Stuttgart",
  "70187": "Stuttgart",
  "70188": "Stuttgart",
  "70189": "Stuttgart",
  "70190": "Stuttgart",
  "70191": "Stuttgart",
  "70192": "Stuttgart",
  "70193": "Stuttgart",
  "70194": "Stuttgart",
  "70195": "Stuttgart",
  "70196": "Stuttgart",
  "70197": "Stuttgart",
  "70198": "Stuttgart",
  "70199": "Stuttgart",
  
  // Karlsruhe (complete)
  "76131": "Karlsruhe",
  "76133": "Karlsruhe",
  "76135": "Karlsruhe",
  "76137": "Karlsruhe",
  "76139": "Karlsruhe",
  "76227": "Karlsruhe",
  "76228": "Karlsruhe",
  "76229": "<PERSON>sruhe",
  
  // Mannheim (complete with additional codes)
  "68159": "Mannheim",
  "68161": "Mannheim",
  "68163": "Mannheim",
  "68165": "Mannheim",
  "68167": "Mannheim",
  "68169": "Mannheim",
  "68199": "Mannheim",
  "68219": "Mannheim",
  "68229": "Mannheim",
  "68239": "Mannheim",
  "68259": "Mannheim",
  "68305": "Mannheim",
  "68307": "Mannheim",
  "68309": "Mannheim",
  
  // Freiburg (complete)
  "79098": "Freiburg",
  "79100": "Freiburg",
  "79102": "Freiburg",
  "79104": "Freiburg",
  "79106": "Freiburg",
  "79108": "Freiburg",
  "79110": "Freiburg",
  "79112": "Freiburg",
  "79114": "Freiburg",
  "79115": "Freiburg",
  "79117": "Freiburg",
  
  // Heidelberg
  "69115": "Heidelberg",
  "69116": "Heidelberg",
  "69117": "Heidelberg",
  "69118": "Heidelberg",
  "69120": "Heidelberg",
  "69121": "Heidelberg",
  "69123": "Heidelberg",
  "69124": "Heidelberg",
  "69126": "Heidelberg",
  
  // Heilbronn
  "74072": "Heilbronn",
  "74074": "Heilbronn",
  "74076": "Heilbronn",
  "74078": "Heilbronn",
  "74080": "Heilbronn",
  "74081": "Heilbronn",
  
  // Ulm (erweitert)
  "89073": "Ulm",
  "89075": "Ulm",
  "89077": "Ulm",
  "89079": "Ulm",
  "89081": "Ulm",
  "89082": "Ulm",
  "89083": "Ulm",
  "89085": "Ulm",
  "89087": "Ulm",
  "89090": "Ulm",
  
  // Ulm Umgebung (neu)
  "89134": "Blaustein",
  "89143": "Blaubeuren",
  "89150": "Laichingen",
  "89160": "Dornstadt",
  "89168": "Niederstotzingen",
  "89171": "Illerkirchberg",
  "89173": "Lonsee",
  "89180": "Berghülen",
  "89188": "Merklingen",
  "89191": "Nellingen",
  "89194": "Schnürpflingen",
  "89197": "Weidenstetten",
  "89198": "Westerstetten",
  "89275": "Elchingen",
  "89278": "Nersingen",
  "89284": "Pfaffenhofen an der Roth",
  "89287": "Bellenberg",
  
  // Leutkirch (neu hinzugefügt)
  "88299": "Leutkirch",
  
  // Pforzheim
  "75172": "Pforzheim",
  "75173": "Pforzheim",
  "75175": "Pforzheim",
  "75177": "Pforzheim",
  "75179": "Pforzheim",
  "75180": "Pforzheim",
  "75181": "Pforzheim",
  
  // München PLZ (neu)
  "80331": "München",
  "80333": "München",
  "80335": "München",
  "80336": "München",
  "80337": "München",
  "80339": "München",
  "80469": "München",
  "80538": "München",
  "80539": "München",
  "80634": "München",
  "80636": "München",
  "80637": "München",
  "80638": "München",
  "80639": "München",
  "80686": "München",
  "80687": "München",
  "80689": "München",
  "80796": "München",
  "80797": "München",
  "80798": "München",
  "80799": "München",
  "80801": "München",
  "80802": "München",
  "80803": "München",
  "80804": "München",
  "80805": "München",
  "80807": "München",
  "80809": "München",
  "80933": "München",
  "80935": "München",
  "80937": "München",
  "80939": "München",
  "80992": "München",
  "80993": "München",
  "80995": "München",
  "80997": "München",
  "80999": "München",
  "81241": "München",
  "81243": "München",
  "81245": "München",
  "81247": "München",
  "81249": "München",
  "81369": "München",
  "81371": "München",
  "81373": "München",
  "81375": "München",
  "81377": "München",
  "81379": "München",
  
  // Smaller cities
  "76297": "Stutensee",
  "76275": "Ettlingen",
  "76287": "Rheinstetten",
  "79539": "Lörrach",
  "79540": "Lörrach",
  "79541": "Lörrach",
  "79618": "Rheinfelden",
  "88045": "Friedrichshafen",
  "88046": "Friedrichshafen",
  "88048": "Friedrichshafen",
  "88212": "Ravensburg",
  "88213": "Ravensburg",
  "88214": "Ravensburg",
  "71634": "Ludwigsburg",
  "71636": "Ludwigsburg",
  "71638": "Ludwigsburg",
  "71640": "Ludwigsburg",
  "72070": "Tübingen",
  "72072": "Tübingen",
  "72074": "Tübingen",
  "72076": "Tübingen",
  "78462": "Konstanz",
  "78464": "Konstanz",
  "78467": "Konstanz",
  
  // Reutlingen
  "72760": "Reutlingen",
  "72762": "Reutlingen",
  "72764": "Reutlingen",
  "72766": "Reutlingen",
  "72768": "Reutlingen",
  "72770": "Reutlingen",
  
  // Esslingen
  "73728": "Esslingen",
  "73730": "Esslingen",
  "73732": "Esslingen",
  "73734": "Esslingen",
  "73736": "Esslingen",
  "73738": "Esslingen",
  "73740": "Esslingen",
  "73742": "Esslingen",
  "73744": "Esslingen",
  "73746": "Esslingen",
  
  // Other cities
  "73033": "Göppingen",
  "73035": "Göppingen",
  "73037": "Göppingen",
  "73614": "Schorndorf",
  "73525": "Schwäbisch Gmünd",
  "74321": "Bietigheim-Bissingen",
  "74172": "Neckarsulm",
  "78048": "Villingen-Schwenningen",
  "78050": "Villingen-Schwenningen",
  "78052": "Villingen-Schwenningen",
  "78054": "Villingen-Schwenningen",
};

// Function to get city by postal code
export const getCityByPostalCode = (postalCode: string): string | null => {
  return bwPostalCodes[postalCode] || null;
};

// Function to get all available postal codes for a specific city
export const getPostalCodesByCity = (city: string): string[] => {
  return Object.entries(bwPostalCodes)
    .filter(([_, cityName]) => cityName === city)
    .map(([code, _]) => code);
};

// Function to get postal code suggestions based on input
export const getPostalCodeSuggestions = (query: string): string[] => {
  if (!query || query.length < 2) return [];
  
  return Object.keys(bwPostalCodes)
    .filter(code => code.startsWith(query))
    .slice(0, 15); // Show more suggestions
};

// Function to get all cities in Baden-Württemberg
export const getAllCities = (): string[] => {
  return [...new Set(Object.values(bwPostalCodes))].sort();
};
