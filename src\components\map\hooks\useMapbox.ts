
import { useEffect, useState, useRef } from 'react';
import { useData } from '@/context/data';
import { toast } from 'sonner';
import mapboxgl from 'mapbox-gl';
import { GeocodingService, GeocodeResult } from '@/services/geocoding/geocodingService';
import { RoutingService, RouteCoordinate, RouteResponse } from '@/services/routing/routingService';

export const useMapbox = (mapboxToken: string) => {
  const { houses, visits, addresses } = useData();
  const [mapLoaded, setMapLoaded] = useState(false);
  const [currentLocation, setCurrentLocation] = useState<{ lat: number; lng: number } | null>(null);
  const [isTracking, setIsTracking] = useState(false);
  const [showFiltered, setShowFiltered] = useState(false);
  const [routeInfo, setRouteInfo] = useState<{distance: string; duration: string; destination: string} | null>(null);
  const [hasRoute, setHasRoute] = useState(false);
  
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const watchId = useRef<number | null>(null);
  const userMarker = useRef<mapboxgl.Marker | null>(null);
  const houseMarkers = useRef<mapboxgl.Marker[]>([]);
  const routeLayer = useRef<string | null>(null);
  const geocodingService = useRef<GeocodingService | null>(null);
  const routingService = useRef<RoutingService | null>(null);

  // Initialize services when token is available
  useEffect(() => {
    if (mapboxToken) {
      geocodingService.current = new GeocodingService(mapboxToken);
      routingService.current = new RoutingService(mapboxToken);
    }
  }, [mapboxToken]);

  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          setCurrentLocation({ lat: latitude, lng: longitude });
          
          if (map.current) {
            map.current.flyTo({
              center: [longitude, latitude],
              zoom: 15,
              duration: 2000
            });

            // Update or create user marker
            if (userMarker.current) {
              userMarker.current.setLngLat([longitude, latitude]);
            } else {
              userMarker.current = new mapboxgl.Marker({
                color: '#3b82f6',
                scale: 1.2
              })
              .setLngLat([longitude, latitude])
              .addTo(map.current);
            }
          }
          
          toast.success('Standort gefunden');
        },
        (error) => {
          console.error('Error getting location:', error);
          toast.error('Standort konnte nicht ermittelt werden');
        },
        { enableHighAccuracy: true, timeout: 10000, maximumAge: 300000 }
      );
    } else {
      toast.error('Geolocation wird von diesem Browser nicht unterstützt');
    }
  };

  const startTracking = () => {
    if (navigator.geolocation) {
      watchId.current = navigator.geolocation.watchPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          setCurrentLocation({ lat: latitude, lng: longitude });
          
          if (map.current && userMarker.current) {
            userMarker.current.setLngLat([longitude, latitude]);
            
            if (isTracking) {
              map.current.easeTo({
                center: [longitude, latitude],
                duration: 1000
              });
            }
          }
        },
        (error) => {
          console.error('Error tracking location:', error);
          toast.error('Position konnte nicht verfolgt werden');
          setIsTracking(false);
        },
        { 
          enableHighAccuracy: true, 
          timeout: 10000, 
          maximumAge: 30000 
        }
      );
      toast.success('Positionsverfolgung aktiviert');
    } else {
      toast.error('Geolocation wird von diesem Browser nicht unterstützt');
    }
  };

  const stopTracking = () => {
    if (watchId.current) {
      navigator.geolocation.clearWatch(watchId.current);
      watchId.current = null;
      toast.success('Positionsverfolgung deaktiviert');
    }
  };

  const toggleTracking = (enabled: boolean) => {
    setIsTracking(enabled);
    if (enabled) {
      startTracking();
    } else {
      stopTracking();
    }
  };

  const toggleFilter = () => {
    setShowFiltered(!showFiltered);
    updateHouseMarkers();
  };

  const updateHouseMarkers = () => {
    if (!map.current) return;

    // Clear existing markers
    houseMarkers.current.forEach(marker => marker.remove());
    houseMarkers.current = [];

    // Filter houses based on showFiltered state
    const housesToShow = showFiltered 
      ? houses.filter(house => {
          const houseVisits = visits.filter(v => v.houseId === house.id);
          return !houseVisits.some(v => 
            v.status === 'Angetroffen → Sale' || 
            v.status === 'Angetroffen → Kein Interesse'
          );
        })
      : houses;

    // Add markers for filtered houses
    housesToShow.forEach(async (house) => {
      const address = addresses.find(a => a.id === house.addressId);
      if (!address) return;

      const houseVisits = visits.filter(v => v.houseId === house.id);
      const isCompleted = houseVisits.some(v => 
        v.status === 'Angetroffen → Sale' || 
        v.status === 'Angetroffen → Kein Interesse'
      );

      const marker = new mapboxgl.Marker({
        color: isCompleted ? '#22c55e' : '#ef4444'
      })
      .setPopup(
        new mapboxgl.Popup({ offset: 25 })
          .setHTML(`
            <div>
              <h3 style="margin: 0; font-weight: bold;">${address.street} ${house.houseNumber}</h3>
              <p style="margin: 5px 0;">${address.zipCode} ${address.city}</p>
              <p style="margin: 5px 0; color: ${isCompleted ? '#22c55e' : '#ef4444'};">
                Status: ${isCompleted ? 'Abgeschlossen' : 'Offen'}
              </p>
            </div>
          `)
      );

      // Try to geocode the address
      if (geocodingService.current) {
        const fullAddress = `${address.street} ${house.houseNumber}, ${address.zipCode} ${address.city}, Deutschland`;
        const result = await geocodingService.current.geocodeAddress(fullAddress);
        
        if (result) {
          marker.setLngLat([result.longitude, result.latitude]);
        } else {
          // Fallback to random position in Germany
          const lat = 51.1657 + (Math.random() - 0.5) * 2;
          const lng = 10.4515 + (Math.random() - 0.5) * 4;
          marker.setLngLat([lng, lat]);
        }
      } else {
        // Fallback positioning
        const lat = 51.1657 + (Math.random() - 0.5) * 2;
        const lng = 10.4515 + (Math.random() - 0.5) * 4;
        marker.setLngLat([lng, lat]);
      }

      marker.addTo(map.current!);
      houseMarkers.current.push(marker);
    });
  };

  const routeToNearest = async () => {
    if (!currentLocation || !routingService.current || !map.current) {
      toast.error('Position oder Routing-Service nicht verfügbar');
      return;
    }

    // Find nearest pending house
    const pendingHouses = houses.filter(house => {
      const houseVisits = visits.filter(v => v.houseId === house.id);
      return !houseVisits.some(v => 
        v.status === 'Angetroffen → Sale' || 
        v.status === 'Angetroffen → Kein Interesse'
      );
    });

    if (pendingHouses.length === 0) {
      toast.info('Keine offenen Besuche verfügbar');
      return;
    }

    // For demo purposes, select the first pending house
    const targetHouse = pendingHouses[0];
    const targetAddress = addresses.find(a => a.id === targetHouse.addressId);
    
    if (!targetAddress) return;

    // Use a sample coordinate for demo (in real app, would geocode first)
    const targetCoord: RouteCoordinate = {
      longitude: 10.4515 + (Math.random() - 0.5) * 0.1,
      latitude: 51.1657 + (Math.random() - 0.5) * 0.1
    };

    const route = await routingService.current.getRoute(
      { longitude: currentLocation.lng, latitude: currentLocation.lat },
      targetCoord
    );

    if (route) {
      setRouteInfo({
        distance: routingService.current.formatDistance(route.distance),
        duration: routingService.current.formatDuration(route.duration),
        destination: `${targetAddress.street} ${targetHouse.houseNumber}`
      });
      setHasRoute(true);
      
      // Add route to map
      if (routeLayer.current) {
        map.current.removeLayer(routeLayer.current);
        map.current.removeSource(routeLayer.current);
      }

      const routeId = 'route-' + Date.now();
      routeLayer.current = routeId;

      map.current.addSource(routeId, {
        type: 'geojson',
        data: {
          type: 'Feature',
          properties: {},
          geometry: {
            type: 'LineString',
            coordinates: route.geometry.map(coord => [coord.longitude, coord.latitude])
          }
        }
      });

      map.current.addLayer({
        id: routeId,
        type: 'line',
        source: routeId,
        layout: {
          'line-join': 'round',
          'line-cap': 'round'
        },
        paint: {
          'line-color': '#8b5cf6',
          'line-width': 4
        }
      });

      toast.success('Route berechnet!');
    } else {
      toast.error('Route konnte nicht berechnet werden');
    }
  };

  const initializeMap = () => {
    if (!mapContainer.current || !mapboxToken) return;

    mapboxgl.accessToken = mapboxToken;
    
    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: 'mapbox://styles/mapbox/streets-v12',
      center: [10.4515, 51.1657],
      zoom: 6
    });

    map.current.addControl(new mapboxgl.NavigationControl(), 'top-right');

    map.current.on('load', () => {
      setMapLoaded(true);
      updateHouseMarkers();
      getCurrentLocation();
    });
  };

  // Update markers when filter changes
  useEffect(() => {
    if (mapLoaded) {
      updateHouseMarkers();
    }
  }, [showFiltered, houses, visits, addresses, mapLoaded]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (watchId.current) {
        navigator.geolocation.clearWatch(watchId.current);
      }
    };
  }, []);

  return {
    mapContainer,
    map: map.current,
    mapLoaded,
    currentLocation,
    isTracking,
    showFiltered,
    routeInfo,
    hasRoute,
    initializeMap,
    getCurrentLocation,
    toggleTracking,
    toggleFilter,
    routeToNearest
  };
};
