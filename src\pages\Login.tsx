
import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import LoginForm from '@/components/auth/LoginForm';
import RegisterForm from '@/components/auth/RegisterForm';
import AuthHeader from '@/components/auth/AuthHeader';
import AuthErrorBoundary from '@/components/auth/AuthErrorBoundary';
import { useAuth } from '@/context/auth';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';

const Login: React.FC = () => {
  const [activeTab, setActiveTab] = useState<"login" | "register">("login");
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');

  const { isAuthenticated, isLoading, refreshSession, sessionExpiry } = useAuth();
  const navigate = useNavigate();

  // Enhanced session management
  const handleSessionRefresh = useCallback(async () => {
    try {
      const isValid = await refreshSession();
      if (isValid && isAuthenticated) {
        navigate('/', { replace: true });
      }
    } catch (error) {
      console.error('Session refresh failed:', error);
    }
  }, [refreshSession, isAuthenticated, navigate]);

  // Check session on mount and periodically
  useEffect(() => {
    if (sessionExpiry) {
      handleSessionRefresh();
    }

    // Set up periodic session check (every 5 minutes)
    const interval = setInterval(() => {
      if (sessionExpiry) {
        handleSessionRefresh();
      }
    }, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, [handleSessionRefresh, sessionExpiry]);

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && !isLoading) {
      navigate('/', { replace: true });
    }
  }, [isAuthenticated, isLoading, navigate]);

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-red-50 flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-12 h-12 border-4 border-red-600 border-t-transparent rounded-full animate-spin"></div>
          <p className="text-gray-600 font-medium">Anmeldung wird überprüft...</p>
        </div>
      </div>
    );
  }

  // Don't render login form if already authenticated
  if (isAuthenticated) {
    return null;
  }

  return (
    <AuthErrorBoundary>
      <div className="min-h-screen w-full flex items-center justify-center bg-gradient-to-br from-red-600 via-red-500 to-pink-600 px-4 py-6 relative overflow-hidden">
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-white bg-opacity-10 rounded-full blur-3xl animate-pulse-slow"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-white bg-opacity-10 rounded-full blur-3xl animate-pulse-slow" style={{animationDelay: '1s'}}></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-white bg-opacity-5 rounded-full blur-3xl animate-pulse-slow" style={{animationDelay: '2s'}}></div>
        </div>

      <div className="w-full max-w-md mx-auto">
        <Card className="w-full border-0 shadow-2xl rounded-3xl overflow-hidden backdrop-blur-sm bg-white/95 animate-fade-in">
          <AuthHeader />
          
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "login" | "register")}>
            <TabsList className="grid grid-cols-2 w-full rounded-none bg-gray-50 h-16">
              <TabsTrigger
                value="login"
                className="data-[state=active]:bg-red-500 data-[state=active]:text-white py-4 rounded-none text-lg font-semibold transition-all duration-300 hover:bg-red-100 min-h-[44px] touch-manipulation"
              >
                Anmelden
              </TabsTrigger>
              <TabsTrigger
                value="register"
                className="data-[state=active]:bg-red-500 data-[state=active]:text-white py-4 rounded-none text-lg font-semibold transition-all duration-300 hover:bg-red-100 min-h-[44px] touch-manipulation"
              >
                Registrieren
              </TabsTrigger>
            </TabsList>

            <CardContent className="px-8 pt-8 pb-10">
              <TabsContent value="login" className="animate-fade-in">
                <LoginForm 
                  email={email}
                  setEmail={setEmail}
                  password={password}
                  setPassword={setPassword}
                />
              </TabsContent>

              <TabsContent value="register" className="animate-fade-in">
                <RegisterForm 
                  name={name}
                  setName={setName}
                  email={email}
                  setEmail={setEmail}
                  password={password}
                  setPassword={setPassword}
                  setActiveTab={setActiveTab}
                />
              </TabsContent>
            </CardContent>
          </Tabs>
        </Card>
      </div>
    </div>
    </AuthErrorBoundary>
  );
};

export default Login;
