import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MapPin, Home, CheckCircle, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SequentialTrackingIndicatorProps {
  currentAddress: {
    zipCode: string;
    city: string;
    street: string;
  };
  completedHouses: string[];
  className?: string;
}

export const SequentialTrackingIndicator: React.FC<SequentialTrackingIndicatorProps> = ({
  currentAddress,
  completedHouses,
  className
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Show indicator when there are completed houses in the same street
    setIsVisible(completedHouses.length > 0);
  }, [completedHouses]);

  if (!isVisible) return null;

  return (
    <Card
      className={cn(
        'mb-4 border-green-200 bg-green-50 animate-in slide-in-from-bottom-2 fade-in duration-250',
        className
      )}
    >
      <CardContent className="p-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-green-100 rounded-lg">
            <MapPin className="h-5 w-5 text-green-600" />
          </div>
          
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-semibold text-green-800">Sequenzielles Tracking</h3>
              <Badge variant="secondary" className="bg-green-100 text-green-700">
                {completedHouses.length} Häuser erfasst
              </Badge>
            </div>
            
            <p className="text-sm text-green-600">
              📍 {currentAddress.street}, {currentAddress.city}
            </p>
            
            <div className="flex items-center gap-4 mt-2">
              <div className="flex items-center gap-1 text-xs text-green-600">
                <CheckCircle className="h-3 w-3" />
                <span>Erfasst: {completedHouses.join(', ')}</span>
              </div>
              
              <div className="flex items-center gap-1 text-xs text-blue-600">
                <Clock className="h-3 w-3" />
                <span>Nächstes Haus bereit</span>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mt-3 p-2 bg-white rounded-lg border border-green-200">
          <p className="text-xs text-green-700 text-center">
            💡 <strong>Tipp:</strong> Hausnummer eingeben und Quick N/A für schnelles Tracking
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

// Hook für sequenzielles Tracking
export const useSequentialTracking = () => {
  const [completedHouses, setCompletedHouses] = useState<string[]>([]);
  const [currentStreet, setCurrentStreet] = useState<string>('');

  const addCompletedHouse = (houseNumber: string, street: string) => {
    if (street !== currentStreet) {
      // Neue Straße - Reset der Liste
      setCompletedHouses([houseNumber]);
      setCurrentStreet(street);
    } else {
      // Gleiche Straße - Hausnummer hinzufügen
      setCompletedHouses(prev => [...prev, houseNumber]);
    }
  };

  const resetTracking = () => {
    setCompletedHouses([]);
    setCurrentStreet('');
  };

  const isSequentialTracking = completedHouses.length > 0;

  return {
    completedHouses,
    currentStreet,
    isSequentialTracking,
    addCompletedHouse,
    resetTracking
  };
};

// Floating Progress Indicator
export const FloatingProgressIndicator: React.FC<{
  completedCount: number;
  isVisible: boolean;
}> = ({ completedCount, isVisible }) => {
  if (!isVisible || completedCount === 0) return null;

  return (
    <div
      className={cn(
        'fixed top-20 right-4 z-40 transition-all duration-300',
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10 pointer-events-none'
      )}
    >
      <div className="bg-green-500 text-white px-3 py-2 rounded-full shadow-lg flex items-center gap-2">
        <Home className="h-4 w-4" />
        <span className="text-sm font-semibold">{completedCount}</span>
      </div>
      <div className="absolute -bottom-2 right-2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-green-500"></div>
    </div>
  );
};

export default SequentialTrackingIndicator;
