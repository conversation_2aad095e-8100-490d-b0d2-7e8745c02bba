
import { House, Visit } from "@/types";

export const getTodaysHousesUtil = (houses: House[]): House[] => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  return houses.filter(house => {
    const createdAt = new Date(house.createdAt);
    return createdAt >= today && createdAt < tomorrow;
  });
};

export const getTodaysVisitsUtil = (visits: Visit[]): Visit[] => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  return visits.filter(visit => {
    const timestamp = new Date(visit.timestamp);
    return timestamp >= today && timestamp < tomorrow;
  });
};

export const getHousesByAddressUtil = (houses: House[], addressId: string): House[] => {
  return houses.filter(house => house.addressId === addressId);
};
