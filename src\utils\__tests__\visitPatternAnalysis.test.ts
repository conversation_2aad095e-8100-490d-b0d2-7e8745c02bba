import { describe, it, expect } from 'vitest';
import { 
  extractTimestampData, 
  analyzeVisitPatterns, 
  generateVisitRecommendations,
  createAddressVisitHistory
} from '../visitPatternAnalysis';
import { Visit, House, VisitPattern } from '@/types';

describe('visitPatternAnalysis', () => {
  const mockAddress = {
    id: 'address-1',
    street: 'Teststraße',
    houseNumber: '123',
    city: 'Stuttgart',
    zipCode: '70173'
  };

  const mockHouse: House = {
    id: 'house-1',
    addressId: 'address-1',
    houseNumber: '123',
    type: 'EFH',
    latitude: 48.7758,
    longitude: 9.1829,
    createdAt: '2024-01-01T10:00:00Z',
    createdBy: 'user-1'
  };

  describe('extractTimestampData', () => {
    it('should extract correct timestamp data for weekday morning', () => {
      const timestamp = '2024-01-15T09:30:00Z'; // Monday morning
      const result = extractTimestampData(timestamp);

      expect(result.dayOfWeek).toBe(1); // Monday
      expect(result.hourOfDay).toBeGreaterThanOrEqual(9); // Account for timezone differences
      expect(result.isWeekend).toBe(false);
      expect(result.seasonalContext).toBe('winter');
    });

    it('should extract correct timestamp data for weekend evening', () => {
      const timestamp = '2024-06-15T19:30:00Z'; // Saturday evening in summer
      const result = extractTimestampData(timestamp);

      expect(result.dayOfWeek).toBe(6); // Saturday
      expect(result.hourOfDay).toBeGreaterThanOrEqual(19); // Account for timezone differences
      expect(result.isWeekend).toBe(true);
      expect(result.seasonalContext).toBe('summer');
    });
  });

  describe('analyzeVisitPatterns', () => {
    it('should return empty patterns for insufficient data', () => {
      const visits: Visit[] = [{
        id: 'visit-1',
        houseId: 'house-1',
        timestamp: '2024-01-15T09:30:00Z',
        userId: 'user-1',
        status: 'N/A'
      }];

      const patterns = analyzeVisitPatterns('address-1', visits, [mockHouse]);
      expect(patterns).toHaveLength(0);
    });

    it('should detect time of day patterns', () => {
      const visits: Visit[] = [
        {
          id: 'visit-1',
          houseId: 'house-1',
          timestamp: '2024-01-15T09:30:00Z',
          userId: 'user-1',
          status: 'N/A'
        },
        {
          id: 'visit-2',
          houseId: 'house-1',
          timestamp: '2024-01-16T10:30:00Z',
          userId: 'user-1',
          status: 'N/A'
        },
        {
          id: 'visit-3',
          houseId: 'house-1',
          timestamp: '2024-01-17T18:30:00Z',
          userId: 'user-1',
          status: 'Angetroffen → Sale'
        }
      ];

      const patterns = analyzeVisitPatterns('address-1', visits, [mockHouse]);
      
      expect(patterns.length).toBeGreaterThan(0);
      const timePattern = patterns.find(p => p.patternType === 'time_of_day');
      expect(timePattern).toBeDefined();
      expect(timePattern?.patternData.failedTimes?.length).toBeGreaterThanOrEqual(2);
      expect(timePattern?.patternData.successfulTimes?.length).toBeGreaterThanOrEqual(1);
    });

    it('should detect shift work patterns', () => {
      const visits: Visit[] = [
        {
          id: 'visit-1',
          houseId: 'house-1',
          timestamp: '2024-01-15T09:30:00Z',
          userId: 'user-1',
          status: 'N/A'
        },
        {
          id: 'visit-2',
          houseId: 'house-1',
          timestamp: '2024-01-16T11:30:00Z',
          userId: 'user-1',
          status: 'N/A'
        },
        {
          id: 'visit-3',
          houseId: 'house-1',
          timestamp: '2024-01-17T14:30:00Z',
          userId: 'user-1',
          status: 'N/A'
        },
        {
          id: 'visit-4',
          houseId: 'house-1',
          timestamp: '2024-01-18T15:30:00Z',
          userId: 'user-1',
          status: 'N/A'
        }
      ];

      const patterns = analyzeVisitPatterns('address-1', visits, [mockHouse]);
      
      const shiftWorkPattern = patterns.find(p => p.patternType === 'shift_work');
      expect(shiftWorkPattern).toBeDefined();
      expect(shiftWorkPattern?.patternData.confidence).toBeGreaterThan(0);
    });
  });

  describe('generateVisitRecommendations', () => {
    it('should generate optimal time recommendations', () => {
      const patterns: VisitPattern[] = [{
        id: 'pattern-1',
        addressId: 'address-1',
        patternType: 'time_of_day',
        patternData: {
          failedTimes: [9, 10, 11],
          successfulTimes: [18, 19],
          confidence: 0.8,
          dataPoints: 5
        },
        lastUpdated: '2024-01-20T10:00:00Z',
        createdAt: '2024-01-20T10:00:00Z'
      }];

      const recommendations = generateVisitRecommendations('address-1', patterns, 3);
      
      expect(recommendations.length).toBeGreaterThan(0);
      
      const optimalTimeRec = recommendations.find(r => r.recommendationType === 'optimal_time');
      expect(optimalTimeRec).toBeDefined();
      expect(optimalTimeRec?.confidence).toBe(0.8);
      expect(optimalTimeRec?.priority).toBe('high');
      expect(optimalTimeRec?.suggestedTimeSlots).toBeDefined();
    });

    it('should generate shift work recommendations', () => {
      const patterns: VisitPattern[] = [{
        id: 'pattern-1',
        addressId: 'address-1',
        patternType: 'shift_work',
        patternData: {
          failedTimes: [9, 10, 11, 14, 15],
          confidence: 0.9,
          dataPoints: 5
        },
        lastUpdated: '2024-01-20T10:00:00Z',
        createdAt: '2024-01-20T10:00:00Z'
      }];

      const recommendations = generateVisitRecommendations('address-1', patterns, 5);
      
      const shiftWorkRec = recommendations.find(r => r.recommendationType === 'general_advice');
      expect(shiftWorkRec).toBeDefined();
      expect(shiftWorkRec?.priority).toBe('high');
      expect(shiftWorkRec?.recommendation).toContain('Arbeitszeiten');
      expect(shiftWorkRec?.suggestedTimeSlots).toBeDefined();
      expect(shiftWorkRec?.suggestedTimeSlots?.length).toBeGreaterThan(0);
    });
  });

  describe('createAddressVisitHistory', () => {
    it('should create comprehensive visit history', () => {
      const visits: Visit[] = [
        {
          id: 'visit-1',
          houseId: 'house-1',
          timestamp: '2024-01-15T09:30:00Z',
          userId: 'user-1',
          status: 'N/A'
        },
        {
          id: 'visit-2',
          houseId: 'house-1',
          timestamp: '2024-01-16T10:30:00Z',
          userId: 'user-1',
          status: 'N/A'
        },
        {
          id: 'visit-3',
          houseId: 'house-1',
          timestamp: '2024-01-17T18:30:00Z',
          userId: 'user-1',
          status: 'Angetroffen → Sale'
        }
      ];

      const history = createAddressVisitHistory('address-1', visits, [mockHouse]);
      
      expect(history.addressId).toBe('address-1');
      expect(history.totalVisits).toBe(3);
      expect(history.failedVisits).toBe(2);
      expect(history.successfulVisits).toBe(1);
      expect(history.patterns.length).toBeGreaterThan(0);
      expect(history.recommendations.length).toBeGreaterThan(0);
      expect(history.lastVisitDate).toBe('2024-01-17T18:30:00Z');
    });

    it('should handle empty visit history', () => {
      const history = createAddressVisitHistory('address-1', [], [mockHouse]);
      
      expect(history.addressId).toBe('address-1');
      expect(history.totalVisits).toBe(0);
      expect(history.failedVisits).toBe(0);
      expect(history.successfulVisits).toBe(0);
      expect(history.patterns).toHaveLength(0);
      expect(history.recommendations).toHaveLength(0);
      expect(history.lastVisitDate).toBe('');
    });
  });
});
