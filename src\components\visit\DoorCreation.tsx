
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Home } from 'lucide-react';

interface DoorCreationProps {
  onCreateDoor: (doorName: string) => void;
  isCreating: boolean;
}

export const DoorCreation: React.FC<DoorCreationProps> = ({
  onCreateDoor,
  isCreating
}) => {
  const [doorName, setDoorName] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (doorName.trim()) {
      onCreateDoor(doorName.trim());
    }
  };

  return (
    <Card className="mb-6 glass-card border-0 shadow-lg rounded-2xl">
      <CardHeader className="text-center pb-4">
        <div className="flex items-center justify-center mb-3">
          <div className="bg-blue-100 p-3 rounded-full">
            <Home className="h-6 w-6 text-blue-600" />
          </div>
        </div>
        <CardTitle className="text-lg font-bold text-gray-800">
          Tür benennen
        </CardTitle>
        <p className="text-sm text-gray-600">
          Geben Sie einen Namen für die Tür ein (z.B. "Haupteingang", "Wohnung 1")
        </p>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="door-name" className="text-sm font-medium text-gray-700">
              Türname
            </Label>
            <Input
              id="door-name"
              type="text"
              value={doorName}
              onChange={(e) => setDoorName(e.target.value)}
              placeholder="z.B. Haupteingang"
              className="mt-1 h-12 rounded-xl border-2 border-gray-200 focus:border-blue-500"
              disabled={isCreating}
              required
            />
          </div>
          
          <Button
            type="submit"
            disabled={!doorName.trim() || isCreating}
            className="w-full h-12 text-lg font-semibold bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 rounded-xl text-white"
          >
            {isCreating ? 'Wird erstellt...' : 'Tür erstellen und fortfahren'}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};
