import { useState, useCallback, useRef } from 'react';
import { toast } from 'sonner';

interface OptimisticUpdateOptions<T> {
  onSuccess?: (data: T) => void;
  onError?: (error: Error) => void;
  successMessage?: string;
  errorMessage?: string;
  revertDelay?: number;
}

interface OptimisticState<T> {
  data: T;
  isOptimistic: boolean;
  isLoading: boolean;
  error: Error | null;
}

export function useOptimisticUpdates<T>(
  initialData: T,
  options: OptimisticUpdateOptions<T> = {}
) {
  const {
    onSuccess,
    onError,
    successMessage,
    errorMessage = 'Ein Fehler ist aufgetreten',
    revertDelay = 3000
  } = options;

  const [state, setState] = useState<OptimisticState<T>>({
    data: initialData,
    isOptimistic: false,
    isLoading: false,
    error: null
  });

  const revertTimeoutRef = useRef<NodeJS.Timeout>();

  const updateOptimistically = useCallback(
    async <R>(
      optimisticData: T,
      asyncOperation: () => Promise<R>,
      options: {
        successMessage?: string;
        errorMessage?: string;
        revertOnError?: boolean;
      } = {}
    ): Promise<R | null> => {
      const {
        successMessage: localSuccessMessage,
        errorMessage: localErrorMessage,
        revertOnError = true
      } = options;

      // Clear any existing revert timeout
      if (revertTimeoutRef.current) {
        clearTimeout(revertTimeoutRef.current);
      }

      // Apply optimistic update immediately
      setState(prev => ({
        ...prev,
        data: optimisticData,
        isOptimistic: true,
        isLoading: true,
        error: null
      }));

      try {
        // Perform the async operation
        const result = await asyncOperation();

        // Success - confirm the optimistic update
        setState(prev => ({
          ...prev,
          isOptimistic: false,
          isLoading: false,
          error: null
        }));

        // Show success message
        if (localSuccessMessage || successMessage) {
          toast.success(localSuccessMessage || successMessage);
        }

        onSuccess?.(optimisticData);
        return result;

      } catch (error) {
        const errorObj = error instanceof Error ? error : new Error(String(error));

        // Error - revert optimistic update if requested
        if (revertOnError) {
          setState(prev => ({
            ...prev,
            data: initialData,
            isOptimistic: false,
            isLoading: false,
            error: errorObj
          }));
        } else {
          setState(prev => ({
            ...prev,
            isOptimistic: false,
            isLoading: false,
            error: errorObj
          }));
        }

        // Show error message
        toast.error(localErrorMessage || errorMessage);
        
        onError?.(errorObj);
        return null;
      }
    },
    [initialData, successMessage, errorMessage, onSuccess, onError]
  );

  const revertAfterDelay = useCallback((delay: number = revertDelay) => {
    revertTimeoutRef.current = setTimeout(() => {
      setState(prev => ({
        ...prev,
        data: initialData,
        isOptimistic: false,
        error: null
      }));
    }, delay);
  }, [initialData, revertDelay]);

  const reset = useCallback(() => {
    if (revertTimeoutRef.current) {
      clearTimeout(revertTimeoutRef.current);
    }
    setState({
      data: initialData,
      isOptimistic: false,
      isLoading: false,
      error: null
    });
  }, [initialData]);

  const updateData = useCallback((newData: T) => {
    setState(prev => ({
      ...prev,
      data: newData,
      isOptimistic: false
    }));
  }, []);

  return {
    ...state,
    updateOptimistically,
    revertAfterDelay,
    reset,
    updateData
  };
}

// Specialized hook for visit status updates
export function useOptimisticVisitStatus(
  initialStatus: string,
  visitId: string,
  updateFunction: (visitId: string, status: string) => Promise<void>
) {
  return useOptimisticUpdates(initialStatus, {
    successMessage: 'Status erfolgreich aktualisiert',
    errorMessage: 'Fehler beim Aktualisieren des Status'
  });
}

// Hook for form submissions with optimistic updates
export function useOptimisticForm<T>(
  initialData: T,
  submitFunction: (data: T) => Promise<void>
) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const optimistic = useOptimisticUpdates(initialData, {
    successMessage: 'Erfolgreich gespeichert',
    errorMessage: 'Fehler beim Speichern'
  });

  const submitWithOptimisticUpdate = useCallback(
    async (formData: T) => {
      setIsSubmitting(true);
      
      try {
        await optimistic.updateOptimistically(
          formData,
          () => submitFunction(formData),
          {
            successMessage: 'Daten erfolgreich gespeichert',
            errorMessage: 'Fehler beim Speichern der Daten'
          }
        );
      } finally {
        setIsSubmitting(false);
      }
    },
    [optimistic, submitFunction]
  );

  return {
    ...optimistic,
    isSubmitting,
    submit: submitWithOptimisticUpdate
  };
}

// Hook for list operations with optimistic updates
export function useOptimisticList<T extends { id: string }>(
  initialItems: T[],
  operations: {
    add?: (item: Omit<T, 'id'>) => Promise<T>;
    update?: (id: string, updates: Partial<T>) => Promise<T>;
    remove?: (id: string) => Promise<void>;
  }
) {
  const optimistic = useOptimisticUpdates(initialItems);

  const addItem = useCallback(
    async (newItem: Omit<T, 'id'>) => {
      if (!operations.add) return null;

      const tempId = `temp-${Date.now()}`;
      const optimisticItem = { ...newItem, id: tempId } as T;
      const optimisticList = [...optimistic.data, optimisticItem];

      return optimistic.updateOptimistically(
        optimisticList,
        async () => {
          const result = await operations.add!(newItem);
          // Replace temp item with real item
          const finalList = optimisticList.map(item => 
            item.id === tempId ? result : item
          );
          optimistic.updateData(finalList);
          return result;
        },
        {
          successMessage: 'Element hinzugefügt',
          errorMessage: 'Fehler beim Hinzufügen'
        }
      );
    },
    [optimistic, operations.add]
  );

  const updateItem = useCallback(
    async (id: string, updates: Partial<T>) => {
      if (!operations.update) return null;

      const optimisticList = optimistic.data.map(item =>
        item.id === id ? { ...item, ...updates } : item
      );

      return optimistic.updateOptimistically(
        optimisticList,
        () => operations.update!(id, updates),
        {
          successMessage: 'Element aktualisiert',
          errorMessage: 'Fehler beim Aktualisieren'
        }
      );
    },
    [optimistic, operations.update]
  );

  const removeItem = useCallback(
    async (id: string) => {
      if (!operations.remove) return null;

      const optimisticList = optimistic.data.filter(item => item.id !== id);

      return optimistic.updateOptimistically(
        optimisticList,
        () => operations.remove!(id),
        {
          successMessage: 'Element entfernt',
          errorMessage: 'Fehler beim Entfernen'
        }
      );
    },
    [optimistic, operations.remove]
  );

  return {
    ...optimistic,
    addItem,
    updateItem,
    removeItem
  };
}
