import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useSwipeGestures, useSwipeNavigation, useSwipeActions, triggerHapticFeedback } from '../useSwipeGestures';

// Mock navigator.vibrate
Object.defineProperty(navigator, 'vibrate', {
  writable: true,
  value: vi.fn(),
});

describe('useSwipeGestures', () => {
  let mockElement: HTMLElement;
  let addEventListenerSpy: ReturnType<typeof vi.spyOn>;
  let removeEventListenerSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    mockElement = document.createElement('div');
    addEventListenerSpy = vi.spyOn(mockElement, 'addEventListener');
    removeEventListenerSpy = vi.spyOn(mockElement, 'removeEventListener');
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should initialize with correct default values', () => {
    const onSwipeLeft = vi.fn();
    const onSwipeRight = vi.fn();
    
    const { result } = renderHook(() =>
      useSwipeGestures({
        onSwipeLeft,
        onSwipeRight,
      })
    );

    expect(result.current.isSwipeActive).toBe(false);
    expect(result.current.ref.current).toBe(null);
  });

  it('should add event listeners when ref is set', () => {
    const onSwipeLeft = vi.fn();
    const { result } = renderHook(() =>
      useSwipeGestures({ onSwipeLeft })
    );

    act(() => {
      result.current.ref.current = mockElement;
    });

    expect(addEventListenerSpy).toHaveBeenCalledWith('touchstart', expect.any(Function), { passive: false });
    expect(addEventListenerSpy).toHaveBeenCalledWith('touchmove', expect.any(Function), { passive: false });
    expect(addEventListenerSpy).toHaveBeenCalledWith('touchend', expect.any(Function), { passive: true });
  });

  it('should remove event listeners on cleanup', () => {
    const onSwipeLeft = vi.fn();
    const { result, unmount } = renderHook(() =>
      useSwipeGestures({ onSwipeLeft })
    );

    act(() => {
      result.current.ref.current = mockElement;
    });

    unmount();

    expect(removeEventListenerSpy).toHaveBeenCalledWith('touchstart', expect.any(Function));
    expect(removeEventListenerSpy).toHaveBeenCalledWith('touchmove', expect.any(Function));
    expect(removeEventListenerSpy).toHaveBeenCalledWith('touchend', expect.any(Function));
  });

  it('should detect horizontal swipe left', () => {
    const onSwipeLeft = vi.fn();
    const onSwipeRight = vi.fn();
    
    const { result } = renderHook(() =>
      useSwipeGestures({
        onSwipeLeft,
        onSwipeRight,
        threshold: 50,
      })
    );

    act(() => {
      result.current.ref.current = mockElement;
    });

    // Simulate touch events
    const touchStart = new TouchEvent('touchstart', {
      touches: [{ clientX: 100, clientY: 100 } as Touch],
    });

    const touchEnd = new TouchEvent('touchend', {
      changedTouches: [{ clientX: 40, clientY: 100 } as Touch], // Swipe left 60px
    });

    act(() => {
      mockElement.dispatchEvent(touchStart);
    });

    act(() => {
      mockElement.dispatchEvent(touchEnd);
    });

    expect(onSwipeLeft).toHaveBeenCalledTimes(1);
    expect(onSwipeRight).not.toHaveBeenCalled();
  });

  it('should detect horizontal swipe right', () => {
    const onSwipeLeft = vi.fn();
    const onSwipeRight = vi.fn();
    
    const { result } = renderHook(() =>
      useSwipeGestures({
        onSwipeLeft,
        onSwipeRight,
        threshold: 50,
      })
    );

    act(() => {
      result.current.ref.current = mockElement;
    });

    // Simulate touch events
    const touchStart = new TouchEvent('touchstart', {
      touches: [{ clientX: 100, clientY: 100 } as Touch],
    });

    const touchEnd = new TouchEvent('touchend', {
      changedTouches: [{ clientX: 160, clientY: 100 } as Touch], // Swipe right 60px
    });

    act(() => {
      mockElement.dispatchEvent(touchStart);
    });

    act(() => {
      mockElement.dispatchEvent(touchEnd);
    });

    expect(onSwipeRight).toHaveBeenCalledTimes(1);
    expect(onSwipeLeft).not.toHaveBeenCalled();
  });

  it('should not trigger swipe if distance is below threshold', () => {
    const onSwipeLeft = vi.fn();
    const onSwipeRight = vi.fn();
    
    const { result } = renderHook(() =>
      useSwipeGestures({
        onSwipeLeft,
        onSwipeRight,
        threshold: 50,
      })
    );

    act(() => {
      result.current.ref.current = mockElement;
    });

    // Simulate small swipe (below threshold)
    const touchStart = new TouchEvent('touchstart', {
      touches: [{ clientX: 100, clientY: 100 } as Touch],
    });

    const touchEnd = new TouchEvent('touchend', {
      changedTouches: [{ clientX: 120, clientY: 100 } as Touch], // Only 20px
    });

    act(() => {
      mockElement.dispatchEvent(touchStart);
    });

    act(() => {
      mockElement.dispatchEvent(touchEnd);
    });

    expect(onSwipeLeft).not.toHaveBeenCalled();
    expect(onSwipeRight).not.toHaveBeenCalled();
  });

  it('should detect vertical swipes', () => {
    const onSwipeUp = vi.fn();
    const onSwipeDown = vi.fn();
    
    const { result } = renderHook(() =>
      useSwipeGestures({
        onSwipeUp,
        onSwipeDown,
        threshold: 50,
      })
    );

    act(() => {
      result.current.ref.current = mockElement;
    });

    // Simulate swipe up
    const touchStartUp = new TouchEvent('touchstart', {
      touches: [{ clientX: 100, clientY: 100 } as Touch],
    });

    const touchEndUp = new TouchEvent('touchend', {
      changedTouches: [{ clientX: 100, clientY: 40 } as Touch], // Swipe up 60px
    });

    act(() => {
      mockElement.dispatchEvent(touchStartUp);
    });

    act(() => {
      mockElement.dispatchEvent(touchEndUp);
    });

    expect(onSwipeUp).toHaveBeenCalledTimes(1);

    // Simulate swipe down
    const touchStartDown = new TouchEvent('touchstart', {
      touches: [{ clientX: 100, clientY: 100 } as Touch],
    });

    const touchEndDown = new TouchEvent('touchend', {
      changedTouches: [{ clientX: 100, clientY: 160 } as Touch], // Swipe down 60px
    });

    act(() => {
      mockElement.dispatchEvent(touchStartDown);
    });

    act(() => {
      mockElement.dispatchEvent(touchEndDown);
    });

    expect(onSwipeDown).toHaveBeenCalledTimes(1);
  });
});

describe('useSwipeNavigation', () => {
  it('should navigate to next step on swipe left', () => {
    const onStepChange = vi.fn();
    const currentStep = 1;
    const totalSteps = 3;

    const { result } = renderHook(() =>
      useSwipeNavigation(currentStep, totalSteps, onStepChange)
    );

    act(() => {
      result.current.ref.current = mockElement;
    });

    // Simulate swipe left (next step)
    const touchStart = new TouchEvent('touchstart', {
      touches: [{ clientX: 100, clientY: 100 } as Touch],
    });

    const touchEnd = new TouchEvent('touchend', {
      changedTouches: [{ clientX: 20, clientY: 100 } as Touch], // Swipe left 80px
    });

    act(() => {
      mockElement.dispatchEvent(touchStart);
    });

    act(() => {
      mockElement.dispatchEvent(touchEnd);
    });

    expect(onStepChange).toHaveBeenCalledWith(2);
  });

  it('should navigate to previous step on swipe right', () => {
    const onStepChange = vi.fn();
    const currentStep = 2;
    const totalSteps = 3;

    const { result } = renderHook(() =>
      useSwipeNavigation(currentStep, totalSteps, onStepChange)
    );

    act(() => {
      result.current.ref.current = mockElement;
    });

    // Simulate swipe right (previous step)
    const touchStart = new TouchEvent('touchstart', {
      touches: [{ clientX: 100, clientY: 100 } as Touch],
    });

    const touchEnd = new TouchEvent('touchend', {
      changedTouches: [{ clientX: 180, clientY: 100 } as Touch], // Swipe right 80px
    });

    act(() => {
      mockElement.dispatchEvent(touchStart);
    });

    act(() => {
      mockElement.dispatchEvent(touchEnd);
    });

    expect(onStepChange).toHaveBeenCalledWith(1);
  });

  it('should not navigate beyond boundaries', () => {
    const onStepChange = vi.fn();

    // Test at first step
    const { result: resultFirst } = renderHook(() =>
      useSwipeNavigation(1, 3, onStepChange)
    );

    act(() => {
      resultFirst.current.ref.current = mockElement;
    });

    // Try to go to previous step (should not work)
    const touchStartRight = new TouchEvent('touchstart', {
      touches: [{ clientX: 100, clientY: 100 } as Touch],
    });

    const touchEndRight = new TouchEvent('touchend', {
      changedTouches: [{ clientX: 180, clientY: 100 } as Touch],
    });

    act(() => {
      mockElement.dispatchEvent(touchStartRight);
    });

    act(() => {
      mockElement.dispatchEvent(touchEndRight);
    });

    expect(onStepChange).not.toHaveBeenCalled();

    // Test at last step
    const { result: resultLast } = renderHook(() =>
      useSwipeNavigation(3, 3, onStepChange)
    );

    act(() => {
      resultLast.current.ref.current = mockElement;
    });

    // Try to go to next step (should not work)
    const touchStartLeft = new TouchEvent('touchstart', {
      touches: [{ clientX: 100, clientY: 100 } as Touch],
    });

    const touchEndLeft = new TouchEvent('touchend', {
      changedTouches: [{ clientX: 20, clientY: 100 } as Touch],
    });

    act(() => {
      mockElement.dispatchEvent(touchStartLeft);
    });

    act(() => {
      mockElement.dispatchEvent(touchEndLeft);
    });

    expect(onStepChange).not.toHaveBeenCalled();
  });
});

describe('triggerHapticFeedback', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should trigger vibration with correct patterns', () => {
    triggerHapticFeedback('light');
    expect(navigator.vibrate).toHaveBeenCalledWith([10]);

    triggerHapticFeedback('medium');
    expect(navigator.vibrate).toHaveBeenCalledWith([20]);

    triggerHapticFeedback('heavy');
    expect(navigator.vibrate).toHaveBeenCalledWith([30]);
  });

  it('should handle missing vibrate API gracefully', () => {
    const originalVibrate = navigator.vibrate;
    delete (navigator as any).vibrate;

    expect(() => {
      triggerHapticFeedback('light');
    }).not.toThrow();

    // Restore
    navigator.vibrate = originalVibrate;
  });
});
