
import { Address, House } from "@/types";

// Initialize with some mock data
export const mockAddresses: Address[] = [
  { id: "a1", zipCode: "80331", city: "München", street: "Marienplatz" },
  { id: "a2", zipCode: "10117", city: "Berlin", street: "Unter den Linden" },
];

export const mockHouses: House[] = [
  { 
    id: "h1", 
    addressId: "a1", 
    houseNumber: "1", 
    type: "EFH", 
    latitude: 48.1351, 
    longitude: 11.5820, 
    createdAt: new Date().toISOString(), 
    createdBy: "1"
  },
  { 
    id: "h2", 
    addressId: "a1", 
    houseNumber: "2", 
    type: "MFH", 
    latitude: 48.1355, 
    longitude: 11.5825, 
    createdAt: new Date().toISOString(), 
    createdBy: "1"
  },
];
