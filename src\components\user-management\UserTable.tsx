
import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { User } from '@/types';
import { Eye, Edit, RotateCcw, ChevronDown, ChevronUp } from 'lucide-react';

interface Team {
  id: string;
  name: string;
}

type SortField = 'name' | 'email' | 'role' | 'team';
type SortDirection = 'asc' | 'desc';

interface UserTableProps {
  users: User[];
  userProfiles: any[];
  teams: Team[];
  isMobile: boolean;
  onViewUser: (user: User) => void;
  onEditUser: (user: User) => void;
  onResetUserStats: (user: User) => void;
}

export const UserTable: React.FC<UserTableProps> = ({
  users,
  userProfiles,
  teams,
  isMobile,
  onViewUser,
  onEditUser,
  onResetUserStats,
}) => {
  const [sortField, setSortField] = useState<SortField>('name');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const getSortedUsers = () => {
    return [...users].sort((a, b) => {
      let aValue: string;
      let bValue: string;

      switch (sortField) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'email':
          aValue = a.email.toLowerCase();
          bValue = b.email.toLowerCase();
          break;
        case 'role':
          aValue = a.role.toLowerCase();
          bValue = b.role.toLowerCase();
          break;
        case 'team':
          const aTeam = teams.find(t => t.id === a.teamId);
          const bTeam = teams.find(t => t.id === b.teamId);
          aValue = (aTeam?.name || '').toLowerCase();
          bValue = (bTeam?.name || '').toLowerCase();
          break;
        default:
          return 0;
      }

      if (sortDirection === 'asc') {
        return aValue.localeCompare(bValue);
      } else {
        return bValue.localeCompare(aValue);
      }
    });
  };

  const SortButton: React.FC<{ field: SortField; children: React.ReactNode }> = ({ field, children }) => (
    <button
      onClick={() => handleSort(field)}
      className="flex items-center gap-1 hover:text-blue-600 transition-colors"
    >
      {children}
      {sortField === field && (
        sortDirection === 'asc' ? 
        <ChevronUp className="h-3 w-3" /> : 
        <ChevronDown className="h-3 w-3" />
      )}
    </button>
  );

  const sortedUsers = getSortedUsers();

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow className="border-b-2">
            <TableHead className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-gray-700`}>
              <SortButton field="name">Name</SortButton>
            </TableHead>
            <TableHead className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-gray-700`}>
              <SortButton field="email">E-Mail</SortButton>
            </TableHead>
            <TableHead className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-gray-700`}>
              <SortButton field="role">Rolle</SortButton>
            </TableHead>
            <TableHead className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-gray-700`}>
              <SortButton field="team">Team</SortButton>
            </TableHead>
            <TableHead className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-gray-700`}>Status</TableHead>
            <TableHead className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-gray-700`}>Aktionen</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {sortedUsers.map((user, index) => {
            const profile = userProfiles.find(p => p.id === user.id);
            const team = teams.find(t => t.id === user.teamId);
            
            return (
              <TableRow 
                key={user.id} 
                className={`hover:bg-blue-50/70 transition-colors animate-fade-in ${
                  index % 2 === 0 ? 'bg-gray-50/30' : 'bg-white'
                }`}
              >
                <TableCell className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-gray-800 py-3`}>
                  {user.name}
                </TableCell>
                <TableCell className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-700 py-3`}>
                  <div className="truncate max-w-[200px]" title={user.email}>
                    {user.email}
                  </div>
                </TableCell>
                <TableCell className="py-3">
                  <Badge variant="outline" className="capitalize text-xs">
                    {user.role}
                  </Badge>
                </TableCell>
                <TableCell className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-700 py-3`}>
                  {team?.name || user.teamId || (
                    <span className="text-gray-400 italic">Kein Team</span>
                  )}
                </TableCell>
                <TableCell className="py-3">
                  <Badge variant={profile?.is_active !== false ? 'default' : 'secondary'} className="text-xs">
                    {profile?.is_active !== false ? 'Aktiv' : 'Inaktiv'}
                  </Badge>
                </TableCell>
                <TableCell className="py-3">
                  <div className="flex gap-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onViewUser(user)}
                      className="text-blue-600 border-blue-200 hover:bg-blue-50 h-8 w-8 p-0"
                      title="Benutzer anzeigen"
                    >
                      <Eye className="h-3 w-3" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm" 
                      onClick={() => onEditUser(user)}
                      className="text-green-600 border-green-200 hover:bg-green-50 h-8 w-8 p-0"
                      title="Benutzer bearbeiten"
                    >
                      <Edit className="h-3 w-3" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onResetUserStats(user)}
                      className="text-orange-600 border-orange-200 hover:bg-orange-50 h-8 w-8 p-0"
                      title="Statistiken zurücksetzen"
                    >
                      <RotateCcw className="h-3 w-3" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
      
      {sortedUsers.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <p>Keine Benutzer gefunden.</p>
          <p className="text-sm">Versuchen Sie, Ihre Filter zu ändern.</p>
        </div>
      )}
    </div>
  );
};
