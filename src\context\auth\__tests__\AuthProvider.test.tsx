import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AuthProvider, useAuth } from '../AuthContext';
import { mockLocalStorage } from '@/test/test-utils';
import { RegisterData } from '../types';

// Test component to access auth context
const TestComponent = () => {
  const {
    user,
    login,
    register,
    logout,
    isLoading,
    error,
    clearError,
    isAuthenticated
  } = useAuth();

  const handleRegister = () => {
    const registerData: RegisterData = {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'SecurePass123!',
      confirmPassword: 'SecurePass123!'
    };
    register(registerData);
  };

  return (
    <div>
      <div data-testid="user-info">
        {user ? `Logged in as: ${user.name}` : 'Not logged in'}
      </div>
      <div data-testid="loading">{isLoading ? 'Loading...' : 'Not loading'}</div>
      <div data-testid="error">{error?.message || 'No error'}</div>
      <div data-testid="authenticated">{isAuthenticated ? 'Authenticated' : 'Not authenticated'}</div>
      <button
        data-testid="login-btn"
        onClick={() => login('<EMAIL>', 'test123')}
      >
        Login
      </button>
      <button
        data-testid="login-remember-btn"
        onClick={() => login('<EMAIL>', 'test123', true)}
      >
        Login with Remember Me
      </button>
      <button
        data-testid="register-btn"
        onClick={handleRegister}
      >
        Register
      </button>
      <button data-testid="logout-btn" onClick={logout}>
        Logout
      </button>
      <button data-testid="clear-error-btn" onClick={clearError}>
        Clear Error
      </button>
    </div>
  );
};

const renderWithAuth = () => {
  return render(
    <AuthProvider>
      <TestComponent />
    </AuthProvider>
  );
};

describe('AuthProvider', () => {
  let localStorage: ReturnType<typeof mockLocalStorage>;

  beforeEach(() => {
    // Mock localStorage
    localStorage = mockLocalStorage();
    Object.defineProperty(window, 'localStorage', {
      value: localStorage,
      writable: true,
    });

    // Clear all mocks
    vi.clearAllMocks();

    // Mock console methods to avoid noise in tests
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should render without crashing', () => {
    renderWithAuth();
    expect(screen.getByTestId('user-info')).toBeInTheDocument();
  });

  it('should show not logged in initially', () => {
    renderWithAuth();
    expect(screen.getByTestId('user-info')).toHaveTextContent('Not logged in');
    expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
    expect(screen.getByTestId('error')).toHaveTextContent('No error');
    expect(screen.getByTestId('authenticated')).toHaveTextContent('Not authenticated');
  });

  it('should handle successful login', async () => {
    renderWithAuth();

    fireEvent.click(screen.getByTestId('login-btn'));

    // Wait for login to complete
    await waitFor(() => {
      expect(screen.getByTestId('user-info')).toHaveTextContent('Logged in as: Test Berater');
      expect(screen.getByTestId('authenticated')).toHaveTextContent('Authenticated');
      expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
    });
  });

  it('should handle login with remember me', async () => {
    renderWithAuth();

    fireEvent.click(screen.getByTestId('login-remember-btn'));

    await waitFor(() => {
      expect(screen.getByTestId('user-info')).toHaveTextContent('Logged in as: Test Berater');
    });

    // Check that remember me was set in localStorage
    expect(localStorage.getItem('rememberMe')).toBe('true');
  });

  it('should handle registration', async () => {
    renderWithAuth();

    fireEvent.click(screen.getByTestId('register-btn'));

    // Should show loading state
    expect(screen.getByTestId('loading')).toHaveTextContent('Loading...');

    // Wait for registration to complete
    await waitFor(() => {
      expect(screen.getByTestId('user-info')).toHaveTextContent('Logged in as: Test User');
      expect(screen.getByTestId('authenticated')).toHaveTextContent('Authenticated');
      expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
    });
  });

  it('should handle logout', async () => {
    renderWithAuth();

    // First login
    fireEvent.click(screen.getByTestId('login-btn'));
    await waitFor(() => {
      expect(screen.getByTestId('authenticated')).toHaveTextContent('Authenticated');
    });

    // Then logout
    fireEvent.click(screen.getByTestId('logout-btn'));

    await waitFor(() => {
      expect(screen.getByTestId('user-info')).toHaveTextContent('Not logged in');
      expect(screen.getByTestId('authenticated')).toHaveTextContent('Not authenticated');
    });

    // Check that session data was cleared
    expect(localStorage.getItem('currentUser')).toBeNull();
    expect(localStorage.getItem('sessionExpiry')).toBeNull();
    expect(localStorage.getItem('rememberMe')).toBeNull();
  });

  it('should clear errors', async () => {
    renderWithAuth();

    fireEvent.click(screen.getByTestId('clear-error-btn'));

    await waitFor(() => {
      expect(screen.getByTestId('error')).toHaveTextContent('No error');
    });
  });

  it('should restore user session from localStorage', () => {
    // Set up stored user data
    const storedUser = {
      id: '1',
      name: 'Stored User',
      email: '<EMAIL>',
      role: 'berater'
    };

    localStorage.setItem('currentUser', JSON.stringify(storedUser));
    localStorage.setItem('rememberMe', 'true');

    renderWithAuth();

    // Should restore the user from localStorage
    expect(screen.getByTestId('user-info')).toHaveTextContent('Logged in as: Stored User');
    expect(screen.getByTestId('authenticated')).toHaveTextContent('Authenticated');
  });

  it('should handle expired session', () => {
    // Set up expired session
    const storedUser = {
      id: '1',
      name: 'Expired User',
      email: '<EMAIL>',
      role: 'berater'
    };

    const expiredDate = new Date();
    expiredDate.setHours(expiredDate.getHours() - 1); // 1 hour ago

    localStorage.setItem('currentUser', JSON.stringify(storedUser));
    localStorage.setItem('rememberMe', 'false');
    localStorage.setItem('sessionExpiry', expiredDate.toISOString());

    renderWithAuth();

    // Should not restore expired session
    expect(screen.getByTestId('user-info')).toHaveTextContent('Not logged in');
    expect(screen.getByTestId('authenticated')).toHaveTextContent('Not authenticated');

    // Should have cleared the expired session data
    expect(localStorage.getItem('currentUser')).toBeNull();
  });

  it('should handle login with invalid credentials', async () => {
    const TestComponentWithInvalidLogin = () => {
      const { login, error, isLoading } = useAuth();

      return (
        <div>
          <div data-testid="invalid-error">{error?.message || 'No error'}</div>
          <div data-testid="invalid-loading">{isLoading ? 'Loading...' : 'Not loading'}</div>
          <button
            data-testid="invalid-login-btn"
            onClick={() => login('<EMAIL>', 'wrongpassword')}
          >
            Invalid Login
          </button>
        </div>
      );
    };

    render(
      <AuthProvider>
        <TestComponentWithInvalidLogin />
      </AuthProvider>
    );

    const invalidLoginBtn = screen.getByTestId('invalid-login-btn');
    fireEvent.click(invalidLoginBtn);

    // Wait for login to fail
    await waitFor(() => {
      expect(screen.getByTestId('invalid-loading')).toHaveTextContent('Not loading');
    });

    // Should show error
    expect(screen.getByTestId('invalid-error')).toHaveTextContent('Ungültige Anmeldeinformationen');
  });

  it('should persist user in localStorage on login', async () => {
    renderWithAuth();

    const loginBtn = screen.getByTestId('login-btn');
    fireEvent.click(loginBtn);

    await waitFor(() => {
      expect(screen.getByTestId('user-info')).toHaveTextContent('Logged in as: Test Berater');
    });

    // Should have persisted user data
    expect(localStorage.getItem('currentUser')).toContain('Test Berater');
    expect(localStorage.getItem('sessionExpiry')).toBeTruthy();
  });
});
