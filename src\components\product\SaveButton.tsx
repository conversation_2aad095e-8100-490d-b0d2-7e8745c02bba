
import React from 'react';
import { But<PERSON> } from '@/design-system/components/Button';
import { ArrowLeft } from 'lucide-react';

interface SaveButtonProps {
  canSave: boolean;
  isSubmitting: boolean;
  selectedDoorsCount: number;
  productsCount: number;
  onSubmit: () => void;
  onBack: () => void;
}

export const SaveButton: React.FC<SaveButtonProps> = ({
  canSave,
  isSubmitting,
  selectedDoorsCount,
  productsCount,
  onSubmit,
  onBack
}) => {
  return (
    <div className="w-full space-y-4">
      <Button
        onClick={onSubmit}
        variant="success"
        size="lg"
        fullWidth
        loading={isSubmitting}
        disabled={!canSave}
      >
        {productsCount > 0 ?
          `${productsCount} Produkte speichern` :
          `Besuch ohne Produkte speichern`}
      </Button>

      <Button
        onClick={onBack}
        variant="outline"
        size="md"
        fullWidth
        leftIcon={<ArrowLeft className="h-4 w-4" />}
        disabled={isSubmitting}
      >
        Zurück
      </Button>
    </div>
  );
};
