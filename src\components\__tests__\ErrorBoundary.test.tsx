import React from 'react';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import ErrorBoundary, { withErrorBoundary } from '../ErrorBoundary';

// Component that throws an error
const ThrowError = ({ shouldThrow = false }: { shouldThrow?: boolean }) => {
  if (shouldThrow) {
    throw new Error('Test error');
  }
  return <div data-testid="no-error">No error occurred</div>;
};

// Component that can be toggled to throw an error
const ToggleError = () => {
  const [shouldThrow, setShouldThrow] = React.useState(false);
  
  return (
    <div>
      <button 
        data-testid="trigger-error" 
        onClick={() => setShouldThrow(true)}
      >
        Trigger Error
      </button>
      <ThrowError shouldThrow={shouldThrow} />
    </div>
  );
};

describe('ErrorBoundary', () => {
  beforeEach(() => {
    // Mock console.error to avoid noise in test output
    vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.spyOn(console, 'group').mockImplementation(() => {});
    vi.spyOn(console, 'groupEnd').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should render children when there is no error', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={false} />
      </ErrorBoundary>
    );
    
    expect(screen.getByTestId('no-error')).toBeInTheDocument();
    expect(screen.getByText('No error occurred')).toBeInTheDocument();
  });

  it('should render error UI when child component throws', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );
    
    expect(screen.getByText('Etwas ist schiefgelaufen')).toBeInTheDocument();
    expect(screen.getByText('Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es erneut.')).toBeInTheDocument();
    expect(screen.getByText('Erneut versuchen')).toBeInTheDocument();
    expect(screen.getByText('Zur Startseite')).toBeInTheDocument();
  });

  it('should call onError callback when error occurs', () => {
    const onError = vi.fn();
    
    render(
      <ErrorBoundary onError={onError}>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );
    
    expect(onError).toHaveBeenCalledWith(
      expect.any(Error),
      expect.objectContaining({
        componentStack: expect.any(String),
      })
    );
  });

  it('should render custom fallback when provided', () => {
    const customFallback = <div data-testid="custom-fallback">Custom Error UI</div>;
    
    render(
      <ErrorBoundary fallback={customFallback}>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );
    
    expect(screen.getByTestId('custom-fallback')).toBeInTheDocument();
    expect(screen.getByText('Custom Error UI')).toBeInTheDocument();
    expect(screen.queryByText('Etwas ist schiefgelaufen')).not.toBeInTheDocument();
  });

  it('should reset error state when reset button is clicked', () => {
    render(
      <ErrorBoundary>
        <ToggleError />
      </ErrorBoundary>
    );
    
    // Initially no error
    expect(screen.getByTestId('no-error')).toBeInTheDocument();
    
    // Trigger error
    fireEvent.click(screen.getByTestId('trigger-error'));
    
    // Should show error UI
    expect(screen.getByText('Etwas ist schiefgelaufen')).toBeInTheDocument();
    expect(screen.queryByTestId('no-error')).not.toBeInTheDocument();
    
    // Reset error
    fireEvent.click(screen.getByText('Erneut versuchen'));
    
    // Should show children again (but error will trigger again due to state)
    // In real scenarios, the reset would work if the error condition is resolved
  });

  it('should navigate to home when home button is clicked', () => {
    // Mock window.location
    const mockLocation = { href: '' };
    Object.defineProperty(window, 'location', {
      value: mockLocation,
      writable: true,
    });
    
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );
    
    fireEvent.click(screen.getByText('Zur Startseite'));
    
    expect(mockLocation.href).toBe('/');
  });

  it('should log error details to console', () => {
    const consoleSpy = vi.spyOn(console, 'error');
    
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );
    
    expect(consoleSpy).toHaveBeenCalledWith(
      'ErrorBoundary caught an error:',
      expect.any(Error),
      expect.any(Object)
    );
  });

  it('should show error details in development mode', () => {
    // Mock development environment
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'development';
    
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );
    
    expect(screen.getByText('Fehlerdetails (nur in Entwicklung sichtbar):')).toBeInTheDocument();
    expect(screen.getByText('Test error')).toBeInTheDocument();
    
    // Restore environment
    process.env.NODE_ENV = originalEnv;
  });

  it('should not show error details in production mode', () => {
    // Mock production environment
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'production';
    
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );
    
    expect(screen.queryByText('Fehlerdetails (nur in Entwicklung sichtbar):')).not.toBeInTheDocument();
    expect(screen.queryByText('Test error')).not.toBeInTheDocument();
    
    // Restore environment
    process.env.NODE_ENV = originalEnv;
  });
});

describe('withErrorBoundary HOC', () => {
  beforeEach(() => {
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should wrap component with error boundary', () => {
    const WrappedComponent = withErrorBoundary(ThrowError);
    
    render(<WrappedComponent shouldThrow={false} />);
    
    expect(screen.getByTestId('no-error')).toBeInTheDocument();
  });

  it('should catch errors in wrapped component', () => {
    const WrappedComponent = withErrorBoundary(ThrowError);
    
    render(<WrappedComponent shouldThrow={true} />);
    
    expect(screen.getByText('Etwas ist schiefgelaufen')).toBeInTheDocument();
  });

  it('should use custom fallback and onError', () => {
    const customFallback = <div data-testid="hoc-fallback">HOC Error UI</div>;
    const onError = vi.fn();
    
    const WrappedComponent = withErrorBoundary(ThrowError, customFallback, onError);
    
    render(<WrappedComponent shouldThrow={true} />);
    
    expect(screen.getByTestId('hoc-fallback')).toBeInTheDocument();
    expect(onError).toHaveBeenCalled();
  });
});
