// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://tbrsqfghvoaqwsthigxu.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRicnNxZmdodm9hcXdzdGhpZ3h1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY3NzkzMTQsImV4cCI6MjA2MjM1NTMxNH0.yWpAIwmcHMk1jkf18_oNS77veD56dwZBq290XDYZtZU";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);