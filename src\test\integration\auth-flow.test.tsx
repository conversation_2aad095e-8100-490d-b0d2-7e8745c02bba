import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider } from '@/context/auth/AuthContext';
import { DataProvider } from '@/context/data';
import { ThemeProvider } from '@/context/theme/ThemeProvider';
import { SettingsProvider } from '@/context/settings/SettingsProvider';
import App from '@/App';
import { mockLocalStorage } from '@/test/test-utils';

// Mock the lazy-loaded components to avoid dynamic import issues in tests
vi.mock('@/pages/Index', () => ({
  default: () => <div data-testid="index-page">Index Page Loaded Successfully</div>
}));

vi.mock('@/pages/Login', () => ({
  default: () => <div data-testid="login-page">Login Page</div>
}));

// Mock other components that might cause issues
vi.mock('@/components/index/IndexContent', () => ({
  default: () => <div data-testid="index-content">Index Content</div>
}));

vi.mock('@/components/layout/MainLayout', () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="main-layout">{children}</div>
  )
}));

// Mock console methods to avoid noise
const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

describe('Authentication Flow Integration', () => {
  let queryClient: QueryClient;
  let localStorage: ReturnType<typeof mockLocalStorage>;

  beforeEach(() => {
    // Create fresh query client for each test
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });

    // Mock localStorage
    localStorage = mockLocalStorage();
    Object.defineProperty(window, 'localStorage', {
      value: localStorage,
      writable: true,
    });

    // Clear all mocks
    vi.clearAllMocks();
  });

  const renderApp = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <ThemeProvider>
          <SettingsProvider>
            <AuthProvider>
              <DataProvider>
                <BrowserRouter>
                  <App />
                </BrowserRouter>
              </DataProvider>
            </AuthProvider>
          </SettingsProvider>
        </ThemeProvider>
      </QueryClientProvider>
    );
  };

  it('should redirect to login when not authenticated', async () => {
    renderApp();

    // Should show login page initially
    await waitFor(() => {
      expect(screen.getByTestId('login-page')).toBeInTheDocument();
    });
  });

  it('should navigate to index page after successful authentication', async () => {
    // Mock successful authentication by setting user in localStorage
    const mockUser = {
      id: '1',
      name: 'Test User',
      email: '<EMAIL>',
      role: 'berater',
      teamId: 'team1'
    };

    localStorage.setItem('currentUser', JSON.stringify(mockUser));
    localStorage.setItem('rememberMe', 'false');
    
    // Set session expiry to future date
    const futureDate = new Date();
    futureDate.setHours(futureDate.getHours() + 1);
    localStorage.setItem('sessionExpiry', futureDate.toISOString());

    renderApp();

    // Should show index page for authenticated user
    await waitFor(() => {
      expect(screen.getByTestId('index-page')).toBeInTheDocument();
    }, { timeout: 3000 });

    expect(screen.getByText('Index Page Loaded Successfully')).toBeInTheDocument();
  });

  it('should handle expired session correctly', async () => {
    // Mock expired session
    const mockUser = {
      id: '1',
      name: 'Expired User',
      email: '<EMAIL>',
      role: 'berater',
      teamId: 'team1'
    };

    localStorage.setItem('currentUser', JSON.stringify(mockUser));
    localStorage.setItem('rememberMe', 'false');
    
    // Set session expiry to past date
    const pastDate = new Date();
    pastDate.setHours(pastDate.getHours() - 1);
    localStorage.setItem('sessionExpiry', pastDate.toISOString());

    renderApp();

    // Should redirect to login due to expired session
    await waitFor(() => {
      expect(screen.getByTestId('login-page')).toBeInTheDocument();
    });

    // Session data should be cleared
    expect(localStorage.getItem('currentUser')).toBeNull();
    expect(localStorage.getItem('sessionExpiry')).toBeNull();
  });

  it('should handle remember me sessions correctly', async () => {
    // Mock remember me session (no expiry check)
    const mockUser = {
      id: '1',
      name: 'Remembered User',
      email: '<EMAIL>',
      role: 'berater',
      teamId: 'team1'
    };

    localStorage.setItem('currentUser', JSON.stringify(mockUser));
    localStorage.setItem('rememberMe', 'true');

    renderApp();

    // Should show index page even without checking expiry
    await waitFor(() => {
      expect(screen.getByTestId('index-page')).toBeInTheDocument();
    });
  });

  it('should handle corrupted localStorage data gracefully', async () => {
    // Set corrupted user data
    localStorage.setItem('currentUser', 'invalid-json');
    localStorage.setItem('rememberMe', 'true');

    renderApp();

    // Should redirect to login and clear corrupted data
    await waitFor(() => {
      expect(screen.getByTestId('login-page')).toBeInTheDocument();
    });

    // Corrupted data should be cleared
    expect(localStorage.getItem('currentUser')).toBeNull();
  });

  it('should not show console errors during normal authentication flow', async () => {
    const mockUser = {
      id: '1',
      name: 'Test User',
      email: '<EMAIL>',
      role: 'berater',
      teamId: 'team1'
    };

    localStorage.setItem('currentUser', JSON.stringify(mockUser));
    localStorage.setItem('rememberMe', 'true');

    renderApp();

    await waitFor(() => {
      expect(screen.getByTestId('index-page')).toBeInTheDocument();
    });

    // Should not have any console errors during normal flow
    expect(consoleErrorSpy).not.toHaveBeenCalled();
  });
});

describe('Route Protection Integration', () => {
  let queryClient: QueryClient;
  let localStorage: ReturnType<typeof mockLocalStorage>;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });

    localStorage = mockLocalStorage();
    Object.defineProperty(window, 'localStorage', {
      value: localStorage,
      writable: true,
    });

    vi.clearAllMocks();
  });

  const renderAppWithRoute = (initialRoute = '/') => {
    window.history.pushState({}, 'Test page', initialRoute);
    
    return render(
      <QueryClientProvider client={queryClient}>
        <ThemeProvider>
          <SettingsProvider>
            <AuthProvider>
              <DataProvider>
                <BrowserRouter>
                  <App />
                </BrowserRouter>
              </DataProvider>
            </AuthProvider>
          </SettingsProvider>
        </ThemeProvider>
      </QueryClientProvider>
    );
  };

  it('should protect routes and redirect to login', async () => {
    // Try to access protected route without authentication
    renderAppWithRoute('/');

    await waitFor(() => {
      expect(screen.getByTestId('login-page')).toBeInTheDocument();
    });
  });

  it('should allow access to protected routes when authenticated', async () => {
    // Set up authenticated user
    const mockUser = {
      id: '1',
      name: 'Authenticated User',
      email: '<EMAIL>',
      role: 'berater',
      teamId: 'team1'
    };

    localStorage.setItem('currentUser', JSON.stringify(mockUser));
    localStorage.setItem('rememberMe', 'true');

    renderAppWithRoute('/');

    await waitFor(() => {
      expect(screen.getByTestId('index-page')).toBeInTheDocument();
    });
  });
});
