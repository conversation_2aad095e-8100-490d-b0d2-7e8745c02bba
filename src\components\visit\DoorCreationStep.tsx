
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { DoorCreation } from './DoorCreation';
import { AddressHeader } from './AddressHeader';
import { Address, House } from '@/types';

interface DoorCreationStepProps {
  visitId: string;
  address: Address;
  house: House;
  onCreateDoor: (doorName: string) => void;
  isCreating: boolean;
}

export const DoorCreationStep: React.FC<DoorCreationStepProps> = ({
  visitId,
  address,
  house,
  onCreateDoor,
  isCreating
}) => {
  const navigate = useNavigate();

  return (
    <div className="space-y-6">
      <AddressHeader 
        address={address} 
        house={house} 
        step={1} 
      />

      <DoorCreation
        onCreateDoor={onCreateDoor}
        isCreating={isCreating}
      />

      <Card className="w-full bg-white/95 backdrop-blur-sm border-0 shadow-lg rounded-2xl">
        <CardContent className="p-6">
          <Button
            onClick={() => navigate('/')}
            variant="outline"
            className="w-full h-12 text-lg font-semibold rounded-xl border-2 border-gray-300 hover:border-red-500 hover:text-red-600"
          >
            Zurück zur Startseite
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};
