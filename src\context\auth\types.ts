
import { User as AppUser, UserRole } from "../../types";

// Enhanced auth specific types with strict typing
export interface AuthContextType {
  user: AppUser | null;
  login: (emailOrName: string, password: string, rememberMe?: boolean) => Promise<AuthResult>;
  register: (userData: RegisterData) => Promise<AuthResult>;
  logout: () => Promise<void>;
  isLoading: boolean;
  error: AuthError | null;
  users: AppUser[]; // For admin management
  updateUser: (user: AppUser) => Promise<void>;
  createUser: (user: Omit<AppUser, "id">) => Promise<AppUser>;
  loginWithGoogle: () => Promise<AuthResult>;
  clearError: () => void;
  isAuthenticated: boolean;
  sessionExpiry: Date | null;
  refreshSession: () => Promise<boolean>;
}

// Authentication result interface
export interface AuthResult {
  success: boolean;
  error?: AuthError;
  user?: AppUser;
}

// Registration data interface with enhanced validation
export interface RegisterData {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
}

// Enhanced error handling with better categorization
export interface AuthError {
  type: 'validation' | 'authentication' | 'network' | 'server' | 'session' | 'permission';
  message: string;
  field?: string;
  code?: string;
  timestamp?: Date;
  retryable?: boolean;
}

// Enhanced validation types
export interface ValidationError {
  field: string;
  message: string;
  code?: string;
  severity?: 'error' | 'warning' | 'info';
}

export interface FormValidationState {
  isValid: boolean;
  errors: ValidationError[];
  touched: Record<string, boolean>;
}

// Enhanced password strength types
export type PasswordStrength = 'weak' | 'fair' | 'good' | 'strong' | 'excellent';

export interface PasswordValidation {
  strength: PasswordStrength;
  score: number;
  requirements: PasswordRequirements;
  suggestions: string[];
  isValid: boolean;
  entropy: number;
}

// Enhanced form state interfaces
export interface LoginFormState {
  emailOrName: string;
  password: string;
  rememberMe: boolean;
  isSubmitting: boolean;
  lastAttempt?: Date;
  attemptCount: number;
}

export interface RegisterFormState {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  isSubmitting: boolean;
  currentStep: number;
  maxSteps: number;
  agreedToTerms: boolean;
  marketingConsent: boolean;
}

// Enhanced form validator interface
export interface FormValidator {
  errors: Record<string, ValidationError | null>;
  touched: Record<string, boolean>;
  setError: (error: ValidationError) => void;
  clearError: (field: string) => void;
  setTouched: (field: string) => void;
  getFieldError: (field: string) => ValidationError | null;
  hasErrors: () => boolean;
  reset: () => void;
  validateField: (field: string, value: string) => ValidationError | null;
  validateAll: () => boolean;
}

// Session management types
export interface SessionInfo {
  token: string;
  expiresAt: Date;
  refreshToken?: string;
  rememberMe: boolean;
  deviceId?: string;
}

// User preferences for auth
export interface AuthPreferences {
  rememberMe: boolean;
  biometricEnabled: boolean;
  twoFactorEnabled: boolean;
  sessionTimeout: number; // in minutes
}

// Enhanced password requirements
export interface PasswordRequirements {
  minLength: boolean;
  hasUppercase: boolean;
  hasLowercase: boolean;
  hasNumbers: boolean;
  hasSpecialChars: boolean;
  noCommonPatterns: boolean;
  noPersonalInfo: boolean;
}
