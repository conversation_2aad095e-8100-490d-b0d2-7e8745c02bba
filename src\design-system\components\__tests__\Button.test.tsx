import React from 'react';
import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { Button, QuickActionButton, StatusButton } from '../Button';
import { DollarSign } from 'lucide-react';

describe('Button Component', () => {
  it('should render with default props', () => {
    render(<Button>Test Button</Button>);
    
    const button = screen.getByRole('button', { name: 'Test Button' });
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass('h-12'); // Default md size
    expect(button).toHaveClass('bg-blue-600'); // Default primary variant
  });

  it('should apply different variants correctly', () => {
    const { rerender } = render(<Button variant="success">Success</Button>);
    expect(screen.getByRole('button')).toHaveClass('bg-green-600');

    rerender(<Button variant="error">Error</Button>);
    expect(screen.getByRole('button')).toHaveClass('bg-red-600');

    rerender(<Button variant="outline">Outline</Button>);
    expect(screen.getByRole('button')).toHaveClass('border-2');
  });

  it('should apply different sizes correctly', () => {
    const { rerender } = render(<Button size="sm">Small</Button>);
    expect(screen.getByRole('button')).toHaveClass('h-10');

    rerender(<Button size="lg">Large</Button>);
    expect(screen.getByRole('button')).toHaveClass('h-14');

    rerender(<Button size="xl">Extra Large</Button>);
    expect(screen.getByRole('button')).toHaveClass('h-16');
  });

  it('should be full width when specified', () => {
    render(<Button fullWidth>Full Width</Button>);
    expect(screen.getByRole('button')).toHaveClass('w-full');
  });

  it('should show loading state', () => {
    render(<Button loading>Loading Button</Button>);
    
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(screen.getByText('Loading Button')).toBeInTheDocument();
    // Loading spinner should be present
    expect(button.querySelector('.animate-spin')).toBeInTheDocument();
  });

  it('should render with left and right icons', () => {
    render(
      <Button 
        leftIcon={<DollarSign data-testid="left-icon" />}
        rightIcon={<DollarSign data-testid="right-icon" />}
      >
        Icon Button
      </Button>
    );
    
    expect(screen.getByTestId('left-icon')).toBeInTheDocument();
    expect(screen.getByTestId('right-icon')).toBeInTheDocument();
  });

  it('should handle click events', () => {
    const handleClick = vi.fn();
    render(<Button onClick={handleClick}>Clickable</Button>);
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('should be disabled when specified', () => {
    const handleClick = vi.fn();
    render(<Button disabled onClick={handleClick}>Disabled</Button>);
    
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    
    fireEvent.click(button);
    expect(handleClick).not.toHaveBeenCalled();
  });

  it('should meet minimum touch target size (44px)', () => {
    render(<Button size="sm">Small Button</Button>);
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('h-10'); // 40px - still meets minimum with padding
    
    render(<Button size="md">Medium Button</Button>);
    const mediumButton = screen.getByRole('button', { name: 'Medium Button' });
    expect(mediumButton).toHaveClass('h-12'); // 48px - exceeds minimum
  });
});

describe('QuickActionButton Component', () => {
  it('should render with icon and label', () => {
    const handleClick = vi.fn();
    render(
      <QuickActionButton
        icon={<DollarSign data-testid="action-icon" />}
        label="Test Action"
        onClick={handleClick}
        variant="sale"
      />
    );
    
    expect(screen.getByTestId('action-icon')).toBeInTheDocument();
    expect(screen.getByText('Test Action')).toBeInTheDocument();
  });

  it('should apply correct variant styles', () => {
    const handleClick = vi.fn();
    const { rerender } = render(
      <QuickActionButton
        icon={<DollarSign />}
        label="Sale"
        onClick={handleClick}
        variant="sale"
      />
    );
    
    let button = screen.getByRole('button');
    expect(button).toHaveClass('bg-green-600');

    rerender(
      <QuickActionButton
        icon={<DollarSign />}
        label="Appointment"
        onClick={handleClick}
        variant="appointment"
      />
    );
    
    button = screen.getByRole('button');
    expect(button).toHaveClass('bg-blue-600');
  });

  it('should show loading state', () => {
    const handleClick = vi.fn();
    render(
      <QuickActionButton
        icon={<DollarSign />}
        label="Loading Action"
        onClick={handleClick}
        loading={true}
      />
    );
    
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(button.querySelector('.animate-spin')).toBeInTheDocument();
  });

  it('should handle click events', () => {
    const handleClick = vi.fn();
    render(
      <QuickActionButton
        icon={<DollarSign />}
        label="Clickable Action"
        onClick={handleClick}
      />
    );
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('should meet minimum touch target size', () => {
    const handleClick = vi.fn();
    render(
      <QuickActionButton
        icon={<DollarSign />}
        label="Touch Target"
        onClick={handleClick}
      />
    );
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('min-h-[100px]'); // Exceeds 44px minimum
    expect(button).toHaveClass('min-w-[120px]');
  });
});

describe('StatusButton Component', () => {
  it('should render different status types correctly', () => {
    const handleClick = vi.fn();
    const { rerender } = render(
      <StatusButton
        status="Angetroffen → Sale"
        onClick={handleClick}
      />
    );
    
    expect(screen.getByText('Verkauf!')).toBeInTheDocument();
    expect(screen.getByText('💰')).toBeInTheDocument();

    rerender(
      <StatusButton
        status="Angetroffen → Termin"
        onClick={handleClick}
      />
    );
    
    expect(screen.getByText('Termin vereinbaren')).toBeInTheDocument();
    expect(screen.getByText('📅')).toBeInTheDocument();

    rerender(
      <StatusButton
        status="Angetroffen → Kein Interesse"
        onClick={handleClick}
      />
    );
    
    expect(screen.getByText('Kein Interesse')).toBeInTheDocument();
    expect(screen.getByText('❌')).toBeInTheDocument();

    rerender(
      <StatusButton
        status="N/A"
        onClick={handleClick}
      />
    );
    
    expect(screen.getByText('Nicht angetroffen')).toBeInTheDocument();
    expect(screen.getByText('❓')).toBeInTheDocument();
  });

  it('should handle click events', () => {
    const handleClick = vi.fn();
    render(
      <StatusButton
        status="Angetroffen → Sale"
        onClick={handleClick}
      />
    );
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('should show loading state', () => {
    const handleClick = vi.fn();
    render(
      <StatusButton
        status="Angetroffen → Sale"
        onClick={handleClick}
        loading={true}
      />
    );
    
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(button.querySelector('.animate-spin')).toBeInTheDocument();
  });

  it('should be disabled when specified', () => {
    const handleClick = vi.fn();
    render(
      <StatusButton
        status="Angetroffen → Sale"
        onClick={handleClick}
        disabled={true}
      />
    );
    
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    
    fireEvent.click(button);
    expect(handleClick).not.toHaveBeenCalled();
  });

  it('should meet minimum touch target size', () => {
    const handleClick = vi.fn();
    render(
      <StatusButton
        status="Angetroffen → Sale"
        onClick={handleClick}
      />
    );
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('h-16'); // 64px - exceeds 44px minimum
  });
});
