
import React from 'react';
import { Outlet } from 'react-router-dom';
import { useAuth } from '@/context/auth';
import { Toaster } from '@/components/ui/toaster';
import { useIsMobile } from '@/hooks/use-mobile';
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar';
import { MainSidebar } from './MainSidebar';
import { MainHeader } from './MainHeader';

interface MainLayoutProps {
  title?: string;
  children?: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ title, children }) => {
  const { user, logout } = useAuth();
  const isMobile = useIsMobile();

  // Ensure logout is wrapped to return a Promise
  const handleLogout = async () => {
    return await logout();
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background transition-colors duration-300">
        {/* Sidebar - Mobile optimized */}
        <MainSidebar user={user} logout={handleLogout} />

        {/* Main Content */}
        <SidebarInset className="flex-1 flex flex-col overflow-hidden w-full">
          {/* Header - Mobile optimized */}
          <MainHeader title={title} user={user} isMobile={isMobile} />

          {/* Page Content - Mobile optimized */}
          <main className="flex-1 overflow-y-auto bg-background w-full scrollbar-none transition-colors duration-300">
            <div className="w-full h-full">
              {children || <Outlet />}
            </div>
          </main>
        </SidebarInset>
        
        {/* Toast notifications */}
        <Toaster />
      </div>
    </SidebarProvider>
  );
};

export default MainLayout;
