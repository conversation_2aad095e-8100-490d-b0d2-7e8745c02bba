
import React from 'react';
import { StatusButton } from '@/design-system/components/Button';
import { VisitStatus } from '@/types';
import {
  CheckCircle,
  XCircle,
  Calendar,
  UserX,
  DollarSign,
  Clock
} from 'lucide-react';
import { animations, getStaggerDelay } from '@/design-system/animations';
import { cn } from '@/lib/utils';

interface StatusButtonsProps {
  currentStatus: VisitStatus;
  onStatusUpdate: (status: VisitStatus) => void;
  isUpdating: boolean;
}

export const StatusButtons: React.FC<StatusButtonsProps> = ({
  currentStatus,
  onStatusUpdate,
  isUpdating
}) => {
  const statusOptions: VisitStatus[] = [
    'Angetroffen → Sale',
    'Angetroffen → Termin',
    'Angetroffen → Kein Interesse',
    'N/A'
  ];

  return (
    <div className="space-y-4">
      <h3 className="text-xl font-semibold text-neutral-800 mb-6">Was ist passiert?</h3>

      <div className="grid grid-cols-1 gap-4">
        {statusOptions.map((status, index) => (
          <div
            key={status}
            className={cn(
              animations.classes.slideInFromBottom,
              "opacity-0 animate-in"
            )}
            style={{
              animationDelay: `${getStaggerDelay(index)}ms`,
              animationFillMode: 'forwards'
            }}
          >
            <StatusButton
              status={status}
              onClick={() => onStatusUpdate(status)}
              loading={isUpdating}
              disabled={currentStatus === status}
            />
            {currentStatus === status && (
              <div className="flex items-center justify-center mt-2 text-sm text-green-600">
                <CheckCircle className="h-4 w-4 mr-1" />
                <span>Aktueller Status</span>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};
