// Animation utilities for consistent transitions
export const animations = {
  // Standard durations
  duration: {
    fast: 150,
    normal: 250,
    slow: 350,
    slower: 500,
  },

  // Easing functions
  easing: {
    ease: 'cubic-bezier(0.4, 0, 0.2, 1)',
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  },

  // Common animation classes
  classes: {
    // Fade animations
    fadeIn: 'animate-in fade-in duration-250',
    fadeOut: 'animate-out fade-out duration-250',
    
    // Slide animations
    slideInFromLeft: 'animate-in slide-in-from-left-4 duration-250',
    slideInFromRight: 'animate-in slide-in-from-right-4 duration-250',
    slideInFromTop: 'animate-in slide-in-from-top-4 duration-250',
    slideInFromBottom: 'animate-in slide-in-from-bottom-4 duration-250',
    
    // Scale animations
    scaleIn: 'animate-in zoom-in-95 duration-250',
    scaleOut: 'animate-out zoom-out-95 duration-250',
    
    // Combined animations
    slideAndFade: 'animate-in slide-in-from-bottom-2 fade-in duration-250',
    scaleAndFade: 'animate-in zoom-in-95 fade-in duration-250',
    
    // Hover animations
    hoverScale: 'transition-transform duration-250 hover:scale-105 active:scale-95',
    hoverLift: 'transition-all duration-250 hover:shadow-lg hover:-translate-y-1',
    
    // Loading animations
    pulse: 'animate-pulse',
    spin: 'animate-spin',
    bounce: 'animate-bounce',
  },

  // Transition styles for React components
  transitions: {
    default: {
      transition: 'all 250ms cubic-bezier(0.4, 0, 0.2, 1)',
    },
    fast: {
      transition: 'all 150ms cubic-bezier(0.4, 0, 0.2, 1)',
    },
    slow: {
      transition: 'all 350ms cubic-bezier(0.4, 0, 0.2, 1)',
    },
    transform: {
      transition: 'transform 250ms cubic-bezier(0.4, 0, 0.2, 1)',
    },
    opacity: {
      transition: 'opacity 250ms cubic-bezier(0.4, 0, 0.2, 1)',
    },
  },
} as const;

// Animation hook for React components
export const useAnimation = (
  trigger: boolean,
  options: {
    duration?: number;
    easing?: string;
    delay?: number;
  } = {}
) => {
  const {
    duration = animations.duration.normal,
    easing = animations.easing.ease,
    delay = 0,
  } = options;

  return {
    style: {
      transition: `all ${duration}ms ${easing}`,
      transitionDelay: `${delay}ms`,
    },
    className: trigger ? 'opacity-100 transform-none' : 'opacity-0 transform scale-95',
  };
};

// Stagger animation for lists
export const getStaggerDelay = (index: number, baseDelay: number = 50) => {
  return index * baseDelay;
};

// Page transition variants
export const pageTransitions = {
  slideLeft: {
    initial: { x: '100%', opacity: 0 },
    animate: { x: 0, opacity: 1 },
    exit: { x: '-100%', opacity: 0 },
    transition: { duration: 0.25, ease: 'easeInOut' },
  },
  slideRight: {
    initial: { x: '-100%', opacity: 0 },
    animate: { x: 0, opacity: 1 },
    exit: { x: '100%', opacity: 0 },
    transition: { duration: 0.25, ease: 'easeInOut' },
  },
  fade: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
    transition: { duration: 0.25 },
  },
  scaleAndFade: {
    initial: { scale: 0.95, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    exit: { scale: 0.95, opacity: 0 },
    transition: { duration: 0.25, ease: 'easeOut' },
  },
};

// CSS-in-JS animation keyframes
export const keyframes = {
  fadeIn: `
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
  `,
  slideInFromBottom: `
    @keyframes slideInFromBottom {
      from { 
        opacity: 0;
        transform: translateY(20px);
      }
      to { 
        opacity: 1;
        transform: translateY(0);
      }
    }
  `,
  scaleIn: `
    @keyframes scaleIn {
      from { 
        opacity: 0;
        transform: scale(0.95);
      }
      to { 
        opacity: 1;
        transform: scale(1);
      }
    }
  `,
  shimmer: `
    @keyframes shimmer {
      0% { background-position: -200px 0; }
      100% { background-position: calc(200px + 100%) 0; }
    }
  `,
};

// Performance optimized animation utilities
export const performanceAnimations = {
  // Use transform instead of changing layout properties
  optimizedHover: 'transition-transform duration-250 will-change-transform hover:scale-105',
  
  // Use opacity for show/hide instead of display
  optimizedFade: 'transition-opacity duration-250 will-change-opacity',
  
  // Composite layers for smooth animations
  composite: 'will-change-transform transform-gpu',
  
  // Reduce motion for accessibility
  reduceMotion: 'motion-reduce:transition-none motion-reduce:animate-none',
};

// Animation presets for common UI patterns
export const presets = {
  button: {
    base: 'transition-all duration-250 ease-out',
    hover: 'hover:scale-105 hover:shadow-lg',
    active: 'active:scale-95',
    disabled: 'disabled:opacity-50 disabled:cursor-not-allowed',
  },
  card: {
    base: 'transition-all duration-250 ease-out',
    hover: 'hover:shadow-xl hover:-translate-y-1',
    interactive: 'cursor-pointer hover:scale-[1.02] active:scale-[0.98]',
  },
  modal: {
    overlay: 'animate-in fade-in duration-250',
    content: 'animate-in slide-in-from-bottom-4 fade-in duration-250',
  },
  toast: {
    enter: 'animate-in slide-in-from-top-2 fade-in duration-250',
    exit: 'animate-out slide-out-to-top-2 fade-out duration-250',
  },
  page: {
    enter: 'animate-in slide-in-from-right-4 fade-in duration-250',
    exit: 'animate-out slide-out-to-left-4 fade-out duration-250',
  },
};
