
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Navigation, Locate } from 'lucide-react';
import { cn } from '@/lib/utils';

interface MapControlsProps {
  onLocationClick: () => void;
  onTrackingToggle: (enabled: boolean) => void;
  isTracking: boolean;
  currentLocation: { lat: number; lng: number } | null;
}

export const MapControls: React.FC<MapControlsProps> = ({ 
  onLocationClick, 
  onTrackingToggle, 
  isTracking,
  currentLocation 
}) => {
  const handleTrackingToggle = () => {
    onTrackingToggle(!isTracking);
  };

  return (
    <div className="absolute top-4 left-4 z-10 flex flex-col gap-2">
      <Button
        onClick={onLocationClick}
        className={cn(
          "h-10 w-10 p-0 rounded-full border shadow-md",
          currentLocation 
            ? "bg-blue-500 hover:bg-blue-600 text-white" 
            : "bg-white hover:bg-gray-100 text-gray-800"
        )}
        variant="outline"
      >
        <Navigation className="h-4 w-4" />
      </Button>

      <Button
        onClick={handleTrackingToggle}
        className={cn(
          "h-10 w-10 p-0 rounded-full border shadow-md",
          isTracking 
            ? "bg-green-500 hover:bg-green-600 text-white" 
            : "bg-white hover:bg-gray-100 text-gray-800"
        )}
        variant="outline"
      >
        <Locate className="h-4 w-4" />
      </Button>
    </div>
  );
};
