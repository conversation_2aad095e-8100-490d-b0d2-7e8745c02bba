
import React, { useState } from 'react';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, isToday } from 'date-fns';
import { de } from 'date-fns/locale';
import { Calendar, ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useData } from '@/context/data';
import { cn } from '@/lib/utils';
import { useNavigate } from 'react-router-dom';

export const SidebarCalendar: React.FC = () => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const { getAppointmentsByDate } = useData();
  const navigate = useNavigate();

  const monthStart = startOfMonth(currentDate);
  const monthEnd = endOfMonth(currentDate);
  const monthDays = eachDayOfInterval({ start: monthStart, end: monthEnd });

  const navigateToMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setMonth(currentDate.getMonth() - 1);
    } else {
      newDate.setMonth(currentDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  const handleDateClick = (date: Date) => {
    setSelectedDate(date);
  };

  const getAppointmentsForDate = (date: Date) => {
    const dateString = format(date, 'yyyy-MM-dd');
    return getAppointmentsByDate(dateString);
  };

  const hasAppointments = (date: Date) => {
    return getAppointmentsForDate(date).length > 0;
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-semibold flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Kalender
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/calendar')}
            className="text-xs text-blue-600 hover:text-blue-800"
          >
            Alle anzeigen
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-3">
        {/* Month Navigation */}
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateToMonth('prev')}
            className="h-7 w-7 p-0"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          
          <span className="text-sm font-medium">
            {format(currentDate, 'MMMM yyyy', { locale: de })}
          </span>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateToMonth('next')}
            className="h-7 w-7 p-0"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        {/* Calendar Grid */}
        <div className="grid grid-cols-7 gap-1 text-xs">
          {['Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa', 'So'].map(day => (
            <div key={day} className="h-6 flex items-center justify-center text-gray-500 font-medium">
              {day}
            </div>
          ))}
          
          {monthDays.map(date => {
            const appointments = getAppointmentsForDate(date);
            const hasAppts = hasAppointments(date);
            const isSelected = selectedDate && isSameDay(date, selectedDate);
            const isTodayDate = isToday(date);
            
            return (
              <button
                key={date.toISOString()}
                onClick={() => handleDateClick(date)}
                className={cn(
                  "h-6 w-6 text-xs rounded flex items-center justify-center relative transition-colors",
                  "hover:bg-gray-100",
                  isTodayDate && "bg-red-100 text-red-700 font-semibold",
                  isSelected && "bg-blue-100 text-blue-700",
                  hasAppts && "font-bold"
                )}
              >
                {format(date, 'd')}
                {hasAppts && (
                  <div className="absolute -bottom-0.5 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-500 rounded-full" />
                )}
              </button>
            );
          })}
        </div>

        {/* Selected Date Appointments */}
        {selectedDate && (
          <div className="mt-3 space-y-2">
            <div className="text-xs font-medium text-gray-700">
              {format(selectedDate, 'dd.MM.yyyy', { locale: de })}
            </div>
            
            {getAppointmentsForDate(selectedDate).map((appointment, index) => (
              <div key={index} className="text-xs p-2 bg-blue-50 rounded border-l-2 border-blue-400">
                <div className="font-medium text-blue-800">{appointment.time}</div>
                <div className="text-blue-600 truncate">{appointment.address}</div>
              </div>
            ))}
            
            {getAppointmentsForDate(selectedDate).length === 0 && (
              <div className="text-xs text-gray-500 italic">Keine Termine</div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
