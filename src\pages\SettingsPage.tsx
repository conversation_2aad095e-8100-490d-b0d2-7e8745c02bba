
import React from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/context/auth';
import { PersonalSettings } from '@/components/settings/PersonalSettings';
import { AppSettings } from '@/components/settings/AppSettings';
import { SystemSettings } from '@/components/settings/SystemSettings';

const SettingsPage: React.FC = () => {
  const { user } = useAuth();
  const isAdmin = user?.role === 'admin';
  
  if (!user) {
    return (
      <MainLayout title="Einstellungen">
        <div className="p-4 text-center">
          Sie müssen angemeldet sein, um auf die Einstellungen zuzugreifen.
        </div>
      </MainLayout>
    );
  }
  
  return (
    <MainLayout title="Einstellungen">
      <div className="space-y-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Einstellungen</h1>
          <p className="text-gray-600 mt-2">
            Konfigurieren Sie Ihre persönlichen und App-Einstellungen
          </p>
        </div>

        <Tabs defaultValue="personal" className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="personal">Persönlich</TabsTrigger>
            <TabsTrigger value="app">App-Einstellungen</TabsTrigger>
            {isAdmin && (
              <TabsTrigger value="system">System</TabsTrigger>
            )}
          </TabsList>
          
          <TabsContent value="personal">
            <PersonalSettings />
          </TabsContent>
          
          <TabsContent value="app">
            <AppSettings />
          </TabsContent>
          
          {isAdmin && (
            <TabsContent value="system">
              <SystemSettings />
            </TabsContent>
          )}
        </Tabs>
      </div>
    </MainLayout>
  );
};

export default SettingsPage;
