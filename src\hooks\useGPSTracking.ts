import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useData } from '@/context/data';
import { triggerHapticFeedback } from './useSwipeGestures';
import { toast } from 'sonner';

interface GPSPosition {
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: number;
}

interface NearbyAddress {
  addressId: string;
  distance: number;
  address: string;
  houseId: string;
}

interface GPSTrackingOptions {
  enableAutoLogging?: boolean;
  proximityThreshold?: number; // meters
  enableBackgroundTracking?: boolean;
  onAddressDetected?: (address: NearbyAddress) => void;
  onVisitLogged?: (visitId: string) => void;
}

export const useGPSTracking = (options: GPSTrackingOptions = {}) => {
  const {
    enableAutoLogging = false,
    proximityThreshold = 50, // 50 meters
    enableBackgroundTracking = false,
    onAddressDetected,
    onVisitLogged
  } = options;

  const { addresses, houses, addVisit, addDoor } = useData();
  const [currentPosition, setCurrentPosition] = useState<GPSPosition | null>(null);
  const [isTracking, setIsTracking] = useState(false);
  const [nearbyAddresses, setNearbyAddresses] = useState<NearbyAddress[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [permissionStatus, setPermissionStatus] = useState<'granted' | 'denied' | 'prompt' | 'unknown'>('unknown');
  
  const watchIdRef = useRef<number | null>(null);
  const lastLoggedAddressRef = useRef<string | null>(null);
  const positionHistoryRef = useRef<GPSPosition[]>([]);

  // Calculate distance between two coordinates using Haversine formula
  const calculateDistance = useCallback((
    lat1: number, 
    lon1: number, 
    lat2: number, 
    lon2: number
  ): number => {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = lat1 * Math.PI / 180;
    const φ2 = lat2 * Math.PI / 180;
    const Δφ = (lat2 - lat1) * Math.PI / 180;
    const Δλ = (lon2 - lon1) * Math.PI / 180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c;
  }, []);

  // Find nearby addresses based on current position
  const findNearbyAddresses = useCallback((position: GPSPosition): NearbyAddress[] => {
    const nearby: NearbyAddress[] = [];

    houses.forEach(house => {
      const distance = calculateDistance(
        position.latitude,
        position.longitude,
        house.latitude,
        house.longitude
      );

      if (distance <= proximityThreshold) {
        const address = addresses.find(a => a.id === house.addressId);
        if (address) {
          nearby.push({
            addressId: address.id,
            distance,
            address: `${address.street} ${house.houseNumber}, ${address.city}`,
            houseId: house.id
          });
        }
      }
    });

    return nearby.sort((a, b) => a.distance - b.distance);
  }, [addresses, houses, proximityThreshold, calculateDistance]);

  // Auto-log visit when arriving at an address
  const autoLogVisit = useCallback(async (nearbyAddress: NearbyAddress) => {
    if (!enableAutoLogging) return;
    
    // Prevent duplicate logging for the same address
    if (lastLoggedAddressRef.current === nearbyAddress.addressId) return;
    
    try {
      const visit = addVisit({
        houseId: nearbyAddress.houseId,
        timestamp: new Date().toISOString(),
      });

      // Add default door
      addDoor({
        visitId: visit.id,
        name: 'Haupteingang',
        status: 'N/A'
      });

      lastLoggedAddressRef.current = nearbyAddress.addressId;
      
      triggerHapticFeedback('success');
      toast.success(`Besuch automatisch erfasst: ${nearbyAddress.address}`);
      
      onVisitLogged?.(visit.id);
    } catch (error) {
      console.error('Auto-logging failed:', error);
      triggerHapticFeedback('error');
      toast.error('Automatische Erfassung fehlgeschlagen');
    }
  }, [enableAutoLogging, addVisit, addDoor, onVisitLogged]);

  // Handle position updates
  const handlePositionUpdate = useCallback((position: GeolocationPosition) => {
    const newPosition: GPSPosition = {
      latitude: position.coords.latitude,
      longitude: position.coords.longitude,
      accuracy: position.coords.accuracy,
      timestamp: Date.now()
    };

    setCurrentPosition(newPosition);
    setError(null);

    // Add to position history (keep last 10 positions)
    positionHistoryRef.current = [
      ...positionHistoryRef.current.slice(-9),
      newPosition
    ];

    // Find nearby addresses
    const nearby = findNearbyAddresses(newPosition);
    setNearbyAddresses(nearby);

    // Auto-log if enabled and address detected
    if (nearby.length > 0) {
      const closestAddress = nearby[0];
      onAddressDetected?.(closestAddress);
      
      // Auto-log if very close (within 20 meters)
      if (closestAddress.distance <= 20) {
        autoLogVisit(closestAddress);
      }
    } else {
      // Reset last logged address when moving away
      lastLoggedAddressRef.current = null;
    }
  }, [findNearbyAddresses, onAddressDetected, autoLogVisit]);

  // Handle geolocation errors
  const handlePositionError = useCallback((error: GeolocationPositionError) => {
    let errorMessage = 'GPS-Fehler aufgetreten';
    
    switch (error.code) {
      case error.PERMISSION_DENIED:
        errorMessage = 'GPS-Berechtigung verweigert';
        setPermissionStatus('denied');
        break;
      case error.POSITION_UNAVAILABLE:
        errorMessage = 'GPS-Position nicht verfügbar';
        break;
      case error.TIMEOUT:
        errorMessage = 'GPS-Timeout';
        break;
    }
    
    setError(errorMessage);
    console.error('GPS Error:', error);
  }, []);

  // Request GPS permission
  const requestPermission = useCallback(async (): Promise<boolean> => {
    if (!('geolocation' in navigator)) {
      setError('GPS nicht unterstützt');
      return false;
    }

    try {
      // Check if permissions API is available
      if ('permissions' in navigator) {
        const permission = await navigator.permissions.query({ name: 'geolocation' });
        setPermissionStatus(permission.state);
        
        if (permission.state === 'denied') {
          setError('GPS-Berechtigung verweigert. Bitte in den Einstellungen aktivieren.');
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('Permission check failed:', error);
      return true; // Fallback to trying geolocation directly
    }
  }, []);

  // Start GPS tracking
  const startTracking = useCallback(async () => {
    const hasPermission = await requestPermission();
    if (!hasPermission) return;

    if (watchIdRef.current !== null) {
      navigator.geolocation.clearWatch(watchIdRef.current);
    }

    const options: PositionOptions = {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 30000 // 30 seconds
    };

    watchIdRef.current = navigator.geolocation.watchPosition(
      handlePositionUpdate,
      handlePositionError,
      options
    );

    setIsTracking(true);
    setError(null);
  }, [requestPermission, handlePositionUpdate, handlePositionError]);

  // Stop GPS tracking
  const stopTracking = useCallback(() => {
    if (watchIdRef.current !== null) {
      navigator.geolocation.clearWatch(watchIdRef.current);
      watchIdRef.current = null;
    }
    
    setIsTracking(false);
    setCurrentPosition(null);
    setNearbyAddresses([]);
    lastLoggedAddressRef.current = null;
  }, []);

  // Get current position once
  const getCurrentPosition = useCallback(async (): Promise<GPSPosition | null> => {
    const hasPermission = await requestPermission();
    if (!hasPermission) return null;

    return new Promise((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const gpsPosition: GPSPosition = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: Date.now()
          };
          resolve(gpsPosition);
        },
        (error) => {
          handlePositionError(error);
          reject(error);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000
        }
      );
    });
  }, [requestPermission, handlePositionError]);

  // Track background tracking state to prevent infinite loops
  const shouldStartTracking = useMemo(() => {
    return enableBackgroundTracking && !isTracking;
  }, [enableBackgroundTracking, isTracking]);

  // Background tracking effect - only depends on shouldStartTracking
  useEffect(() => {
    if (shouldStartTracking) {
      startTracking();
    }

    return () => {
      if (enableBackgroundTracking) {
        stopTracking();
      }
    };
  }, [shouldStartTracking, enableBackgroundTracking, startTracking, stopTracking]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (watchIdRef.current !== null) {
        navigator.geolocation.clearWatch(watchIdRef.current);
      }
    };
  }, []);

  return {
    currentPosition,
    isTracking,
    nearbyAddresses,
    error,
    permissionStatus,
    positionHistory: positionHistoryRef.current,
    startTracking,
    stopTracking,
    getCurrentPosition,
    calculateDistance
  };
};
