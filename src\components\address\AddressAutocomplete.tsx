
import React, { useState, useRef, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';

interface AutocompleteProps {
  id: string;
  label: string;
  value: string;
  onChange: (value: string) => void;
  suggestions: string[];
  placeholder?: string;
  required?: boolean;
  className?: string;
  disabled?: boolean;
  maxSuggestions?: number;
}

const AddressAutocomplete: React.FC<AutocompleteProps> = ({
  id,
  label,
  value,
  onChange,
  suggestions,
  placeholder,
  required = false,
  className,
  disabled = false,
  maxSuggestions = 15,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Limit suggestions to display
  const displayedSuggestions = suggestions.slice(0, maxSuggestions);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;
      
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setHighlightedIndex(prev => 
            prev < displayedSuggestions.length - 1 ? prev + 1 : prev
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setHighlightedIndex(prev => (prev > 0 ? prev - 1 : 0));
          break;
        case 'Enter':
          if (highlightedIndex >= 0 && highlightedIndex < displayedSuggestions.length) {
            e.preventDefault();
            onChange(displayedSuggestions[highlightedIndex]);
            setIsOpen(false);
            setHighlightedIndex(-1);
          }
          break;
        case 'Escape':
          setIsOpen(false);
          setHighlightedIndex(-1);
          break;
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, highlightedIndex, displayedSuggestions, onChange]);

  // Scroll to highlighted item
  useEffect(() => {
    if (isOpen && highlightedIndex >= 0 && suggestionsRef.current) {
      const highlightedElement = suggestionsRef.current.children[highlightedIndex] as HTMLElement;
      if (highlightedElement) {
        highlightedElement.scrollIntoView({
          block: 'nearest',
          behavior: 'smooth',
        });
      }
    }
  }, [highlightedIndex, isOpen]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);
    
    // Check if the new value exactly matches any suggestion
    const hasExactMatch = suggestions.some(suggestion => 
      suggestion.toLowerCase() === newValue.toLowerCase()
    );
    
    // Open dropdown if no exact match, close if exact match
    setIsOpen(!hasExactMatch && newValue.length > 0);
    setHighlightedIndex(-1);
  };

  const handleSuggestionClick = (suggestion: string) => {
    onChange(suggestion);
    setIsOpen(false);
    setHighlightedIndex(-1);
    inputRef.current?.focus();
  };

  return (
    <div className="space-y-2" ref={wrapperRef}>
      <Label htmlFor={id}>{label}{required && <span className="text-red-500 ml-1">*</span>}</Label>
      <div className="relative">
        <Input
          id={id}
          ref={inputRef}
          value={value}
          onChange={handleInputChange}
          onFocus={() => {
            // Only open if there's no exact match
            const hasExactMatch = suggestions.some(suggestion => 
              suggestion.toLowerCase() === value.toLowerCase()
            );
            if (!hasExactMatch && value.length > 0) {
              setIsOpen(true);
            }
          }}
          placeholder={placeholder}
          required={required}
          className={className}
          disabled={disabled}
        />
        
        {isOpen && displayedSuggestions.length > 0 && (
          <div 
            ref={suggestionsRef}
            className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto animate-fade-in"
          >
            {displayedSuggestions.map((suggestion, index) => (
              <Button
                key={index}
                type="button"
                variant="ghost"
                className={`w-full justify-start px-3 py-2 text-left rounded-none ${
                  index === highlightedIndex 
                    ? 'bg-red-50 text-red-600' 
                    : 'hover:bg-red-50 hover:text-red-600'
                }`}
                onClick={() => handleSuggestionClick(suggestion)}
                onMouseEnter={() => setHighlightedIndex(index)}
              >
                {suggestion}
              </Button>
            ))}
          </div>
        )}
        
        {isOpen && value && displayedSuggestions.length === 0 && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg p-3 text-center text-gray-500 animate-fade-in">
            Keine Vorschläge gefunden
          </div>
        )}
      </div>
    </div>
  );
};

export default AddressAutocomplete;
