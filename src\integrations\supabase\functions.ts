
import { supabase } from './client';

// Areas Management
export const createArea = async (areaData: {
  name: string;
  description?: string;
  postal_codes?: string[];
}) => {
  const { data, error } = await supabase
    .from('areas')
    .insert([areaData])
    .select()
    .single();
  
  if (error) throw error;
  return data;
};

export const getAreas = async () => {
  const { data, error } = await supabase
    .from('areas')
    .select('*')
    .order('name');
  
  if (error) throw error;
  return data;
};

export const updateArea = async (id: string, updates: {
  name?: string;
  description?: string;
  postal_codes?: string[];
}) => {
  const { data, error } = await supabase
    .from('areas')
    .update(updates)
    .eq('id', id)
    .select()
    .single();
  
  if (error) throw error;
  return data;
};

export const deleteArea = async (id: string) => {
  const { error } = await supabase
    .from('areas')
    .delete()
    .eq('id', id);
  
  if (error) throw error;
};

// Teams Management
export const createTeam = async (teamData: {
  name: string;
  description?: string;
  area_id?: string;
  team_leader_id?: string;
}) => {
  const { data, error } = await supabase
    .from('teams')
    .insert([teamData])
    .select()
    .single();
  
  if (error) throw error;
  return data;
};

export const getTeams = async () => {
  const { data, error } = await supabase
    .from('teams')
    .select(`
      *,
      areas (
        id,
        name
      )
    `)
    .order('name');
  
  if (error) throw error;
  return data;
};

export const updateTeam = async (id: string, updates: {
  name?: string;
  description?: string;
  area_id?: string;
  team_leader_id?: string;
  is_active?: boolean;
}) => {
  const { data, error } = await supabase
    .from('teams')
    .update(updates)
    .eq('id', id)
    .select()
    .single();
  
  if (error) throw error;
  return data;
};

export const deleteTeam = async (id: string) => {
  const { error } = await supabase
    .from('teams')
    .delete()
    .eq('id', id);
  
  if (error) throw error;
};

// User Profiles Management
export const getUserProfiles = async () => {
  const { data, error } = await supabase
    .from('user_profiles')
    .select(`
      *,
      teams (
        id,
        name
      )
    `)
    .order('created_at', { ascending: false });
  
  if (error) throw error;
  return data;
};

export const updateUserProfile = async (id: string, updates: {
  team_id?: string;
  is_active?: boolean;
  total_visits?: number;
  total_sales?: number;
}) => {
  const { data, error } = await supabase
    .from('user_profiles')
    .update(updates)
    .eq('id', id)
    .select()
    .single();
  
  if (error) throw error;
  return data;
};

export const resetUserStatistics = async (userId: string, adminId: string) => {
  const { data, error } = await supabase.rpc('reset_user_statistics', {
    p_user_id: userId,
    p_admin_id: adminId
  });
  
  if (error) throw error;
  return data;
};

// Audit Logs
export const getAuditLogs = async (limit: number = 50) => {
  const { data, error } = await supabase
    .from('audit_logs')
    .select('*')
    .order('created_at', { ascending: false })
    .limit(limit);
  
  if (error) throw error;
  return data;
};

export const logAdminAction = async (actionData: {
  admin_user_id: string;
  action_type: string;
  target_type: string;
  target_id?: string;
  old_data?: any;
  new_data?: any;
  description?: string;
}) => {
  const { data, error } = await supabase.rpc('log_admin_action', {
    p_admin_user_id: actionData.admin_user_id,
    p_action_type: actionData.action_type,
    p_target_type: actionData.target_type,
    p_target_id: actionData.target_id,
    p_old_data: actionData.old_data,
    p_new_data: actionData.new_data,
    p_description: actionData.description
  });
  
  if (error) throw error;
  return data;
};
