
import React from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import ModernAddressForm from '@/components/address/ModernAddressForm';
import { Home, Building } from 'lucide-react';
import { useSwipe } from '@/hooks/use-swipe';
import { useSidebar } from '@/components/ui/sidebar';

const IndexContent = () => {
  const { setOpenMobile } = useSidebar();

  // Swipe-Handler für Sidebar öffnen
  useSwipe({
    onSwipeRight: () => {
      setOpenMobile(true);
    }
  });

  return (
    <div className="min-h-full w-full p-0 bg-gradient-to-br from-red-50 via-white to-red-50 dark:from-muted dark:via-background dark:to-muted">
      <div className="w-full h-full p-0">
        <Tabs defaultValue="efh" className="w-full h-full">
          <TabsList className="grid grid-cols-2 mb-0 h-16 bg-white/80 dark:bg-card/80 backdrop-blur-sm rounded-none shadow-lg border-0 border-red-100 dark:border-border m-0">
            <TabsTrigger 
              value="efh" 
              className="text-lg py-4 px-6 rounded-none font-semibold data-[state=active]:bg-red-500 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-200"
            >
              <Home className="mr-3 h-5 w-5" />
              EFH
            </TabsTrigger>
            <TabsTrigger 
              value="mfh" 
              className="text-lg py-4 px-6 rounded-none font-semibold data-[state=active]:bg-red-500 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-200"
            >
              <Building className="mr-3 h-5 w-5" />
              MFH
            </TabsTrigger>
          </TabsList>

          <TabsContent value="efh" className="p-0 m-0 animate-fade-in">
            <ModernAddressForm houseType="EFH" />
          </TabsContent>

          <TabsContent value="mfh" className="p-0 m-0 animate-fade-in">
            <ModernAddressForm houseType="MFH" />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default IndexContent;
