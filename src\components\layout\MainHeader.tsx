
import React from 'react';
import { User } from '@/types';
import { SidebarTrigger } from '@/components/ui/sidebar';

interface MainHeaderProps {
  title?: string;
  user: User | null;
  isMobile: boolean;
}

export const MainHeader: React.FC<MainHeaderProps> = ({ title, user, isMobile }) => {
  return (
    <header className="flex items-center justify-between h-16 md:h-20 bg-card border-b border-border w-full shadow-sm transition-colors duration-300">
      <div className="flex items-center px-4 md:px-6">
        <SidebarTrigger className="mr-3 md:mr-4 h-10 w-10 md:h-12 md:w-12 rounded-xl hover:bg-accent transition-colors touch-feedback" />
        <h1 className="text-lg md:text-2xl font-semibold truncate text-foreground">
          {title || "Dashboard"}
        </h1>
      </div>
      
      {user && !isMobile && (
        <div className="hidden md:flex items-center space-x-4 px-4 md:px-6">
          <div className="bg-red-50 dark:bg-red-950/50 px-4 py-2 rounded-xl border border-red-200 dark:border-red-800">
            <span className="text-foreground font-medium">
              {user.name} ({user.role})
            </span>
          </div>
        </div>
      )}
      
      {/* Mobile user info - shown on small screens */}
      {user && isMobile && (
        <div className="flex md:hidden items-center px-4">
          <div className="bg-red-50 dark:bg-red-950/50 px-3 py-1.5 rounded-lg border border-red-200 dark:border-red-800">
            <span className="text-sm font-medium text-foreground">
              {user.name}
            </span>
          </div>
        </div>
      )}
    </header>
  );
};
