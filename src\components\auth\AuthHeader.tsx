
import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Sparkles } from 'lucide-react';

const AuthHeader: React.FC = () => {
  return (
    <CardHeader className="text-center pb-6 pt-10 bg-gradient-to-b from-red-50 to-white">
      <div className="flex items-center justify-center mb-6">
        <div className="relative">
          <Sparkles className="h-12 w-12 text-red-600 animate-pulse" />
          <div className="absolute inset-0 h-12 w-12 text-red-400 animate-ping opacity-30">
            <Sparkles className="h-12 w-12" />
          </div>
        </div>
      </div>
      <CardTitle className="text-5xl font-bold bg-gradient-to-r from-red-600 to-red-800 bg-clip-text text-transparent">
        Laufliste
      </CardTitle>
      <p className="text-gray-500 mt-2 text-lg">Willkommen zurück</p>
    </CardHeader>
  );
};

export default AuthHeader;
