
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { useData } from '@/context/data';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';
import { useIsMobile } from '@/hooks/use-mobile';
import { Users, CheckCircle, Target, Calendar } from 'lucide-react';

interface BeraterDashboardProps {
  userId: string;
}

const BeraterDashboard: React.FC<BeraterDashboardProps> = ({ userId }) => {
  const { visits, doors, products } = useData();
  const isMobile = useIsMobile();
  
  // Filtere Besuche für den aktuellen Berater
  const userVisits = visits.filter(v => v.userId === userId);
  
  // Berechne die Anzahl der angetroffenen Türen
  const doorsWithInteraction = doors.filter(d => 
    d.status === 'Angetroffen → Termin' || 
    d.status === 'Angetroffen → <PERSON>in Interesse' || 
    d.status === 'Angetroffen → Sale'
  );
  
  // Berechne die Anzahl der Verkäufe (Produkte) des Beraters
  const userProducts = products.filter(p => p.userId === userId);
  
  // Berechne die heutigen Besuche
  const today = new Date().toISOString().split('T')[0];
  const todayVisits = userVisits.filter(v => v.timestamp.startsWith(today));

  return (
    <div className={`space-y-6 ${isMobile ? 'px-4 py-6' : 'p-6'} bg-gradient-to-br from-gray-50 to-gray-100 min-h-full animate-fade-in`}>
      {/* Header */}
      <Card className="glass-card rounded-3xl border-0 shadow-2xl">
        <CardContent className={`${isMobile ? 'p-6' : 'p-8'}`}>
          <div className="text-center">
            <h2 className={`${isMobile ? 'text-2xl' : 'text-3xl'} font-bold text-gray-800 mb-2`}>
              Berater Dashboard
            </h2>
            <p className={`text-muted-foreground ${isMobile ? 'text-sm' : 'text-base'}`}>
              Willkommen zurück! Hier ist Ihre aktuelle Übersicht.
            </p>
          </div>
        </CardContent>
      </Card>
      
      {/* Statistics Cards */}
      <div className={`grid ${isMobile ? 'grid-cols-2 gap-4' : 'grid-cols-4 gap-6'}`}>
        <Card className="glass-card hover-lift rounded-3xl border-0 shadow-2xl animate-scale-in">
          <CardContent className={`${isMobile ? 'p-4' : 'p-6'} text-center`}>
            <div className="flex items-center justify-center mb-3">
              <Users className={`${isMobile ? 'h-6 w-6' : 'h-8 w-8'} text-blue-600`} />
            </div>
            <p className={`text-xs text-muted-foreground mb-2 ${isMobile ? 'text-xs' : 'text-sm'}`}>
              Gesamtbesuche
            </p>
            <p className={`${isMobile ? 'text-2xl' : 'text-3xl'} font-bold text-gray-800`}>
              {userVisits.length}
            </p>
          </CardContent>
        </Card>
        
        <Card className="glass-card hover-lift rounded-3xl border-0 shadow-2xl animate-scale-in">
          <CardContent className={`${isMobile ? 'p-4' : 'p-6'} text-center`}>
            <div className="flex items-center justify-center mb-3">
              <Target className={`${isMobile ? 'h-6 w-6' : 'h-8 w-8'} text-orange-600`} />
            </div>
            <p className={`text-xs text-muted-foreground mb-2 ${isMobile ? 'text-xs' : 'text-sm'}`}>
              Angetroffen
            </p>
            <p className={`${isMobile ? 'text-2xl' : 'text-3xl'} font-bold text-gray-800`}>
              {doorsWithInteraction.length}
            </p>
          </CardContent>
        </Card>
        
        <Card className="glass-card hover-lift rounded-3xl border-0 shadow-2xl animate-scale-in">
          <CardContent className={`${isMobile ? 'p-4' : 'p-6'} text-center`}>
            <div className="flex items-center justify-center mb-3">
              <CheckCircle className={`${isMobile ? 'h-6 w-6' : 'h-8 w-8'} text-green-600`} />
            </div>
            <p className={`text-xs text-muted-foreground mb-2 ${isMobile ? 'text-xs' : 'text-sm'}`}>
              Verkäufe
            </p>
            <p className={`${isMobile ? 'text-2xl' : 'text-3xl'} font-bold text-green-600`}>
              {userProducts.length}
            </p>
          </CardContent>
        </Card>
        
        <Card className="glass-card hover-lift rounded-3xl border-0 shadow-2xl animate-scale-in">
          <CardContent className={`${isMobile ? 'p-4' : 'p-6'} text-center`}>
            <div className="flex items-center justify-center mb-3">
              <Calendar className={`${isMobile ? 'h-6 w-6' : 'h-8 w-8'} text-red-600`} />
            </div>
            <p className={`text-xs text-muted-foreground mb-2 ${isMobile ? 'text-xs' : 'text-sm'}`}>
              Heute
            </p>
            <p className={`${isMobile ? 'text-2xl' : 'text-3xl'} font-bold text-red-600`}>
              {todayVisits.length}
            </p>
            <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-muted-foreground mt-1`}>
              Besuche
            </p>
          </CardContent>
        </Card>
      </div>
      
      {/* Current Period Card */}
      <Card className="glass-card hover-lift rounded-3xl border-0 shadow-2xl animate-slide-in">
        <CardHeader className={`${isMobile ? 'pb-4' : 'pb-6'}`}>
          <CardTitle className={`${isMobile ? 'text-lg' : 'text-xl'} font-bold text-gray-800 flex items-center gap-3`}>
            <Calendar className={`${isMobile ? 'h-5 w-5' : 'h-6 w-6'} text-blue-600`} />
            Aktuelle Periode
          </CardTitle>
          <CardDescription className={`${isMobile ? 'text-sm' : 'text-base'}`}>
            {format(new Date(), "'Woche vom' d. MMMM yyyy", { locale: de })}
          </CardDescription>
        </CardHeader>
        <CardContent className={`${isMobile ? 'pt-0 pb-6' : 'pt-0 pb-8'}`}>
          <p className={`${isMobile ? 'text-sm' : 'text-base'} text-gray-700`}>
            Tragen Sie weiterhin Ihre Besuche ein und pflegen Sie die Ergebnisse.
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default BeraterDashboard;
