
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Download } from 'lucide-react';

interface StatisticsHeaderProps {
  onExport: () => void;
  isMobile: boolean;
}

export const StatisticsHeader: React.FC<StatisticsHeaderProps> = ({
  onExport,
  isMobile
}) => {
  return (
    <Card className="glass-card rounded-3xl border-0 shadow-2xl">
      <CardContent className="p-6">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold text-gray-800">Statistiken</h2>
          <Button 
            onClick={onExport}
            variant="outline" 
            size={isMobile ? "lg" : "default"}
            className={`${isMobile ? 'h-14 px-6' : 'h-12 px-4'} rounded-xl border-2 border-gray-300 hover:border-red-500 hover:text-red-600 transition-all duration-200 hover-scale touch-feedback`}
          >
            <Download size={isMobile ? 20 : 16} />
            {isMobile && <span className="ml-2">Export</span>}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
