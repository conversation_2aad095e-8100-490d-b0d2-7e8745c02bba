import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import { AlertTriangle, XCircle, Info, CheckCircle, AlertCircle } from 'lucide-react';
import { Button } from './Button';

// Error message variants
const errorMessageVariants = cva(
  'rounded-xl border p-4 transition-all duration-250',
  {
    variants: {
      variant: {
        error: 'bg-red-50 border-red-200 text-red-800',
        warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
        info: 'bg-blue-50 border-blue-200 text-blue-800',
        success: 'bg-green-50 border-green-200 text-green-800',
      },
      size: {
        sm: 'text-sm',
        md: 'text-base',
        lg: 'text-lg',
      }
    },
    defaultVariants: {
      variant: 'error',
      size: 'md',
    },
  }
);

export interface ErrorMessageProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof errorMessageVariants> {
  title?: string;
  message: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  onDismiss?: () => void;
}

const ErrorMessage = React.forwardRef<HTMLDivElement, ErrorMessageProps>(
  ({ className, variant, size, title, message, action, onDismiss, ...props }, ref) => {
    const icons = {
      error: XCircle,
      warning: AlertTriangle,
      info: Info,
      success: CheckCircle,
    };

    const Icon = icons[variant || 'error'];

    return (
      <div
        ref={ref}
        className={cn(errorMessageVariants({ variant, size, className }))}
        {...props}
      >
        <div className="flex items-start space-x-3">
          <Icon className="h-5 w-5 flex-shrink-0 mt-0.5" />
          <div className="flex-1 min-w-0">
            {title && (
              <h3 className="font-semibold mb-1">
                {title}
              </h3>
            )}
            <p className="text-sm leading-relaxed">
              {message}
            </p>
            {action && (
              <div className="mt-3">
                <Button
                  variant={variant === 'error' ? 'error' : 'primary'}
                  size="sm"
                  onClick={action.onClick}
                >
                  {action.label}
                </Button>
              </div>
            )}
          </div>
          {onDismiss && (
            <button
              onClick={onDismiss}
              className="flex-shrink-0 p-1 rounded-md hover:bg-black/5 transition-colors"
              aria-label="Schließen"
            >
              <XCircle className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>
    );
  }
);

ErrorMessage.displayName = 'ErrorMessage';

// Specialized error components
export const NotFoundError: React.FC<{
  title?: string;
  message?: string;
  onGoHome?: () => void;
  onGoBack?: () => void;
}> = ({ 
  title = 'Nicht gefunden',
  message = 'Die angeforderte Seite oder Ressource konnte nicht gefunden werden.',
  onGoHome,
  onGoBack
}) => (
  <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
    <div className="max-w-md w-full">
      <ErrorMessage
        variant="error"
        title={title}
        message={message}
        className="mb-4"
      />
      <div className="flex space-x-3">
        {onGoBack && (
          <Button
            variant="outline"
            onClick={onGoBack}
            className="flex-1"
          >
            Zurück
          </Button>
        )}
        {onGoHome && (
          <Button
            variant="primary"
            onClick={onGoHome}
            className="flex-1"
          >
            Zur Startseite
          </Button>
        )}
      </div>
    </div>
  </div>
);

export const NetworkError: React.FC<{
  onRetry?: () => void;
  onGoOffline?: () => void;
}> = ({ onRetry, onGoOffline }) => (
  <ErrorMessage
    variant="warning"
    title="Verbindungsproblem"
    message="Es konnte keine Verbindung zum Server hergestellt werden. Überprüfen Sie Ihre Internetverbindung."
    action={onRetry ? {
      label: 'Erneut versuchen',
      onClick: onRetry
    } : undefined}
  />
);

export const ValidationError: React.FC<{
  errors: string[];
  onDismiss?: () => void;
}> = ({ errors, onDismiss }) => (
  <ErrorMessage
    variant="warning"
    title="Eingabefehler"
    message={errors.length === 1 ? errors[0]! : `${errors.length} Fehler gefunden:`}
    onDismiss={onDismiss}
  />
);

export const SuccessMessage: React.FC<{
  title?: string;
  message: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  onDismiss?: () => void;
}> = ({ title, message, action, onDismiss }) => (
  <ErrorMessage
    variant="success"
    title={title}
    message={message}
    action={action}
    onDismiss={onDismiss}
  />
);

// Toast-style notifications
export const ToastError: React.FC<{
  message: string;
  onDismiss?: () => void;
}> = ({ message, onDismiss }) => (
  <div className="fixed top-4 right-4 z-50 max-w-sm">
    <ErrorMessage
      variant="error"
      message={message}
      onDismiss={onDismiss}
      className="shadow-lg"
    />
  </div>
);

export const ToastSuccess: React.FC<{
  message: string;
  onDismiss?: () => void;
}> = ({ message, onDismiss }) => (
  <div className="fixed top-4 right-4 z-50 max-w-sm">
    <ErrorMessage
      variant="success"
      message={message}
      onDismiss={onDismiss}
      className="shadow-lg"
    />
  </div>
);

export { ErrorMessage, errorMessageVariants };
export type { ErrorMessageProps };
