import { useState, useEffect, useCallback } from "react";
import { User } from "../../types";
import { mockUsers } from "./mockUsers";
import { v4 as uuidv4 } from 'uuid';
import { toast } from 'sonner';
import { AuthError, RegisterData, AuthResult, SessionInfo } from "./types";
import { validateRegistrationData, validateLoginCredentials } from "../../utils/validation";

export const useAuthProvider = () => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<AuthError | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [sessionExpiry, setSessionExpiry] = useState<Date | null>(null);

  // Enhanced helper function to create auth errors
  const createAuthError = (
    type: AuthError['type'],
    message: string,
    field?: string,
    code?: string,
    retryable: boolean = true
  ): AuthError => ({
    type,
    message,
    field,
    code,
    timestamp: new Date(),
    retryable,
  });

  // Clear error function
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Check if user is authenticated
  const isAuthenticated = user !== null;

  // Load users from localStorage on initial load
  useEffect(() => {
    console.log("Initial load, checking for stored users and current user...");

    // First load the mock users to ensure we always have these accounts
    setUsers(mockUsers);
    localStorage.setItem("users", JSON.stringify(mockUsers));

    // Check for remembered user session
    const storedUser = localStorage.getItem("currentUser");
    const rememberMe = localStorage.getItem("rememberMe") === "true";
    const sessionExpiry = localStorage.getItem("sessionExpiry");

    if (storedUser) {
      try {
        const parsedUser = JSON.parse(storedUser);

        // Check if session is still valid
        if (sessionExpiry) {
          const expiryTime = new Date(sessionExpiry);
          if (new Date() < expiryTime) {
            setUser(parsedUser);
            setSessionExpiry(expiryTime);
          } else {
            // Session expired, clear storage
            localStorage.removeItem("currentUser");
            localStorage.removeItem("sessionExpiry");
            localStorage.removeItem("rememberMe");
          }
        } else if (rememberMe) {
          // Legacy support for remember me without expiry
          setUser(parsedUser);
          // Set a default expiry for legacy sessions
          const defaultExpiry = new Date();
          defaultExpiry.setDate(defaultExpiry.getDate() + 30);
          setSessionExpiry(defaultExpiry);
          localStorage.setItem("sessionExpiry", defaultExpiry.toISOString());
        }
      } catch (err) {
        console.error("Error parsing stored user:", err);
        localStorage.removeItem("currentUser");
        localStorage.removeItem("sessionExpiry");
        localStorage.removeItem("rememberMe");
      }
    }

    setIsLoading(false);
  }, []);

  const login = async (emailOrName: string, password: string, rememberMe: boolean = false): Promise<AuthResult> => {
    setIsLoading(true);
    setError(null);

    try {
      // Validate input
      const validationErrors = validateLoginCredentials(emailOrName, password);
      if (validationErrors.length > 0) {
        const firstError = validationErrors[0];
        const authError = createAuthError('validation', firstError.message, firstError.field);
        setError(authError);
        return { success: false, error: authError };
      }

      // Simulate optimized API call delay (reduced from 500ms to 150ms for better UX)
      await new Promise((resolve) => setTimeout(resolve, 150));

      console.log("Attempting to log in with:", emailOrName, "password length:", password.length);
      console.log("Available users:", users.map(u => ({ email: u.email, name: u.name })));

      // Find user with matching email/name and password
      const foundUser = users.find(
        (u) => (u.email === emailOrName || u.name === emailOrName) && u.password === password
      );

      if (foundUser) {
        console.log("User found:", foundUser.name, foundUser.role);
        const { password: _, ...userWithoutPassword } = foundUser;

        // Set user state
        setUser(userWithoutPassword as User);

        // Enhanced session management
        const expiryDate = new Date();
        if (rememberMe) {
          // Remember me: session lasts 30 days
          expiryDate.setDate(expiryDate.getDate() + 30);
        } else {
          // Regular session: lasts 24 hours
          expiryDate.setHours(expiryDate.getHours() + 24);
        }

        setSessionExpiry(expiryDate);

        // Store session data
        localStorage.setItem("currentUser", JSON.stringify(userWithoutPassword));
        localStorage.setItem("rememberMe", rememberMe.toString());
        localStorage.setItem("sessionExpiry", expiryDate.toISOString());

        toast.success(`Willkommen zurück, ${foundUser.name}!`);
        return { success: true, user: userWithoutPassword as User };
      } else {
        console.log("User not found or password incorrect");
        const authError = createAuthError('authentication', 'Ungültige Anmeldeinformationen', undefined, 'INVALID_CREDENTIALS');
        setError(authError);
        toast.error("Anmeldung fehlgeschlagen. Ungültiger Benutzername oder Passwort.");
        return { success: false, error: authError };
      }
    } catch (err) {
      console.error("Login error:", err);
      const authError = createAuthError('network', 'Anmeldefehler. Bitte versuchen Sie es erneut.', undefined, 'NETWORK_ERROR');
      setError(authError);
      toast.error("Ein Fehler ist aufgetreten.");
      return { success: false, error: authError };
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (userData: RegisterData): Promise<AuthResult> => {
    setIsLoading(true);
    setError(null);

    try {
      const { name, email, password, confirmPassword } = userData;

      // Validate registration data
      const validationErrors = validateRegistrationData(name, email, password, confirmPassword);
      if (validationErrors.length > 0) {
        const firstError = validationErrors[0];
        const authError = createAuthError('validation', firstError.message, firstError.field);
        setError(authError);
        return { success: false, error: authError };
      }

      // Simulate optimized API call delay (reduced from 1000ms to 200ms)
      await new Promise((resolve) => setTimeout(resolve, 200));

      // Check if user with this email already exists
      const existingUser = users.find((u) => u.email === email);
      if (existingUser) {
        const authError = createAuthError('validation', 'Diese E-Mail-Adresse wird bereits verwendet', 'email', 'EMAIL_EXISTS');
        setError(authError);
        toast.error("Diese E-Mail-Adresse existiert bereits.");
        return { success: false, error: authError };
      }

      // Create new user
      const newUser: User = {
        id: uuidv4(),
        name: name.trim(),
        email: email.toLowerCase().trim(),
        role: "berater", // Default role for new registrations
        teamId: "team1", // Default team
        password, // In a real app, you would hash this
      };

      // Update users list
      const updatedUsers = [...users, newUser];
      setUsers(updatedUsers);
      localStorage.setItem("users", JSON.stringify(updatedUsers));

      // Automatically log in the new user with a 24-hour session
      const { password: _, ...userWithoutPassword } = newUser;
      setUser(userWithoutPassword as User);

      const expiryDate = new Date();
      expiryDate.setHours(expiryDate.getHours() + 24);
      setSessionExpiry(expiryDate);

      localStorage.setItem("currentUser", JSON.stringify(userWithoutPassword));
      localStorage.setItem("rememberMe", "false");
      localStorage.setItem("sessionExpiry", expiryDate.toISOString());

      toast.success(`Willkommen, ${newUser.name}! Registrierung erfolgreich.`);
      return { success: true, user: userWithoutPassword as User };
    } catch (err) {
      console.error("Registration error:", err);
      const authError = createAuthError('server', 'Registrierungsfehler. Bitte versuchen Sie es erneut.', undefined, 'SERVER_ERROR');
      setError(authError);
      toast.error("Ein Fehler ist aufgetreten.");
      return { success: false, error: authError };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    setUser(null);
    setError(null);
    setSessionExpiry(null);

    // Clear all session data
    localStorage.removeItem("currentUser");
    localStorage.removeItem("sessionExpiry");
    localStorage.removeItem("rememberMe");

    toast.info("Abmeldung erfolgreich.");

    // Return resolved promise
    return Promise.resolve();
  };

  // Session refresh functionality
  const refreshSession = async (): Promise<boolean> => {
    try {
      const currentUser = localStorage.getItem("currentUser");
      const sessionExpiryStr = localStorage.getItem("sessionExpiry");
      const rememberMe = localStorage.getItem("rememberMe") === "true";

      if (!currentUser || !sessionExpiryStr) {
        return false;
      }

      const sessionExpiryDate = new Date(sessionExpiryStr);
      const now = new Date();

      // Check if session is still valid
      if (now > sessionExpiryDate) {
        await logout();
        return false;
      }

      // Extend session if it's close to expiry (within 1 hour)
      const oneHourFromNow = new Date(now.getTime() + 60 * 60 * 1000);
      if (sessionExpiryDate < oneHourFromNow) {
        const newExpiryDate = new Date();
        if (rememberMe) {
          newExpiryDate.setDate(newExpiryDate.getDate() + 30);
        } else {
          newExpiryDate.setHours(newExpiryDate.getHours() + 24);
        }

        setSessionExpiry(newExpiryDate);
        localStorage.setItem("sessionExpiry", newExpiryDate.toISOString());
      }

      return true;
    } catch (error) {
      console.error("Session refresh error:", error);
      return false;
    }
  };

  const updateUser = async (updatedUser: User): Promise<void> => {
    try {
      setUsers(users.map(u => u.id === updatedUser.id ? updatedUser : u));

      // If the current user is being updated, update the local state and storage
      if (user && user.id === updatedUser.id) {
        setUser(updatedUser);
        localStorage.setItem("currentUser", JSON.stringify(updatedUser));
      }

      // Update users in localStorage
      const updatedUsers = users.map(u => u.id === updatedUser.id ? updatedUser : u);
      localStorage.setItem("users", JSON.stringify(updatedUsers));
    } catch (error) {
      console.error("Error updating user:", error);
      throw error;
    }
  };

  const createUser = async (newUser: Omit<User, "id">): Promise<User> => {
    try {
      const userWithId = {
        ...newUser,
        id: uuidv4(),
      };

      const updatedUsers = [...users, userWithId];
      setUsers(updatedUsers);
      localStorage.setItem("users", JSON.stringify(updatedUsers));

      return userWithId;
    } catch (error) {
      console.error("Error creating user:", error);
      throw error;
    }
  };

  // Enhanced Google login implementation
  const loginWithGoogle = async (): Promise<AuthResult> => {
    setIsLoading(true);
    setError(null);

    try {
      // Simulate API call delay for Google auth (reduced for better UX)
      await new Promise((resolve) => setTimeout(resolve, 800));

      // For demonstration purposes, create a Google user
      const googleUser: User = {
        id: uuidv4(),
        name: "Google User",
        email: `user_${Math.floor(Math.random() * 10000)}@gmail.com`,
        role: "berater", // Default role
        teamId: "team1", // Default team
      };

      // Add user to the users array if not already present
      if (!users.some(u => u.email === googleUser.email)) {
        setUsers(prev => [...prev, googleUser]);
      }

      // Set session with 24-hour expiry
      const expiryDate = new Date();
      expiryDate.setHours(expiryDate.getHours() + 24);
      setSessionExpiry(expiryDate);

      setUser(googleUser);
      localStorage.setItem("currentUser", JSON.stringify(googleUser));
      localStorage.setItem("sessionExpiry", expiryDate.toISOString());
      localStorage.setItem("rememberMe", "false");

      toast.success(`Willkommen, ${googleUser.name}!`);
      return { success: true, user: googleUser };
    } catch (err) {
      console.error("Google login error:", err);
      const authError = createAuthError('authentication', 'Google-Anmeldung fehlgeschlagen', undefined, 'GOOGLE_AUTH_ERROR');
      setError(authError);
      toast.error("Ein Fehler ist bei der Google-Anmeldung aufgetreten.");
      return { success: false, error: authError };
    } finally {
      setIsLoading(false);
    }
  };

  return {
    user,
    login,
    register,
    logout,
    isLoading,
    error,
    users,
    updateUser,
    createUser,
    loginWithGoogle,
    clearError,
    isAuthenticated,
    sessionExpiry,
    refreshSession,
  };
};
