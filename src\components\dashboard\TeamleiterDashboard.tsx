
import React, { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useTeamData } from './team/useTeamData';
import TeamStatsCards from './team/TeamStatsCards';
import TeamsOverviewTable from './team/TeamsOverviewTable';
import CompletedVisitsTable from './team/CompletedVisitsTable';
import { TeamleiterDashboardProps } from './team/types';
import { useIsMobile } from '@/hooks/use-mobile';
import { Eye, CheckCircle } from 'lucide-react';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';

const TeamleiterDashboard: React.FC<TeamleiterDashboardProps> = ({ teamId }) => {
  const [activeTab, setActiveTab] = useState<string>("overview");
  const { 
    loading, 
    teamStats, 
    selectedTeam, 
    completedVisits 
  } = useTeamData(teamId);
  const isMobile = useIsMobile();

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 flex items-center justify-center p-4">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Lade Teams...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 p-4 md:p-6">
      <div className="max-w-7xl mx-auto space-y-6 md:space-y-8">
        {/* Header Section */}
        <div className="text-center md:text-left space-y-2 md:space-y-4 animate-fade-in">
          <h1 className={`font-bold text-gray-800 ${isMobile ? 'text-2xl' : 'text-4xl'}`}>
            Teams Übersicht
          </h1>
          {selectedTeam && (
            <p className={`text-gray-600 ${isMobile ? 'text-sm' : 'text-lg'}`}>
              {selectedTeam.name} | {teamStats.find(t => t.id === teamId)?.memberCount || 0} Mitglieder
            </p>
          )}
          <p className={`text-gray-500 ${isMobile ? 'text-xs' : 'text-sm'}`}>
            {format(new Date(), "'Stand:' d. MMMM yyyy", { locale: de })}
          </p>
        </div>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className={`grid w-full grid-cols-2 ${isMobile ? 'h-12' : 'h-14'} bg-white/80 backdrop-blur-sm border border-white/20 shadow-lg rounded-2xl`}>
            <TabsTrigger 
              value="overview" 
              className={`${isMobile ? 'text-sm py-2' : 'text-base py-3'} data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-600 data-[state=active]:text-white rounded-xl transition-all duration-300`}
            >
              <Eye className="h-4 w-4 mr-2" />
              Teams Übersicht
            </TabsTrigger>
            <TabsTrigger 
              value="completed" 
              className={`${isMobile ? 'text-sm py-2' : 'text-base py-3'} data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-emerald-600 data-[state=active]:text-white rounded-xl transition-all duration-300`}
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Abgeschlossen
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-6 animate-fade-in">
            <TeamStatsCards teamStats={teamStats} />
            <TeamsOverviewTable teamStats={teamStats} />
          </TabsContent>
            
          <TabsContent value="completed" className="space-y-6 animate-fade-in">
            <CompletedVisitsTable 
              completedVisits={completedVisits}
              teamName={selectedTeam?.name} 
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default TeamleiterDashboard;
