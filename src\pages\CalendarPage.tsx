
import React, { useState } from 'react';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, isToday } from 'date-fns';
import { de } from 'date-fns/locale';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useData } from '@/context/data';
import { Calendar, ChevronLeft, ChevronRight, Clock, MapPin } from 'lucide-react';
import { cn } from '@/lib/utils';

const CalendarPage: React.FC = () => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(new Date());
  const { getAppointmentsByDate, getUpcomingAppointments } = useData();

  const monthStart = startOfMonth(currentDate);
  const monthEnd = endOfMonth(currentDate);
  const monthDays = eachDayOfInterval({ start: monthStart, end: monthEnd });
  const upcomingAppointments = getUpcomingAppointments().slice(0, 5);

  const navigateToMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setMonth(currentDate.getMonth() - 1);
    } else {
      newDate.setMonth(currentDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  const handleDateClick = (date: Date) => {
    setSelectedDate(date);
  };

  const getAppointmentsForDate = (date: Date) => {
    const dateString = format(date, 'yyyy-MM-dd');
    return getAppointmentsByDate(dateString);
  };

  const hasAppointments = (date: Date) => {
    return getAppointmentsForDate(date).length > 0;
  };

  return (
    <MainLayout title="Termine">
      <div className="min-h-full bg-gradient-to-br from-red-50 via-white to-red-50 p-4 md:p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Header */}
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-800 mb-2">Terminkalender</h1>
            <p className="text-gray-600">Übersicht Ihrer geplanten Termine</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Calendar */}
            <div className="lg:col-span-2">
              <Card className="w-full">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Calendar className="h-5 w-5" />
                      {format(currentDate, 'MMMM yyyy', { locale: de })}
                    </CardTitle>
                    
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigateToMonth('prev')}
                      >
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentDate(new Date())}
                      >
                        Heute
                      </Button>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigateToMonth('next')}
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent>
                  {/* Calendar Grid */}
                  <div className="grid grid-cols-7 gap-2">
                    {/* Weekday Headers */}
                    {['Montag', 'Dienstag', 'Mittwoch', 'Donnerstag', 'Freitag', 'Samstag', 'Sonntag'].map(day => (
                      <div key={day} className="h-10 flex items-center justify-center text-sm font-medium text-gray-500">
                        {day.slice(0, 2)}
                      </div>
                    ))}
                    
                    {/* Calendar Days */}
                    {monthDays.map(date => {
                      const appointments = getAppointmentsForDate(date);
                      const hasAppts = hasAppointments(date);
                      const isSelected = selectedDate && isSameDay(date, selectedDate);
                      const isTodayDate = isToday(date);
                      
                      return (
                        <button
                          key={date.toISOString()}
                          onClick={() => handleDateClick(date)}
                          className={cn(
                            "h-20 p-2 rounded-lg border-2 border-transparent transition-all",
                            "hover:border-gray-200 hover:bg-gray-50",
                            isTodayDate && "bg-red-50 border-red-200",
                            isSelected && "border-blue-400 bg-blue-50",
                            hasAppts && "bg-green-50"
                          )}
                        >
                          <div className="text-left">
                            <div className={cn(
                              "text-sm font-medium",
                              isTodayDate && "text-red-700",
                              isSelected && "text-blue-700"
                            )}>
                              {format(date, 'd')}
                            </div>
                            
                            {hasAppts && (
                              <div className="mt-1">
                                <Badge 
                                  variant="secondary" 
                                  className="text-xs px-1 py-0 bg-blue-100 text-blue-700"
                                >
                                  {appointments.length}
                                </Badge>
                              </div>
                            )}
                          </div>
                        </button>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar with Details */}
            <div className="space-y-6">
              {/* Selected Date Details */}
              {selectedDate && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">
                      {format(selectedDate, 'EEEE, dd.MM.yyyy', { locale: de })}
                    </CardTitle>
                  </CardHeader>
                  
                  <CardContent>
                    {getAppointmentsForDate(selectedDate).length > 0 ? (
                      <div className="space-y-3">
                        {getAppointmentsForDate(selectedDate).map((appointment, index) => (
                          <div key={index} className="p-3 bg-blue-50 rounded-lg border-l-4 border-blue-400">
                            <div className="flex items-center gap-2 text-blue-800 font-medium">
                              <Clock className="h-4 w-4" />
                              {appointment.time}
                            </div>
                            <div className="flex items-start gap-2 mt-1 text-blue-600 text-sm">
                              <MapPin className="h-4 w-4 mt-0.5 flex-shrink-0" />
                              <span>{appointment.address}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-gray-500 text-sm">Keine Termine an diesem Tag</p>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Upcoming Appointments */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Nächste Termine</CardTitle>
                </CardHeader>
                
                <CardContent>
                  {upcomingAppointments.length > 0 ? (
                    <div className="space-y-3">
                      {upcomingAppointments.map((appointment, index) => (
                        <div key={index} className="p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center gap-2 text-gray-800 font-medium text-sm">
                            <Calendar className="h-4 w-4" />
                            {format(new Date(appointment.date), 'dd.MM.yyyy')} um {appointment.time}
                          </div>
                          <div className="flex items-start gap-2 mt-1 text-gray-600 text-xs">
                            <MapPin className="h-3 w-3 mt-0.5 flex-shrink-0" />
                            <span>{appointment.address}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 text-sm">Keine anstehenden Termine</p>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default CalendarPage;
