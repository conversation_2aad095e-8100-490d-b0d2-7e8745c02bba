
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { Target } from 'lucide-react';

interface StatusChartProps {
  data: Array<{ name: string; count: number }>;
  isMobile: boolean;
}

export const StatusChart: React.FC<StatusChartProps> = ({ data, isMobile }) => {
  return (
    <Card className="glass-card hover-lift rounded-3xl border-0 shadow-2xl animate-slide-in">
      <CardHeader className="pb-4">
        <CardTitle className="text-xl font-bold text-gray-800 flex items-center gap-3">
          <Target className="h-6 w-6 text-blue-600" />
          Besuchsstatus
        </CardTitle>
      </CardHeader>
      <CardContent className="pb-6">
        <div style={{ height: isMobile ? '250px' : '320px' }} className="w-full">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
              <XAxis 
                dataKey="name" 
                tick={{ fontSize: isMobile ? 12 : 14 }}
                stroke="#64748b"
              />
              <YAxis 
                tick={{ fontSize: isMobile ? 12 : 14 }}
                stroke="#64748b"
              />
              <Tooltip 
                contentStyle={{
                  backgroundColor: 'rgba(255, 255, 255, 0.95)',
                  border: 'none',
                  borderRadius: '12px',
                  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
                }}
              />
              <Legend />
              <Bar 
                dataKey="count" 
                name="Anzahl" 
                fill="#3b82f6" 
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
};
