
import React from 'react';
import { MapPin } from 'lucide-react';
import { Card, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Address, House, Door } from '@/types';

interface AddressHeaderProps {
  address: Address;
  house: House;
  step: number;
  existingDoors?: Door[];
}

export const AddressHeader: React.FC<AddressHeaderProps> = ({ 
  address, 
  house, 
  step, 
  existingDoors = [] 
}) => {
  return (
    <Card className="w-full bg-white/95 backdrop-blur-sm border-0 shadow-2xl rounded-3xl overflow-hidden animate-fade-in">
      <CardHeader className="text-center pb-6 pt-8 bg-gradient-to-b from-red-50/50 to-transparent">
        <div className="flex items-center justify-center mb-4">
          <MapPin className="h-8 w-8 text-red-600" />
        </div>
        <CardTitle className="text-2xl font-bold text-gray-800">
          EF<PERSON> Besuch - Schritt {step}
        </CardTitle>
        
        {/* Address Info */}
        <div className="mt-4 p-4 bg-white/60 rounded-xl">
          <p className="text-lg font-semibold text-gray-800">
            {address.street} {house.houseNumber}
          </p>
          <p className="text-gray-600">
            {address.zipCode} {address.city}
          </p>
        </div>

        {/* Door info for step 2 */}
        {step === 2 && existingDoors.length > 0 && (
          <div className="mt-4 p-3 bg-green-50/60 rounded-xl border border-green-200">
            <p className="text-sm font-medium text-green-800">
              ✓ Tür: {existingDoors[0].name}
            </p>
          </div>
        )}
      </CardHeader>
    </Card>
  );
};
