
import React from 'react';
import { <PERSON><PERSON>, Di<PERSON><PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Eye } from 'lucide-react';

interface ExtendedUser {
  id: string;
  name: string;
  email: string;
  role: string;
  is_active?: boolean;
  total_visits?: number;
  total_sales?: number;
  stats_reset_count?: number;
  last_stats_reset?: string;
}

interface UserViewDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  user: ExtendedUser | null;
}

export const UserViewDialog: React.FC<UserViewDialogProps> = ({
  isOpen,
  onOpenChange,
  user,
}) => {
  if (!user) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5 text-blue-600" />
            Benutzer Details: {user.name}
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium text-gray-600">Name</Label>
              <p className="text-sm">{user.name}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-gray-600">E-Mail</Label>
              <p className="text-sm">{user.email}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-gray-600">Rolle</Label>
              <Badge variant="outline" className="capitalize">
                {user.role}
              </Badge>
            </div>
            <div>
              <Label className="text-sm font-medium text-gray-600">Status</Label>
              <Badge variant={user.is_active !== false ? 'default' : 'secondary'}>
                {user.is_active !== false ? 'Aktiv' : 'Inaktiv'}
              </Badge>
            </div>
          </div>
          
          <div className="border-t pt-4">
            <h4 className="font-medium text-gray-800 mb-3">Statistiken</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium text-gray-600">Gesamte Besuche</Label>
                <p className="text-lg font-semibold text-blue-600">{user.total_visits || 0}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-600">Gesamte Verkäufe</Label>
                <p className="text-lg font-semibold text-green-600">{user.total_sales || 0}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-600">Resets</Label>
                <p className="text-sm">{user.stats_reset_count || 0}x</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-600">Letzter Reset</Label>
                <p className="text-sm">
                  {user.last_stats_reset 
                    ? new Date(user.last_stats_reset).toLocaleDateString('de-DE')
                    : 'Nie'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
