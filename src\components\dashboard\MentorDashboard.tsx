
import React, { useState } from 'react';
import { Card, Card<PERSON><PERSON>nt, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { useData } from '@/context/data';
import { useAuth } from '@/context/auth';
import { useIsMobile } from '@/hooks/use-mobile';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Users, TrendingUp, Calendar, MapPin, Eye, CheckCircle } from 'lucide-react';

interface MentorDashboardProps {
  mentorId: string;
}

const MentorDashboard: React.FC<MentorDashboardProps> = ({ mentorId }) => {
  const { visits, doors, products, houses, addresses, getHouseById } = useData();
  const { users } = useAuth();
  const isMobile = useIsMobile();
  const [activeTab, setActiveTab] = useState<string>("overview");
  
  // Find all assigned consultants
  const assignedBeraters = users.filter(user => {
    return user.mentorId === mentorId;
  });
  
  // Calculate statistics per consultant
  const beraterStats = assignedBeraters.map(berater => {
    const beraterVisits = visits.filter(v => v.userId === berater.id);
    const beraterProducts = products.filter(p => p.userId === berater.id);
    const today = new Date().toISOString().split('T')[0];
    const todayVisits = beraterVisits.filter(v => v.timestamp.startsWith(today));
    
    return {
      id: berater.id,
      name: berater.name,
      totalVisits: beraterVisits.length,
      todayVisits: todayVisits.length,
      salesCount: beraterProducts.length
    };
  });

  // Overall statistics
  const totalVisits = beraterStats.reduce((sum, berater) => sum + berater.totalVisits, 0);
  const totalSales = beraterStats.reduce((sum, berater) => sum + berater.salesCount, 0);
  const todayTotal = beraterStats.reduce((sum, berater) => sum + berater.todayVisits, 0);
  
  // Get completed addresses (doors with status "Angetroffen → Sale")
  const completedDoors = doors.filter(door => door.status === "Angetroffen → Sale");
  const completedVisits = completedDoors.map(door => {
    const visit = visits.find(v => v.id === door.visitId);
    if (!visit) return null;
    
    // Find consultant name
    const berater = users.find(u => u.id === visit.userId);
    
    // Find house and address
    const house = getHouseById(visit.houseId);
    const address = house ? addresses.find(a => a.id === house.addressId) : null;
    
    return {
      doorId: door.id,
      visitId: door.visitId,
      visitDate: visit.timestamp ? new Date(visit.timestamp).toLocaleDateString('de-DE') : 'Unbekannt',
      address: address 
        ? `${address.street} ${house?.houseNumber || ''}, ${address.zipCode} ${address.city}` 
        : 'Unbekannte Adresse',
      beraterName: berater?.name || 'Unbekannt'
    };
  }).filter(Boolean);

  const statsCards = [
    {
      title: "Team Besuche",
      value: totalVisits,
      icon: Users,
      color: "from-blue-500 to-blue-600",
      textColor: "text-blue-600"
    },
    {
      title: "Team Verkäufe", 
      value: totalSales,
      icon: TrendingUp,
      color: "from-green-500 to-green-600",
      textColor: "text-green-600"
    },
    {
      title: "Heute",
      value: todayTotal,
      icon: Calendar,
      color: "from-purple-500 to-purple-600", 
      textColor: "text-purple-600"
    }
  ];
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 p-4 md:p-6">
      <div className="max-w-7xl mx-auto space-y-6 md:space-y-8">
        {/* Header Section */}
        <div className="text-center md:text-left space-y-2 md:space-y-4 animate-fade-in">
          <h1 className={`font-bold text-gray-800 ${isMobile ? 'text-2xl' : 'text-4xl'}`}>
            Mentor Dashboard
          </h1>
          <p className={`text-gray-600 ${isMobile ? 'text-sm' : 'text-lg'}`}>
            Übersicht über {assignedBeraters.length} zugewiesene Berater
          </p>
        </div>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className={`grid w-full grid-cols-2 ${isMobile ? 'h-12' : 'h-14'} bg-white/80 backdrop-blur-sm border border-white/20 shadow-lg rounded-2xl`}>
            <TabsTrigger 
              value="overview" 
              className={`${isMobile ? 'text-sm py-2' : 'text-base py-3'} data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-600 data-[state=active]:text-white rounded-xl transition-all duration-300`}
            >
              <Eye className="h-4 w-4 mr-2" />
              Team Übersicht
            </TabsTrigger>
            <TabsTrigger 
              value="completed" 
              className={`${isMobile ? 'text-sm py-2' : 'text-base py-3'} data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-emerald-600 data-[state=active]:text-white rounded-xl transition-all duration-300`}
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Abgeschlossen
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-6 animate-fade-in">
            {/* Statistics Cards */}
            <div className={`grid gap-4 md:gap-6 ${isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-3'}`}>
              {statsCards.map((stat, index) => (
                <Card key={stat.title} className={`glass-card hover-lift ${isMobile ? 'p-4' : 'p-6'} rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-scale-in`} style={{ animationDelay: `${index * 0.1}s` }}>
                  <CardContent className="p-0">
                    <div className="flex items-center justify-between">
                      <div className="space-y-2">
                        <p className={`font-medium text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                          {stat.title}
                        </p>
                        <p className={`font-bold ${stat.textColor} ${isMobile ? 'text-2xl' : 'text-4xl'}`}>
                          {stat.value}
                        </p>
                      </div>
                      <div className={`rounded-2xl bg-gradient-to-br ${stat.color} p-3 shadow-lg`}>
                        <stat.icon className={`${isMobile ? 'h-6 w-6' : 'h-8 w-8'} text-white`} />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
            
            {/* Consultants Overview Table */}
            <Card className="glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-slide-in">
              <CardHeader className={`${isMobile ? 'p-4 pb-2' : 'p-6 pb-4'}`}>
                <CardTitle className={`flex items-center gap-3 ${isMobile ? 'text-lg' : 'text-2xl'} font-bold text-gray-800`}>
                  <Users className={`${isMobile ? 'h-5 w-5' : 'h-6 w-6'} text-blue-600`} />
                  Berater-Übersicht
                </CardTitle>
                <CardDescription className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-600`}>
                  {format(new Date(), "'Stand:' d. MMMM yyyy", { locale: de })}
                </CardDescription>
              </CardHeader>
              <CardContent className={`${isMobile ? 'p-4 pt-0' : 'p-6 pt-0'}`}>
                <div className="overflow-x-auto scrollbar-none">
                  <Table>
                    <TableHeader>
                      <TableRow className="border-gray-200">
                        <TableHead className={`font-semibold text-gray-700 ${isMobile ? 'text-xs' : 'text-sm'}`}>Name</TableHead>
                        <TableHead className={`font-semibold text-gray-700 ${isMobile ? 'text-xs' : 'text-sm'}`}>Besuche gesamt</TableHead>
                        <TableHead className={`font-semibold text-gray-700 ${isMobile ? 'text-xs' : 'text-sm'}`}>Heute</TableHead>
                        <TableHead className={`font-semibold text-gray-700 ${isMobile ? 'text-xs' : 'text-sm'}`}>Verkäufe</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {beraterStats.map((berater, index) => (
                        <TableRow key={berater.id} className="hover:bg-blue-50/50 transition-colors border-gray-100" style={{ animationDelay: `${index * 0.05}s` }}>
                          <TableCell className={`font-medium text-gray-800 ${isMobile ? 'text-sm' : 'text-base'}`}>
                            {berater.name}
                          </TableCell>
                          <TableCell className={`${isMobile ? 'text-sm' : 'text-base'}`}>
                            <Badge variant="secondary" className="bg-blue-100 text-blue-800 hover:bg-blue-200">
                              {berater.totalVisits}
                            </Badge>
                          </TableCell>
                          <TableCell className={`${isMobile ? 'text-sm' : 'text-base'}`}>
                            <Badge variant="secondary" className="bg-purple-100 text-purple-800 hover:bg-purple-200">
                              {berater.todayVisits}
                            </Badge>
                          </TableCell>
                          <TableCell className={`${isMobile ? 'text-sm' : 'text-base'}`}>
                            <Badge variant="secondary" className="bg-green-100 text-green-800 hover:bg-green-200">
                              {berater.salesCount}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                      {beraterStats.length === 0 && (
                        <TableRow>
                          <TableCell colSpan={4} className={`text-center py-8 text-gray-500 ${isMobile ? 'text-sm' : 'text-base'}`}>
                            Keine Berater zugewiesen
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="completed" className="space-y-6 animate-fade-in">
            <Card className="glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-slide-in">
              <CardHeader className={`${isMobile ? 'p-4 pb-2' : 'p-6 pb-4'}`}>
                <CardTitle className={`flex items-center gap-3 ${isMobile ? 'text-lg' : 'text-2xl'} font-bold text-gray-800`}>
                  <MapPin className={`${isMobile ? 'h-5 w-5' : 'h-6 w-6'} text-green-600`} />
                  Abgeschlossene Adressen
                </CardTitle>
                <CardDescription className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-600`}>
                  Erfolgreich bearbeitete Adressen ({completedVisits.length} Einträge)
                </CardDescription>
              </CardHeader>
              <CardContent className={`${isMobile ? 'p-4 pt-0' : 'p-6 pt-0'}`}>
                <div className="overflow-x-auto scrollbar-none">
                  <Table>
                    <TableHeader>
                      <TableRow className="border-gray-200">
                        <TableHead className={`font-semibold text-gray-700 ${isMobile ? 'text-xs' : 'text-sm'}`}>Datum</TableHead>
                        <TableHead className={`font-semibold text-gray-700 ${isMobile ? 'text-xs' : 'text-sm'}`}>Adresse</TableHead>
                        <TableHead className={`font-semibold text-gray-700 ${isMobile ? 'text-xs' : 'text-sm'}`}>Berater</TableHead>
                        <TableHead className={`font-semibold text-gray-700 ${isMobile ? 'text-xs' : 'text-sm'}`}>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {completedVisits.map((item: any, index) => (
                        <TableRow key={item.doorId} className="hover:bg-green-50/50 transition-colors border-gray-100" style={{ animationDelay: `${index * 0.05}s` }}>
                          <TableCell className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-600`}>
                            {item.visitDate}
                          </TableCell>
                          <TableCell className={`font-medium text-gray-800 ${isMobile ? 'text-xs' : 'text-sm'} max-w-0 truncate`}>
                            {item.address}
                          </TableCell>
                          <TableCell className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-700`}>
                            {item.beraterName}
                          </TableCell>
                          <TableCell>
                            <Badge className="bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-md">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Abgeschlossen
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                      {completedVisits.length === 0 && (
                        <TableRow>
                          <TableCell colSpan={4} className={`text-center py-8 text-gray-500 ${isMobile ? 'text-sm' : 'text-base'}`}>
                            Keine abgeschlossenen Adressen vorhanden
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default MentorDashboard;
