
import React from 'react';
import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardFooter } from '@/design-system/components/Card';
import { Address, House, Door, ProductCategory } from '@/types';
import { AddressInfo } from './AddressInfo';
import { InfoBanners } from './InfoBanners';
import { DoorSelection } from './DoorSelection';
import { ProductTabs } from './ProductTabs';
import { SaveButton } from './SaveButton';

interface ProductSelectionContentProps {
  address: Address;
  house: House;
  salesDoors: Door[];
  selectedDoors: string[];
  products: {category: ProductCategory; type: string; quantity: number}[];
  isSubmitting: boolean;
  onDoorToggle: (doorId: string) => void;
  onAddProduct: (category: ProductCategory) => void;
  onRemoveProduct: (index: number) => void;
  onUpdateProductType: (index: number, type: string) => void;
  onUpdateProductQuantity: (index: number, quantity: number) => void;
  onSubmit: () => void;
  onBack: () => void;
}

export const ProductSelectionContent: React.FC<ProductSelectionContentProps> = ({
  address,
  house,
  salesDoors,
  selectedDoors,
  products,
  isSubmitting,
  onDoorToggle,
  onAddProduct,
  onRemoveProduct,
  onUpdateProductType,
  onUpdateProductQuantity,
  onSubmit,
  onBack
}) => {
  const isMultiDoor = salesDoors.length > 1;
  const canSave = selectedDoors.length > 0 && !isSubmitting;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-4xl mx-auto">
        <Card variant="glass" padding="none" className="overflow-hidden">
          <CardHeader>
            <AddressInfo address={address} house={house} />
          </CardHeader>

          <CardContent>
            <InfoBanners
              isMultiDoor={isMultiDoor}
              salesDoors={salesDoors}
              selectedDoors={selectedDoors}
              canSave={canSave}
              isSubmitting={isSubmitting}
            />

            <DoorSelection
              salesDoors={salesDoors}
              selectedDoors={selectedDoors}
              onDoorToggle={onDoorToggle}
            />

            <ProductTabs
              products={products}
              onAddProduct={onAddProduct}
              onRemoveProduct={onRemoveProduct}
              onUpdateProductType={onUpdateProductType}
              onUpdateProductQuantity={onUpdateProductQuantity}
            />
          </CardContent>

          <CardFooter>
            <SaveButton
              canSave={canSave}
              isSubmitting={isSubmitting}
              selectedDoorsCount={selectedDoors.length}
              productsCount={products.length}
              onSubmit={onSubmit}
              onBack={onBack}
            />
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};
