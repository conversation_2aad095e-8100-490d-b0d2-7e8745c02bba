
import React from 'react';
import { useParams } from 'react-router-dom';
import MainLayout from '@/components/layout/MainLayout';
import EFHVisitTracker from '@/components/visit/EFHVisitTracker';

const VisitStatusPage: React.FC = () => {
  const { visitId } = useParams<{ visitId: string }>();
  
  if (!visitId) {
    return (
      <MainLayout title="Besuchsstatus">
        <div className="min-h-full flex items-center justify-center px-4 py-6 bg-gradient-to-br from-red-50 via-white to-red-50">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-800 mb-4">Keine Besuchs-ID gefunden</h2>
            <p className="text-gray-600">Bitte gehen Sie zurück zur Startseite und versuchen Sie es erneut.</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout title="Besuchsstatus">
      <div className="min-h-full flex items-start justify-center px-4 py-6 bg-gradient-to-br from-red-50 via-white to-red-50">
        <div className="w-full max-w-2xl">
          <EFHVisitTracker visitId={visitId} />
        </div>
      </div>
    </MainLayout>
  );
};

export default VisitStatusPage;
