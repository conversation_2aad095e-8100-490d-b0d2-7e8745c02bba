
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useSettings } from '@/context/settings/SettingsProvider';
import { toast } from 'sonner';
import { Database, Download, Upload, Trash2, Shield, AlertTriangle, RotateCcw } from 'lucide-react';
import { ConfirmationDialog } from './ConfirmationDialog';

export const SystemSettings: React.FC = () => {
  const { resetSettings } = useSettings();
  const [showResetDialog, setShowResetDialog] = useState(false);
  const [showDataResetDialog, setShowDataResetDialog] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleExportData = async () => {
    setIsProcessing(true);
    try {
      // Simulate export process
      await new Promise(resolve => setTimeout(resolve, 1500));
      toast.success('Datenexport erfolgreich abgeschlossen');
      
      // Create and download a sample export file
      const exportData = {
        timestamp: new Date().toISOString(),
        settings: JSON.parse(localStorage.getItem('app-settings') || '{}'),
        version: '1.0.0'
      };
      
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `app-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      toast.error('Fehler beim Datenexport');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleImportData = async () => {
    setIsProcessing(true);
    try {
      // Create file input for import
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.json';
      
      input.onchange = async (e) => {
        const file = (e.target as HTMLInputElement).files?.[0];
        if (file) {
          const text = await file.text();
          try {
            const data = JSON.parse(text);
            if (data.settings) {
              localStorage.setItem('app-settings', JSON.stringify(data.settings));
              toast.success('Datenimport erfolgreich - Seite wird neu geladen');
              setTimeout(() => window.location.reload(), 1500);
            } else {
              toast.error('Ungültiges Datenformat');
            }
          } catch {
            toast.error('Fehler beim Lesen der Datei');
          }
        }
        setIsProcessing(false);
      };
      
      input.click();
    } catch (error) {
      toast.error('Fehler beim Datenimport');
      setIsProcessing(false);
    }
  };

  const handleResetData = () => {
    resetSettings();
    localStorage.clear();
    toast.success('Alle Daten wurden zurückgesetzt');
    setTimeout(() => window.location.reload(), 1500);
  };

  const handleBackup = async () => {
    setIsProcessing(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      toast.success('System-Backup erfolgreich erstellt');
    } catch (error) {
      toast.error('Fehler beim Erstellen des Backups');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleResetSettings = () => {
    resetSettings();
    toast.success('Einstellungen wurden auf Standardwerte zurückgesetzt');
  };

  return (
    <div className="space-y-6">
      <Alert>
        <Shield className="h-4 w-4" />
        <AlertDescription>
          <strong>Administrator-Bereich:</strong> Diese Einstellungen betreffen das gesamte System. 
          Änderungen hier können Auswirkungen auf alle Benutzer haben.
        </AlertDescription>
      </Alert>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Datenbank-Management
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button 
              variant="outline" 
              onClick={handleBackup}
              disabled={isProcessing}
              className="flex items-center gap-2"
            >
              <Database className="h-4 w-4" />
              {isProcessing ? 'Wird erstellt...' : 'Backup erstellen'}
            </Button>
            <Button 
              variant="outline" 
              onClick={handleExportData}
              disabled={isProcessing}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              {isProcessing ? 'Wird exportiert...' : 'Daten exportieren'}
            </Button>
            <Button 
              variant="outline" 
              onClick={handleImportData}
              disabled={isProcessing}
              className="flex items-center gap-2"
            >
              <Upload className="h-4 w-4" />
              Daten importieren
            </Button>
          </div>
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Backups werden täglich um 2:00 Uhr automatisch erstellt und 30 Tage aufbewahrt.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RotateCcw className="h-5 w-5" />
            Einstellungen zurücksetzen
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert className="border-orange-200 bg-orange-50">
            <AlertTriangle className="h-4 w-4 text-orange-600" />
            <AlertDescription className="text-orange-800">
              Diese Aktion setzt alle App-Einstellungen auf die Standardwerte zurück.
            </AlertDescription>
          </Alert>
          <Button 
            variant="outline" 
            onClick={() => setShowResetDialog(true)}
            className="flex items-center gap-2"
          >
            <RotateCcw className="h-4 w-4" />
            Einstellungen zurücksetzen
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <Trash2 className="h-5 w-5" />
            Gefährliche Aktionen
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              <strong>Warnung:</strong> Diese Aktion löscht alle Daten unwiderruflich. 
              Stellen Sie sicher, dass Sie ein aktuelles Backup haben.
            </AlertDescription>
          </Alert>
          <Button 
            variant="destructive" 
            onClick={() => setShowDataResetDialog(true)}
            className="mt-4 flex items-center gap-2"
          >
            <Trash2 className="h-4 w-4" />
            Alle Daten zurücksetzen
          </Button>
        </CardContent>
      </Card>

      <ConfirmationDialog
        open={showResetDialog}
        onOpenChange={setShowResetDialog}
        title="Einstellungen zurücksetzen"
        description="Sind Sie sicher, dass Sie alle Einstellungen auf die Standardwerte zurücksetzen möchten?"
        confirmText="Zurücksetzen"
        onConfirm={handleResetSettings}
      />

      <ConfirmationDialog
        open={showDataResetDialog}
        onOpenChange={setShowDataResetDialog}
        title="Alle Daten löschen"
        description="Diese Aktion löscht unwiderruflich alle Daten. Sind Sie absolut sicher?"
        confirmText="Alle Daten löschen"
        onConfirm={handleResetData}
        variant="destructive"
      />
    </div>
  );
};
