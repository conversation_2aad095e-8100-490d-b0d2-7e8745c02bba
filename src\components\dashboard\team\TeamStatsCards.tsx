
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { TeamData } from './types';
import { useIsMobile } from '@/hooks/use-mobile';
import { Users, TrendingUp, Calendar, Building } from 'lucide-react';

interface TeamStatsCardsProps {
  teamStats: TeamData[];
}

const TeamStatsCards: React.FC<TeamStatsCardsProps> = ({ teamStats }) => {
  const isMobile = useIsMobile();
  
  const statsCards = [
    {
      title: "Teams",
      value: teamStats.length,
      icon: Building,
      color: "from-blue-500 to-blue-600",
      textColor: "text-blue-600"
    },
    {
      title: "Gesamt Besuche", 
      value: teamStats.reduce((sum, team) => sum + team.visitCount, 0),
      icon: Users,
      color: "from-purple-500 to-purple-600",
      textColor: "text-purple-600"
    },
    {
      title: "Gesamt Verkäufe",
      value: teamStats.reduce((sum, team) => sum + team.salesCount, 0),
      icon: TrendingUp,
      color: "from-green-500 to-green-600", 
      textColor: "text-green-600"
    },
    {
      title: "Heute",
      value: teamStats.reduce((sum, team) => sum + team.todayVisits, 0),
      icon: Calendar,
      color: "from-orange-500 to-orange-600",
      textColor: "text-orange-600"
    }
  ];
  
  return (
    <div className={`grid gap-4 md:gap-6 ${isMobile ? 'grid-cols-2' : 'grid-cols-2 md:grid-cols-4'}`}>
      {statsCards.map((stat, index) => (
        <Card key={stat.title} className={`glass-card hover-lift ${isMobile ? 'p-3' : 'p-4'} rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-scale-in`} style={{ animationDelay: `${index * 0.1}s` }}>
          <CardContent className="p-0">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className={`font-medium text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                  {stat.title}
                </p>
                <p className={`font-bold ${stat.textColor} ${isMobile ? 'text-xl' : 'text-3xl'}`}>
                  {stat.value}
                </p>
              </div>
              <div className={`rounded-2xl bg-gradient-to-br ${stat.color} p-2 shadow-lg`}>
                <stat.icon className={`${isMobile ? 'h-5 w-5' : 'h-6 w-6'} text-white`} />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default TeamStatsCards;
