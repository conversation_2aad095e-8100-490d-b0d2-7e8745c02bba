
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/auth';
import { useData } from '@/context/data';
import { TeamData, CompletedVisitData } from './types';

export const useTeamData = (teamId: string) => {
  const { visits, doors, products, houses, addresses, getHouseById } = useData();
  const { users } = useAuth();
  const [teams, setTeams] = useState<Array<{id: string, name: string}>>([]);
  const [loading, setLoading] = useState(true);
  
  // Fetch teams from Supabase
  useEffect(() => {
    const fetchTeams = async () => {
      try {
        const { data, error } = await supabase.from('teams').select('id, name');
        
        if (error) {
          console.error('Error fetching teams:', error);
          return;
        }
        
        if (data) {
          setTeams(data);
        }
      } catch (err) {
        console.error('Error fetching teams:', err);
      } finally {
        setLoading(false);
      }
    };
    
    fetchTeams();
  }, []);
  
  // Find all team members
  const getTeamMembers = (teamId: string) => {
    return users.filter(user => user.teamId === teamId);
  };
  
  // Calculate team statistics
  const calculateTeamStats = (): TeamData[] => {
    return teams.map(team => {
      const teamMembers = getTeamMembers(team.id);
      
      // Team visits and sales
      const teamVisits = visits.filter(v => {
        const visitUser = teamMembers.find(u => u.id === v.userId);
        return visitUser !== undefined;
      });
      
      const teamProducts = products.filter(p => {
        const productUser = teamMembers.find(u => u.id === p.userId);
        return productUser !== undefined;
      });
      
      // Today's visits
      const today = new Date().toISOString().split('T')[0];
      const todayVisits = teamVisits.filter(v => v.timestamp.startsWith(today));
      
      return {
        id: team.id,
        name: team.name,
        memberCount: teamMembers.length,
        visitCount: teamVisits.length,
        salesCount: teamProducts.length,
        todayVisits: todayVisits.length
      };
    });
  };
  
  // Get completed visits for a specific team
  const getCompletedVisits = (teamId: string): CompletedVisitData[] => {
    const teamMembers = getTeamMembers(teamId);
    
    // Get doors with status "Angetroffen → Sale" for this team
    const teamDoors = doors.filter(door => {
      const visit = visits.find(v => v.id === door.visitId);
      if (!visit) return false;
      
      const visitUser = teamMembers.find(u => u.id === visit.userId);
      return visitUser !== undefined && door.status === "Angetroffen → Sale";
    });
    
    // Map doors to visit data
    return teamDoors.map(door => {
      const visit = visits.find(v => v.id === door.visitId);
      if (!visit) return null;
      
      // Find berater name
      const berater = users.find(u => u.id === visit.userId);
      
      // Find mentor name
      const mentor = berater?.mentorId 
        ? users.find(u => u.id === berater.mentorId)?.name || 'Nicht zugewiesen'
        : 'Nicht zugewiesen';
      
      // Find house and address
      const house = getHouseById(visit.houseId);
      const address = house ? addresses.find(a => a.id === house.addressId) : null;
      
      return {
        visitDate: visit.timestamp ? new Date(visit.timestamp).toLocaleDateString('de-DE') : 'Unbekannt',
        address: address 
          ? `${address.street} ${house?.houseNumber || ''}, ${address.zipCode} ${address.city}` 
          : 'Unbekannte Adresse',
        beraterName: berater?.name || 'Unbekannt',
        mentorName: mentor
      };
    }).filter(Boolean) as CompletedVisitData[];
  };

  // Calculate team statistics
  const teamStats = calculateTeamStats();
  
  // Get selected team data
  const selectedTeam = teams.find(t => t.id === teamId);
  const selectedTeamStats = teamStats.find(t => t.id === teamId);
  const completedVisits = selectedTeam ? getCompletedVisits(selectedTeam.id) : [];

  return {
    loading,
    teams,
    teamStats,
    selectedTeam,
    selectedTeamStats,
    completedVisits
  };
};
