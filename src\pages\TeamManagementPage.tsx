
import React, { useState, useEffect } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { useAuth } from '@/context/auth';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { useIsMobile } from '@/hooks/use-mobile';
import { toast } from 'sonner';
import { Users, Plus, Edit, Trash2, Building } from 'lucide-react';
import { createTeam, getTeams, updateTeam, deleteTeam, getAreas } from '@/integrations/supabase/functions';

interface Team {
  id: string;
  name: string;
  description?: string;
  area_id?: string;
  team_leader_id?: string;
  is_active?: boolean;
  created_at: string;
  updated_at?: string;
  areas?: {
    id: string;
    name: string;
  };
}

interface Area {
  id: string;
  name: string;
}

const TeamManagementPage: React.FC = () => {
  const { user, users } = useAuth();
  const isMobile = useIsMobile();
  const [teams, setTeams] = useState<Team[]>([]);
  const [areas, setAreas] = useState<Area[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTeam, setSelectedTeam] = useState<Team | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    area_id: '',
    team_leader_id: '',
    is_active: true
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const [teamsData, areasData] = await Promise.all([
        getTeams(),
        getAreas()
      ]);
      setTeams(teamsData || []);
      setAreas(areasData || []);
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error('Fehler beim Laden der Daten');
    } finally {
      setLoading(false);
    }
  };

  const teamLeaders = users.filter(u => u.role === 'teamleiter');

  const handleCreateTeam = async () => {
    if (!formData.name.trim()) {
      toast.error('Name ist erforderlich');
      return;
    }

    try {
      await createTeam({
        name: formData.name,
        description: formData.description || undefined,
        area_id: formData.area_id || undefined,
        team_leader_id: formData.team_leader_id || undefined
      });

      toast.success(`Team "${formData.name}" wurde erstellt`);
      setFormData({ name: '', description: '', area_id: '', team_leader_id: '', is_active: true });
      setIsCreateDialogOpen(false);
      loadData();
    } catch (error) {
      console.error('Error creating team:', error);
      toast.error('Fehler beim Erstellen des Teams');
    }
  };

  const handleEditTeam = async () => {
    if (!selectedTeam || !formData.name.trim()) {
      toast.error('Name ist erforderlich');
      return;
    }

    try {
      await updateTeam(selectedTeam.id, {
        name: formData.name,
        description: formData.description || undefined,
        area_id: formData.area_id || undefined,
        team_leader_id: formData.team_leader_id || undefined,
        is_active: formData.is_active
      });

      toast.success(`Team "${formData.name}" wurde aktualisiert`);
      setFormData({ name: '', description: '', area_id: '', team_leader_id: '', is_active: true });
      setSelectedTeam(null);
      setIsEditDialogOpen(false);
      loadData();
    } catch (error) {
      console.error('Error updating team:', error);
      toast.error('Fehler beim Aktualisieren des Teams');
    }
  };

  const handleDeleteTeam = async (team: Team) => {
    if (!confirm(`Möchten Sie das Team "${team.name}" wirklich löschen?`)) {
      return;
    }

    try {
      await deleteTeam(team.id);
      toast.success(`Team "${team.name}" wurde gelöscht`);
      loadData();
    } catch (error) {
      console.error('Error deleting team:', error);
      toast.error('Fehler beim Löschen des Teams');
    }
  };

  const openEditDialog = (team: Team) => {
    setSelectedTeam(team);
    setFormData({
      name: team.name,
      description: team.description || '',
      area_id: team.area_id || '',
      team_leader_id: team.team_leader_id || '',
      is_active: team.is_active ?? true
    });
    setIsEditDialogOpen(true);
  };

  if (!user || user.role !== 'admin') {
    return (
      <MainLayout title="Teams verwalten">
        <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 flex items-center justify-center p-4">
          <div className="text-center">
            <p className="text-gray-600">Sie haben keine Berechtigung, diese Seite anzuzeigen.</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout title="Teams verwalten">
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 p-4 md:p-6">
        <div className="max-w-7xl mx-auto space-y-6 md:space-y-8">
          {/* Header */}
          <div className="text-center md:text-left space-y-2 md:space-y-4 animate-fade-in">
            <h1 className={`font-bold text-gray-800 ${isMobile ? 'text-2xl' : 'text-4xl'}`}>
              Teams verwalten
            </h1>
            <p className={`text-gray-600 ${isMobile ? 'text-sm' : 'text-lg'}`}>
              Erstellen und verwalten Sie Teams und deren Zuordnungen
            </p>
          </div>

          {/* Create Button */}
          <div className="flex justify-end">
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300">
                  <Plus className="h-4 w-4 mr-2" />
                  Neues Team
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5 text-blue-600" />
                    Neues Team erstellen
                  </DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="name">Name*</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      placeholder="z.B. Team Stuttgart"
                    />
                  </div>
                  <div>
                    <Label htmlFor="description">Beschreibung</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      placeholder="Beschreibung des Teams..."
                      rows={3}
                    />
                  </div>
                  <div>
                    <Label htmlFor="area">Gebiet</Label>
                    <Select
                      value={formData.area_id}
                      onValueChange={(value) => setFormData({ ...formData, area_id: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Gebiet auswählen" />
                      </SelectTrigger>
                      <SelectContent>
                        {areas.map((area) => (
                          <SelectItem key={area.id} value={area.id}>
                            {area.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="team_leader">Teamleiter</Label>
                    <Select
                      value={formData.team_leader_id}
                      onValueChange={(value) => setFormData({ ...formData, team_leader_id: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Teamleiter auswählen" />
                      </SelectTrigger>
                      <SelectContent>
                        {teamLeaders.map((leader) => (
                          <SelectItem key={leader.id} value={leader.id}>
                            {leader.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <Button onClick={handleCreateTeam} className="w-full">
                    Team erstellen
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          {/* Teams Table */}
          <Card className="glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-slide-in">
            <CardHeader className={`${isMobile ? 'p-4 pb-2' : 'p-6 pb-4'}`}>
              <CardTitle className={`flex items-center gap-3 ${isMobile ? 'text-lg' : 'text-xl'} font-bold text-gray-800`}>
                <Building className={`${isMobile ? 'h-5 w-5' : 'h-6 w-6'} text-blue-600`} />
                Alle Teams ({teams.length})
              </CardTitle>
            </CardHeader>
            <CardContent className={`${isMobile ? 'p-4 pt-0' : 'p-6 pt-0'}`}>
              {loading ? (
                <div className="text-center py-8">
                  <p className="text-gray-500">Lade Teams...</p>
                </div>
              ) : teams.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">Noch keine Teams erstellt</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-gray-700`}>Name</TableHead>
                        <TableHead className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-gray-700`}>Gebiet</TableHead>
                        <TableHead className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-gray-700`}>Teamleiter</TableHead>
                        <TableHead className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-gray-700`}>Status</TableHead>
                        <TableHead className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-gray-700`}>Aktionen</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {teams.map((team) => {
                        const teamLeader = teamLeaders.find(tl => tl.id === team.team_leader_id);
                        return (
                          <TableRow key={team.id} className="hover:bg-blue-50/50 transition-colors">
                            <TableCell className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-gray-800`}>
                              {team.name}
                            </TableCell>
                            <TableCell className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-700`}>
                              {team.areas?.name || '-'}
                            </TableCell>
                            <TableCell className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-700`}>
                              {teamLeader?.name || '-'}
                            </TableCell>
                            <TableCell>
                              <Badge variant={team.is_active ? 'default' : 'secondary'}>
                                {team.is_active ? 'Aktiv' : 'Inaktiv'}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <div className="flex gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => openEditDialog(team)}
                                  className="text-blue-600 border-blue-200 hover:bg-blue-50"
                                >
                                  <Edit className="h-3 w-3" />
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleDeleteTeam(team)}
                                  className="text-red-600 border-red-200 hover:bg-red-50"
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Edit Dialog */}
          <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <Edit className="h-5 w-5 text-blue-600" />
                  Team bearbeiten
                </DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="edit-name">Name*</Label>
                  <Input
                    id="edit-name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="z.B. Team Stuttgart"
                  />
                </div>
                <div>
                  <Label htmlFor="edit-description">Beschreibung</Label>
                  <Textarea
                    id="edit-description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    placeholder="Beschreibung des Teams..."
                    rows={3}
                  />
                </div>
                <div>
                  <Label htmlFor="edit-area">Gebiet</Label>
                  <Select
                    value={formData.area_id}
                    onValueChange={(value) => setFormData({ ...formData, area_id: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Gebiet auswählen" />
                    </SelectTrigger>
                    <SelectContent>
                      {areas.map((area) => (
                        <SelectItem key={area.id} value={area.id}>
                          {area.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="edit-team-leader">Teamleiter</Label>
                  <Select
                    value={formData.team_leader_id}
                    onValueChange={(value) => setFormData({ ...formData, team_leader_id: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Teamleiter auswählen" />
                    </SelectTrigger>
                    <SelectContent>
                      {teamLeaders.map((leader) => (
                        <SelectItem key={leader.id} value={leader.id}>
                          {leader.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="edit-active"
                    checked={formData.is_active}
                    onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
                  />
                  <Label htmlFor="edit-active">Team ist aktiv</Label>
                </div>
                <Button onClick={handleEditTeam} className="w-full">
                  Änderungen speichern
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </MainLayout>
  );
};

export default TeamManagementPage;
