
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { AlertCircle } from 'lucide-react';
import { toast } from 'sonner';

interface MapTokenInputProps {
  onTokenSubmit: (token: string) => void;
  onAutoLoad: (token: string) => void;
}

export const MapTokenInput: React.FC<MapTokenInputProps> = ({ onTokenSubmit, onAutoLoad }) => {
  const [token, setToken] = useState('');
  const [rememberToken, setRememberToken] = useState(false);

  useEffect(() => {
    // Check if token is stored and auto-load
    const storedToken = localStorage.getItem('mapbox-token');
    if (storedToken) {
      onAutoLoad(storedToken);
    }
  }, [onAutoLoad]);

  const handleSubmit = () => {
    if (!token.trim()) {
      toast.error('Bitte geben Sie einen gültigen Mapbox Token ein');
      return;
    }

    if (rememberToken) {
      localStorage.setItem('mapbox-token', token);
      toast.success('Token wurde gespeichert');
    }

    onTokenSubmit(token);
  };

  const clearStoredToken = () => {
    localStorage.removeItem('mapbox-token');
    toast.success('Gespeicherter Token wurde gelöscht');
  };

  const storedToken = localStorage.getItem('mapbox-token');

  return (
    <div className="flex flex-col items-center justify-center h-full p-6 space-y-4">
      <AlertCircle className="h-12 w-12 text-orange-500" />
      <h3 className="text-lg font-semibold">Mapbox Token erforderlich</h3>
      <p className="text-sm text-muted-foreground text-center max-w-md">
        Um die Karte anzuzeigen, benötigen Sie einen Mapbox API Token. 
        Diesen können Sie kostenlos auf mapbox.com erstellen.
      </p>
      
      {storedToken && (
        <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
          <p className="text-sm text-green-700">
            Token ist bereits gespeichert und wird automatisch geladen.
          </p>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={clearStoredToken}
            className="mt-2"
          >
            Gespeicherten Token löschen
          </Button>
        </div>
      )}

      <div className="flex gap-2 w-full max-w-md">
        <Input
          placeholder="Mapbox Token eingeben..."
          value={token}
          onChange={(e) => setToken(e.target.value)}
          type="password"
        />
        <Button onClick={handleSubmit}>
          Laden
        </Button>
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox
          id="remember-token"
          checked={rememberToken}
          onCheckedChange={(checked) => setRememberToken(checked as boolean)}
        />
        <label htmlFor="remember-token" className="text-sm">
          Token für zukünftige Besuche speichern
        </label>
      </div>

      <a 
        href="https://account.mapbox.com/access-tokens/" 
        target="_blank" 
        rel="noopener noreferrer"
        className="text-sm text-blue-600 hover:underline"
      >
        Mapbox Token erstellen →
      </a>
    </div>
  );
};
