
import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/context/auth';
import { useIsMobile } from '@/hooks/use-mobile';
import { toast } from 'sonner';
import { User, Mail, Shield, Users, UserCheck, Edit3 } from 'lucide-react';

const ProfileView: React.FC = () => {
  const {
    user,
    updateUser
  } = useAuth();
  const isMobile = useIsMobile();
  const [name, setName] = useState(user?.name || '');
  const [email, setEmail] = useState(user?.email || '');
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  
  if (!user) {
    return (
      <div className={`${isMobile ? 'px-4 py-6' : 'p-6'} bg-gradient-to-br from-gray-50 to-gray-100 min-h-full animate-fade-in`}>
        <Card className="glass-card rounded-3xl border-0 shadow-2xl">
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground">Bitte melden Sie sich an, um Ihr Profil zu sehen.</p>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  const handleSave = () => {
    if (newPassword && newPassword !== confirmPassword) {
      toast.error('Die Passwörter stimmen nicht überein.');
      return;
    }

    // Update user info
    updateUser({
      ...user,
      name,
      email,
      ...(newPassword ? {
        password: newPassword
      } : {})
    });
    setIsEditing(false);
    setCurrentPassword('');
    setNewPassword('');
    setConfirmPassword('');
    toast.success('Profil erfolgreich aktualisiert.');
  };
  
  return (
    <div className={`space-y-6 ${isMobile ? 'px-4 py-6' : 'p-6'} bg-gradient-to-br from-gray-50 to-gray-100 min-h-full animate-fade-in`}>
      <Card className="glass-card hover-lift rounded-3xl border-0 shadow-2xl animate-scale-in">
        <CardHeader className={`${isMobile ? 'pb-4' : 'pb-6'}`}>
          <CardTitle className={`${isMobile ? 'text-lg' : 'text-xl'} font-bold text-gray-800 flex items-center gap-3`}>
            <User className={`${isMobile ? 'h-5 w-5' : 'h-6 w-6'} text-blue-600`} />
            Persönliche Informationen
          </CardTitle>
          <CardDescription className={`${isMobile ? 'text-sm' : 'text-base'}`}>
            Verwalten Sie Ihre persönlichen Daten und Passwort
          </CardDescription>
        </CardHeader>
        <CardContent className={`${isMobile ? 'pt-0 pb-6' : 'pt-0 pb-8'}`}>
          <div className="space-y-4">
            {isEditing ? (
              <>
                <div className="space-y-2">
                  <Label htmlFor="name" className="flex items-center gap-2">
                    <User className="h-4 w-4 text-blue-600" />
                    Name
                  </Label>
                  <Input 
                    id="name" 
                    value={name} 
                    onChange={(e) => setName(e.target.value)}
                    className={`${isMobile ? 'h-12' : 'h-10'} rounded-xl border-2 focus:border-blue-500`}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email" className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-green-600" />
                    Email
                  </Label>
                  <Input 
                    id="email" 
                    type="email" 
                    value={email} 
                    onChange={(e) => setEmail(e.target.value)}
                    className={`${isMobile ? 'h-12' : 'h-10'} rounded-xl border-2 focus:border-green-500`}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="currentPassword" className="flex items-center gap-2">
                    <Shield className="h-4 w-4 text-orange-600" />
                    Aktuelles Passwort
                  </Label>
                  <Input 
                    id="currentPassword" 
                    type="password" 
                    value={currentPassword} 
                    onChange={(e) => setCurrentPassword(e.target.value)}
                    className={`${isMobile ? 'h-12' : 'h-10'} rounded-xl border-2 focus:border-orange-500`}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="newPassword" className="flex items-center gap-2">
                    <Shield className="h-4 w-4 text-red-600" />
                    Neues Passwort
                  </Label>
                  <Input 
                    id="newPassword" 
                    type="password" 
                    value={newPassword} 
                    onChange={(e) => setNewPassword(e.target.value)}
                    className={`${isMobile ? 'h-12' : 'h-10'} rounded-xl border-2 focus:border-red-500`}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="confirmPassword" className="flex items-center gap-2">
                    <Shield className="h-4 w-4 text-red-600" />
                    Passwort bestätigen
                  </Label>
                  <Input 
                    id="confirmPassword" 
                    type="password" 
                    value={confirmPassword} 
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className={`${isMobile ? 'h-12' : 'h-10'} rounded-xl border-2 focus:border-red-500`}
                  />
                </div>
                <div className={`flex ${isMobile ? 'flex-col gap-3' : 'justify-end'} space-x-0 ${!isMobile ? 'space-x-2' : ''} pt-4`}>
                  <Button 
                    variant="outline" 
                    onClick={() => setIsEditing(false)}
                    className={`${isMobile ? 'h-12 text-base' : 'h-10'} rounded-xl border-2 border-gray-300 hover:border-gray-500 transition-all duration-200`}
                  >
                    Abbrechen
                  </Button>
                  <Button 
                    onClick={handleSave}
                    className={`${isMobile ? 'h-12 text-base' : 'h-10'} rounded-xl bg-blue-600 hover:bg-blue-700 transition-all duration-200`}
                  >
                    Speichern
                  </Button>
                </div>
              </>
            ) : (
              <>
                <div className="space-y-4">
                  <div className={`flex ${isMobile ? 'flex-col gap-2' : 'justify-between items-center'} p-4 bg-gray-50 rounded-xl`}>
                    <span className="font-medium flex items-center gap-2">
                      <User className="h-4 w-4 text-blue-600" />
                      Name:
                    </span>
                    <span className={`${isMobile ? 'text-lg' : ''} font-semibold text-gray-800`}>{user.name}</span>
                  </div>
                  <div className={`flex ${isMobile ? 'flex-col gap-2' : 'justify-between items-center'} p-4 bg-gray-50 rounded-xl`}>
                    <span className="font-medium flex items-center gap-2">
                      <Mail className="h-4 w-4 text-green-600" />
                      Email:
                    </span>
                    <span className={`${isMobile ? 'text-lg' : ''} font-semibold text-gray-800`}>{user.email}</span>
                  </div>
                </div>
                <div className={`flex ${isMobile ? 'w-full' : 'justify-end'} pt-4`}>
                  <Button 
                    onClick={() => setIsEditing(true)}
                    className={`${isMobile ? 'h-12 text-base w-full' : 'h-10'} rounded-xl bg-blue-600 hover:bg-blue-700 transition-all duration-200 flex items-center gap-2`}
                  >
                    <Edit3 className="h-4 w-4" />
                    Bearbeiten
                  </Button>
                </div>
              </>
            )}
          </div>
        </CardContent>
      </Card>
      
      <Card className="glass-card hover-lift rounded-3xl border-0 shadow-2xl animate-scale-in">
        <CardHeader className={`${isMobile ? 'pb-4' : 'pb-6'}`}>
          <CardTitle className={`${isMobile ? 'text-lg' : 'text-xl'} font-bold text-gray-800 flex items-center gap-3`}>
            <Shield className={`${isMobile ? 'h-5 w-5' : 'h-6 w-6'} text-orange-600`} />
            Rolle und Team
          </CardTitle>
          <CardDescription className={`${isMobile ? 'text-sm' : 'text-base'}`}>
            Informationen zu Ihrer Rolle und Team
          </CardDescription>
        </CardHeader>
        <CardContent className={`${isMobile ? 'pt-0 pb-6' : 'pt-0 pb-8'}`}>
          <div className="space-y-4">
            <div className={`flex ${isMobile ? 'flex-col gap-2' : 'justify-between items-center'} p-4 bg-gray-50 rounded-xl`}>
              <span className="font-medium flex items-center gap-2">
                <Shield className="h-4 w-4 text-orange-600" />
                Rolle:
              </span>
              <span className={`${isMobile ? 'text-lg' : ''} font-semibold text-orange-600 bg-orange-100 px-3 py-1 rounded-lg`}>{user.role}</span>
            </div>
            {user.teamId && (
              <div className={`flex ${isMobile ? 'flex-col gap-2' : 'justify-between items-center'} p-4 bg-gray-50 rounded-xl`}>
                <span className="font-medium flex items-center gap-2">
                  <Users className="h-4 w-4 text-green-600" />
                  Team ID:
                </span>
                <span className={`${isMobile ? 'text-lg' : ''} font-semibold text-green-600 bg-green-100 px-3 py-1 rounded-lg`}>{user.teamId}</span>
              </div>
            )}
            {user.mentorId && (
              <div className={`flex ${isMobile ? 'flex-col gap-2' : 'justify-between items-center'} p-4 bg-gray-50 rounded-xl`}>
                <span className="font-medium flex items-center gap-2">
                  <UserCheck className="h-4 w-4 text-blue-600" />
                  Mentor ID:
                </span>
                <span className={`${isMobile ? 'text-lg' : ''} font-semibold text-blue-600 bg-blue-100 px-3 py-1 rounded-lg`}>{user.mentorId}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProfileView;
