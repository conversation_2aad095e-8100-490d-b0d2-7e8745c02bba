
import { toast } from 'sonner';

export interface GeocodeResult {
  longitude: number;
  latitude: number;
  place_name: string;
}

export class GeocodingService {
  private accessToken: string;

  constructor(accessToken: string) {
    this.accessToken = accessToken;
  }

  async geocodeAddress(address: string): Promise<GeocodeResult | null> {
    try {
      const encodedAddress = encodeURIComponent(address);
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodedAddress}.json?access_token=${this.accessToken}&country=DE&limit=1`
      );
      
      if (!response.ok) {
        throw new Error('Geocoding request failed');
      }

      const data = await response.json() as { features?: Array<{ center: [number, number]; place_name: string }> };

      if (data.features && data.features.length > 0) {
        const feature = data.features[0];
        if (feature && feature.center && feature.center.length >= 2) {
          return {
            longitude: feature.center[0],
            latitude: feature.center[1],
            place_name: feature.place_name
          };
        }
      }
      
      return null;
    } catch (error) {
      console.error('Geocoding error:', error);
      return null;
    }
  }

  async reverseGeocode(longitude: number, latitude: number): Promise<string | null> {
    try {
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${longitude},${latitude}.json?access_token=${this.accessToken}&limit=1&country=DE&types=address`
      );

      if (!response.ok) {
        throw new Error('Reverse geocoding request failed');
      }

      const data = await response.json() as { features?: Array<{ place_name: string }> };

      if (data.features && data.features.length > 0) {
        const feature = data.features[0];
        if (feature && feature.place_name) {
          return feature.place_name;
        }
      }

      return null;
    } catch (error) {
      console.error('Reverse geocoding error:', error);
      return null;
    }
  }

  async reverseGeocodeDetailed(longitude: number, latitude: number): Promise<{
    street?: string;
    houseNumber?: string;
    zipCode?: string;
    city?: string;
    fullAddress?: string;
  } | null> {
    try {
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${longitude},${latitude}.json?access_token=${this.accessToken}&limit=1&country=DE&types=address`
      );

      if (!response.ok) {
        throw new Error('Detailed reverse geocoding request failed');
      }

      const data = await response.json() as {
        features?: Array<{
          place_name: string;
          properties?: {
            address?: string;
          };
          context?: Array<{
            id: string;
            text: string;
          }>;
          text?: string;
          address?: string;
        }>
      };

      if (data.features && data.features.length > 0) {
        const feature = data.features[0];

        // Versuche strukturierte Daten zu extrahieren
        let street = '';
        let houseNumber = '';
        let zipCode = '';
        let city = '';

        // Straße und Hausnummer aus feature.text und feature.address
        if (feature.text) {
          street = feature.text;
        }

        if (feature.address) {
          houseNumber = feature.address;
        }

        // PLZ und Stadt aus context extrahieren
        if (feature.context) {
          for (const contextItem of feature.context) {
            if (contextItem.id.startsWith('postcode')) {
              zipCode = contextItem.text;
            } else if (contextItem.id.startsWith('place')) {
              city = contextItem.text;
            }
          }
        }

        return {
          street: street || undefined,
          houseNumber: houseNumber || undefined,
          zipCode: zipCode || undefined,
          city: city || undefined,
          fullAddress: feature.place_name
        };
      }

      return null;
    } catch (error) {
      console.error('Detailed reverse geocoding error:', error);
      return null;
    }
  }
}
