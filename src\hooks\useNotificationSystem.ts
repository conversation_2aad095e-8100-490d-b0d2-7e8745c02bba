import { useState, useEffect, useCallback } from 'react';
import { useData } from '@/context/data';
import { predictOptimalVisitWindows } from '@/utils/advancedPatternAnalysis';
import { triggerHapticFeedback } from './useSwipeGestures';
import { toast } from 'sonner';
import { format, isWithinInterval, addMinutes, isBefore, isAfter } from 'date-fns';
import { de } from 'date-fns/locale';

interface NotificationSettings {
  enableOptimalTimeNotifications: boolean;
  enableLocationBasedNotifications: boolean;
  enableStreakReminders: boolean;
  enableAchievementNotifications: boolean;
  notificationTiming: 'immediate' | '15min' | '30min' | '1hour';
  quietHours: {
    enabled: boolean;
    start: string; // HH:mm format
    end: string;   // HH:mm format
  };
}

interface ScheduledNotification {
  id: string;
  type: 'optimal_time' | 'location_based' | 'streak_reminder' | 'achievement';
  title: string;
  message: string;
  scheduledFor: Date;
  addressId?: string;
  data?: any;
}

export const useNotificationSystem = () => {
  const { getAddressesRequiringReturnVisits, visits, houses, addresses } = useData();
  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    enableOptimalTimeNotifications: true,
    enableLocationBasedNotifications: true,
    enableStreakReminders: true,
    enableAchievementNotifications: true,
    notificationTiming: '15min',
    quietHours: {
      enabled: true,
      start: '22:00',
      end: '07:00'
    }
  });

  const [scheduledNotifications, setScheduledNotifications] = useState<ScheduledNotification[]>([]);
  const [permissionStatus, setPermissionStatus] = useState<'default' | 'granted' | 'denied'>('default');

  // Request notification permission
  const requestNotificationPermission = useCallback(async (): Promise<boolean> => {
    if (!('Notification' in window)) {
      console.warn('This browser does not support notifications');
      return false;
    }

    if (Notification.permission === 'granted') {
      setPermissionStatus('granted');
      return true;
    }

    if (Notification.permission === 'denied') {
      setPermissionStatus('denied');
      return false;
    }

    try {
      const permission = await Notification.requestPermission();
      setPermissionStatus(permission);
      return permission === 'granted';
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      setPermissionStatus('denied');
      return false;
    }
  }, []);

  // Check if current time is within quiet hours
  const isQuietHours = useCallback((): boolean => {
    if (!notificationSettings.quietHours.enabled) return false;

    const now = new Date();
    const currentTime = format(now, 'HH:mm');
    const { start, end } = notificationSettings.quietHours;

    // Handle overnight quiet hours (e.g., 22:00 to 07:00)
    if (start > end) {
      return currentTime >= start || currentTime <= end;
    }
    
    // Handle same-day quiet hours (e.g., 12:00 to 14:00)
    return currentTime >= start && currentTime <= end;
  }, [notificationSettings.quietHours]);

  // Send notification
  const sendNotification = useCallback(async (
    title: string, 
    message: string, 
    options: NotificationOptions = {}
  ): Promise<void> => {
    if (isQuietHours()) {
      console.log('Notification suppressed due to quiet hours');
      return;
    }

    const hasPermission = await requestNotificationPermission();
    if (!hasPermission) {
      // Fallback to toast notification
      toast.info(title, { description: message });
      triggerHapticFeedback('medium');
      return;
    }

    try {
      const notification = new Notification(title, {
        body: message,
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        tag: 'visit-assistant',
        requireInteraction: false,
        ...options
      });

      notification.onclick = () => {
        window.focus();
        notification.close();
      };

      // Auto-close after 5 seconds
      setTimeout(() => {
        notification.close();
      }, 5000);

      triggerHapticFeedback('medium');
    } catch (error) {
      console.error('Error sending notification:', error);
      // Fallback to toast
      toast.info(title, { description: message });
      triggerHapticFeedback('medium');
    }
  }, [isQuietHours, requestNotificationPermission]);

  // Schedule optimal time notifications
  const scheduleOptimalTimeNotifications = useCallback(() => {
    if (!notificationSettings.enableOptimalTimeNotifications) return;

    const addressesRequiringVisits = getAddressesRequiringReturnVisits();
    const newNotifications: ScheduledNotification[] = [];

    addressesRequiringVisits.forEach(({ address, recommendations }) => {
      const predictiveWindows = predictOptimalVisitWindows(address.id, visits, houses);
      
      predictiveWindows.forEach((window, index) => {
        if (window.probability > 0.6) { // Only high-probability windows
          const notificationTime = new Date(window.startTime);
          
          // Adjust notification timing based on settings
          switch (notificationSettings.notificationTiming) {
            case '15min':
              notificationTime.setMinutes(notificationTime.getMinutes() - 15);
              break;
            case '30min':
              notificationTime.setMinutes(notificationTime.getMinutes() - 30);
              break;
            case '1hour':
              notificationTime.setHours(notificationTime.getHours() - 1);
              break;
          }

          // Only schedule future notifications
          if (isAfter(notificationTime, new Date())) {
            newNotifications.push({
              id: `optimal-${address.id}-${index}`,
              type: 'optimal_time',
              title: 'Optimale Besuchszeit!',
              message: `Jetzt ist eine gute Zeit für ${address.street}, ${address.city} (${Math.round(window.probability * 100)}% Erfolgswahrscheinlichkeit)`,
              scheduledFor: notificationTime,
              addressId: address.id,
              data: { window, address }
            });
          }
        }
      });
    });

    setScheduledNotifications(prev => [
      ...prev.filter(n => n.type !== 'optimal_time'),
      ...newNotifications
    ]);
  }, [notificationSettings, getAddressesRequiringReturnVisits, visits, houses]);

  // Schedule streak reminder notifications
  const scheduleStreakReminders = useCallback(() => {
    if (!notificationSettings.enableStreakReminders) return;

    const today = new Date();
    const todayVisits = visits.filter(v => {
      const visitDate = new Date(v.timestamp);
      return visitDate.toDateString() === today.toDateString();
    });

    const hasSuccessfulVisitToday = todayVisits.some(v => v.status !== 'N/A');

    // If no successful visit today, schedule reminder for evening
    if (!hasSuccessfulVisitToday) {
      const reminderTime = new Date();
      reminderTime.setHours(18, 0, 0, 0); // 6 PM reminder

      if (isAfter(reminderTime, new Date())) {
        const notification: ScheduledNotification = {
          id: 'streak-reminder-today',
          type: 'streak_reminder',
          title: 'Streak beibehalten!',
          message: 'Sie haben heute noch keinen erfolgreichen Besuch. Behalten Sie Ihre Serie bei!',
          scheduledFor: reminderTime
        };

        setScheduledNotifications(prev => [
          ...prev.filter(n => n.id !== 'streak-reminder-today'),
          notification
        ]);
      }
    }
  }, [notificationSettings.enableStreakReminders, visits]);

  // Process scheduled notifications
  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date();
      const dueNotifications = scheduledNotifications.filter(n => 
        isBefore(n.scheduledFor, now) || 
        isWithinInterval(now, { start: n.scheduledFor, end: addMinutes(n.scheduledFor, 1) })
      );

      dueNotifications.forEach(notification => {
        sendNotification(notification.title, notification.message);
      });

      // Remove processed notifications
      if (dueNotifications.length > 0) {
        setScheduledNotifications(prev => 
          prev.filter(n => !dueNotifications.some(due => due.id === n.id))
        );
      }
    }, 60000); // Check every minute

    return () => clearInterval(interval);
  }, [scheduledNotifications, sendNotification]);

  // Schedule notifications when data changes
  useEffect(() => {
    scheduleOptimalTimeNotifications();
    scheduleStreakReminders();
  }, [scheduleOptimalTimeNotifications, scheduleStreakReminders]);

  // Load settings from localStorage
  useEffect(() => {
    const savedSettings = localStorage.getItem('notificationSettings');
    if (savedSettings) {
      try {
        setNotificationSettings(JSON.parse(savedSettings));
      } catch (error) {
        console.error('Error loading notification settings:', error);
      }
    }
  }, []);

  // Save settings to localStorage
  useEffect(() => {
    localStorage.setItem('notificationSettings', JSON.stringify(notificationSettings));
  }, [notificationSettings]);

  // Update notification settings
  const updateSettings = useCallback((newSettings: Partial<NotificationSettings>) => {
    setNotificationSettings(prev => ({ ...prev, ...newSettings }));
  }, []);

  // Send immediate notification for achievements
  const notifyAchievement = useCallback((title: string, description: string) => {
    if (notificationSettings.enableAchievementNotifications) {
      sendNotification(`🏆 ${title}`, description, {
        tag: 'achievement',
        requireInteraction: true
      });
    }
  }, [notificationSettings.enableAchievementNotifications, sendNotification]);

  // Send location-based notification
  const notifyNearbyAddress = useCallback((address: string, distance: number) => {
    if (notificationSettings.enableLocationBasedNotifications) {
      sendNotification(
        'Adresse in der Nähe!',
        `Sie sind ${distance.toFixed(0)}m von ${address} entfernt. Möchten Sie einen Besuch erfassen?`,
        { tag: 'location' }
      );
    }
  }, [notificationSettings.enableLocationBasedNotifications, sendNotification]);

  return {
    notificationSettings,
    updateSettings,
    scheduledNotifications,
    permissionStatus,
    requestNotificationPermission,
    sendNotification,
    notifyAchievement,
    notifyNearbyAddress,
    isQuietHours: isQuietHours()
  };
};
