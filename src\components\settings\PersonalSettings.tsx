
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { useAuth } from '@/context/auth';
import { useSettings } from '@/context/settings/SettingsProvider';
import { toast } from 'sonner';
import { User, Mail, Globe, Bell } from 'lucide-react';
import { PasswordChangeDialog } from './PasswordChangeDialog';

export const PersonalSettings: React.FC = () => {
  const { user, updateUser } = useAuth();
  const { settings, updateSettings } = useSettings();
  const [name, setName] = useState(user?.name || '');
  const [email, setEmail] = useState(user?.email || '');

  useEffect(() => {
    if (user) {
      setName(user.name || '');
      setEmail(user.email || '');
    }
  }, [user]);

  const handleProfileSave = () => {
    if (user) {
      updateUser({
        ...user,
        name,
        email
      });
      toast.success('Profil-Informationen gespeichert');
    }
  };

  const handleLanguageChange = (language: string) => {
    updateSettings({ language });
    toast.success(`Sprache auf ${language === 'de' ? 'Deutsch' : 'English'} geändert`);
  };

  const handleTimezoneChange = (timezone: string) => {
    updateSettings({ timezone });
    toast.success('Zeitzone aktualisiert');
  };

  const handleNotificationChange = (type: 'emailNotifications' | 'pushNotifications', value: boolean) => {
    updateSettings({ [type]: value });
    toast.success(`${type === 'emailNotifications' ? 'E-Mail' : 'Push'}-Benachrichtigungen ${value ? 'aktiviert' : 'deaktiviert'}`);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Profil-Informationen
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Ihr vollständiger Name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">E-Mail</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-2">
            <Button onClick={handleProfileSave} className="flex-1">
              Profil speichern
            </Button>
            <PasswordChangeDialog />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Sprache & Region
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Sprache</Label>
              <Select value={settings.language} onValueChange={handleLanguageChange}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="de">Deutsch</SelectItem>
                  <SelectItem value="en">English</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Zeitzone</Label>
              <Select value={settings.timezone} onValueChange={handleTimezoneChange}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Europe/Berlin">Berlin (UTC+1)</SelectItem>
                  <SelectItem value="Europe/Vienna">Wien (UTC+1)</SelectItem>
                  <SelectItem value="Europe/Zurich">Zürich (UTC+1)</SelectItem>
                  <SelectItem value="America/New_York">New York (UTC-5)</SelectItem>
                  <SelectItem value="Asia/Tokyo">Tokyo (UTC+9)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Benachrichtigungen
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>E-Mail Benachrichtigungen</Label>
              <p className="text-sm text-muted-foreground">
                Erhalten Sie Updates per E-Mail
              </p>
            </div>
            <Switch
              checked={settings.emailNotifications}
              onCheckedChange={(checked) => handleNotificationChange('emailNotifications', checked)}
            />
          </div>
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Push-Benachrichtigungen</Label>
              <p className="text-sm text-muted-foreground">
                Browser-Benachrichtigungen aktivieren
              </p>
            </div>
            <Switch
              checked={settings.pushNotifications}
              onCheckedChange={(checked) => handleNotificationChange('pushNotifications', checked)}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
