import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Accordion, 
  AccordionContent, 
  AccordionItem, 
  AccordionTrigger 
} from '@/components/ui/accordion';
import { 
  Clock, 
  Calendar, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle,
  Download,
  MapPin
} from 'lucide-react';
import { useData } from '@/context/data';
import { VisitRecommendation } from '@/types';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';

interface VisitRecommendationsProps {
  className?: string;
}

export const VisitRecommendations: React.FC<VisitRecommendationsProps> = ({ className }) => {
  const { getAddressesRequiringReturnVisits, getHighPriorityRecommendations } = useData();
  const [showA<PERSON>, setShowAll] = useState(false);
  
  const addressesRequiringVisits = getAddressesRequiringReturnVisits();
  const highPriorityRecommendations = getHighPriorityRecommendations();
  
  const displayedAddresses = showAll ? addressesRequiringVisits : addressesRequiringVisits.slice(0, 5);

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'medium':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'low':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'high':
        return <Badge variant="destructive">Hoch</Badge>;
      case 'medium':
        return <Badge className="bg-yellow-500">Mittel</Badge>;
      case 'low':
        return <Badge className="bg-green-500">Niedrig</Badge>;
      default:
        return <Badge variant="outline">Unbekannt</Badge>;
    }
  };

  const getRecommendationTypeIcon = (type: string) => {
    switch (type) {
      case 'optimal_time':
        return <Clock className="h-4 w-4 text-blue-500" />;
      case 'avoid_time':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'day_preference':
        return <Calendar className="h-4 w-4 text-green-500" />;
      case 'general_advice':
        return <TrendingUp className="h-4 w-4 text-purple-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatConfidence = (confidence: number) => {
    return `${Math.round(confidence * 100)}%`;
  };

  const downloadRecommendations = () => {
    const exportData = {
      exportDate: new Date().toISOString(),
      totalAddresses: addressesRequiringVisits.length,
      highPriorityCount: highPriorityRecommendations.length,
      addresses: addressesRequiringVisits.map(({ address, recommendations, failedVisitCount }) => ({
        address: `${address.street}, ${address.zipCode} ${address.city}`,
        failedVisits: failedVisitCount,
        recommendations: recommendations.map(rec => ({
          type: rec.recommendationType,
          priority: rec.priority,
          confidence: formatConfidence(rec.confidence),
          recommendation: rec.recommendation,
          suggestedTimeSlots: rec.suggestedTimeSlots?.map(slot => ({
            day: ['Sonntag', 'Montag', 'Dienstag', 'Mittwoch', 'Donnerstag', 'Freitag', 'Samstag'][slot.day] || 'Beliebig',
            time: `${slot.startHour}:00 - ${slot.endHour}:00`,
            confidence: formatConfidence(slot.confidence)
          }))
        }))
      }))
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `besuchsempfehlungen-${format(new Date(), 'yyyy-MM-dd')}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(link.href);
  };

  if (addressesRequiringVisits.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <p className="text-muted-foreground mb-2">Keine Wiederholungsbesuche erforderlich</p>
            <p className="text-sm text-muted-foreground">Alle Besuche waren erfolgreich!</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Intelligente Besuchsempfehlungen
          </CardTitle>
          <div className="flex gap-2">
            <Badge variant="outline" className="bg-blue-50">
              {addressesRequiringVisits.length} Adressen
            </Badge>
            <Button variant="outline" size="sm" onClick={downloadRecommendations}>
              <Download className="h-4 w-4 mr-1" />
              Export
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* High Priority Summary */}
        {highPriorityRecommendations.length > 0 && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              <span className="font-semibold text-red-700">Hohe Priorität</span>
              <Badge variant="destructive">{highPriorityRecommendations.length}</Badge>
            </div>
            <p className="text-sm text-red-600">
              {highPriorityRecommendations.length} Empfehlung{highPriorityRecommendations.length !== 1 ? 'en' : ''} mit hoher Priorität verfügbar
            </p>
          </div>
        )}

        {/* Address Recommendations */}
        <Accordion type="single" collapsible className="w-full">
          {displayedAddresses.map(({ address, recommendations, failedVisitCount }) => (
            <AccordionItem key={address.id} value={address.id}>
              <AccordionTrigger className="hover:no-underline">
                <div className="flex justify-between w-full pr-4">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-gray-500" />
                    <span>{address.street}, {address.city}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="bg-red-50">
                      {failedVisitCount} fehlgeschlagen
                    </Badge>
                    <Badge variant="outline" className="bg-blue-50">
                      {recommendations.length} Empfehlung{recommendations.length !== 1 ? 'en' : ''}
                    </Badge>
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <div className="space-y-3 pl-6">
                  {recommendations.map((recommendation) => (
                    <div key={recommendation.id} className="border rounded-lg p-3 bg-gray-50">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          {getRecommendationTypeIcon(recommendation.recommendationType)}
                          <span className="font-medium text-sm">
                            {recommendation.recommendationType === 'optimal_time' && 'Optimale Zeit'}
                            {recommendation.recommendationType === 'avoid_time' && 'Zeit vermeiden'}
                            {recommendation.recommendationType === 'day_preference' && 'Tagesbevorzugung'}
                            {recommendation.recommendationType === 'general_advice' && 'Allgemeine Empfehlung'}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          {getPriorityBadge(recommendation.priority)}
                          <Badge variant="outline" className="text-xs">
                            {formatConfidence(recommendation.confidence)} Vertrauen
                          </Badge>
                        </div>
                      </div>
                      
                      <p className="text-sm text-gray-700 mb-2">
                        {recommendation.recommendation}
                      </p>
                      
                      {recommendation.suggestedTimeSlots && recommendation.suggestedTimeSlots.length > 0 && (
                        <div className="mt-2">
                          <p className="text-xs font-medium text-gray-600 mb-1">Empfohlene Zeitfenster:</p>
                          <div className="flex flex-wrap gap-1">
                            {recommendation.suggestedTimeSlots.map((slot, index) => (
                              <Badge key={index} variant="outline" className="text-xs bg-green-50">
                                {slot.day !== -1 
                                  ? ['So', 'Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa'][slot.day]
                                  : 'Täglich'
                                } {slot.startHour}:00-{slot.endHour}:00
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      <div className="flex justify-between items-center mt-2 text-xs text-gray-500">
                        <span>Basiert auf {recommendation.basedOnVisits} fehlgeschlagenen Besuchen</span>
                        <span>Erstellt: {format(new Date(recommendation.createdAt), 'dd.MM.yyyy', { locale: de })}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>

        {/* Show More/Less Button */}
        {addressesRequiringVisits.length > 5 && (
          <div className="mt-4 text-center">
            <Button 
              variant="outline" 
              onClick={() => setShowAll(!showAll)}
              className="w-full"
            >
              {showAll 
                ? `Weniger anzeigen` 
                : `${addressesRequiringVisits.length - 5} weitere Adressen anzeigen`
              }
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default VisitRecommendations;
