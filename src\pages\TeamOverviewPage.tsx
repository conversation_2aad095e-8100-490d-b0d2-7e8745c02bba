
import React from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { useAuth } from '@/context/auth';
import MentorDashboard from '@/components/dashboard/MentorDashboard';

const TeamOverviewPage: React.FC = () => {
  const { user } = useAuth();

  if (!user || user.role !== 'mentor') {
    return (
      <MainLayout title="Team Übersicht">
        <div className="p-4 text-center">
          Sie haben keine Berechtigung, diese Seite anzuzeigen.
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout title="Team Übersicht">
      <MentorDashboard mentorId={user.id} />
    </MainLayout>
  );
};

export default TeamOverviewPage;
