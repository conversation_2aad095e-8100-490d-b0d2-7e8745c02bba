
import React from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { useAuth } from '@/context/auth';
import GebietsmanagerDashboard from '@/components/dashboard/GebietsmanagerDashboard';

const AreaOverviewPage: React.FC = () => {
  const { user } = useAuth();

  if (!user || user.role !== 'gebietsmanager') {
    return (
      <MainLayout title="Gebietsübersicht">
        <div className="p-4 text-center">
          Sie haben keine Berechtigung, diese Seite anzuzeigen.
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout title="Gebietsübersicht">
      <GebietsmanagerDashboard />
    </MainLayout>
  );
};

export default AreaOverviewPage;
