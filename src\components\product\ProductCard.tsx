
import React from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { MinusCircle, Monitor, Tv, Smartphone } from 'lucide-react';
import { ProductCategory, productOptions } from '@/types';

interface ProductCardProps {
  product: {
    category: ProductCategory;
    type: string;
    quantity: number;
  };
  index: number;
  categoryIndex: number;
  onRemove: () => void;
  onUpdateType: (type: string) => void;
  onUpdateQuantity: (quantity: number) => void;
}

export const ProductCard: React.FC<ProductCardProps> = ({
  product,
  index,
  categoryIndex,
  onRemove,
  onUpdateType,
  onUpdateQuantity
}) => {
  const getCategoryIcon = (category: ProductCategory) => {
    switch (category) {
      case 'KIP':
        return <Monitor className="h-6 w-6" />;
      case 'TV':
        return <Tv className="h-6 w-6" />;
      case 'Mobile':
        return <Smartphone className="h-6 w-6" />;
      default:
        return <Monitor className="h-6 w-6" />;
    }
  };

  const getCategoryLabel = (category: ProductCategory) => {
    switch (category) {
      case 'KIP':
        return 'KIP Option';
      case 'TV':
        return 'TV Option';
      case 'Mobile':
        return 'Mobile Option';
      default:
        return 'Option';
    }
  };

  const showTypeSelector = product.category !== 'KIP';

  return (
    <div className="glass-card rounded-3xl p-6 border border-gray-200/50 shadow-lg hover-lift">
      <div className="flex items-center justify-between mb-4">
        <h4 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
          {getCategoryIcon(product.category)}
          {getCategoryLabel(product.category)} {categoryIndex + 1}
        </h4>
        <Button 
          variant="ghost" 
          size="icon" 
          className="h-12 w-12 rounded-full hover:bg-red-100 hover:text-red-600 transition-colors touch-feedback"
          onClick={onRemove}
        >
          <MinusCircle size={24} />
        </Button>
      </div>
      
      <div className="space-y-4">
        {showTypeSelector && (
          <div>
            <Label htmlFor={`${product.category.toLowerCase()}-type-${index}`} className="text-base font-medium text-gray-700 mb-2 block">
              Typ
            </Label>
            <Select 
              value={product.type} 
              onValueChange={onUpdateType}
            >
              <SelectTrigger id={`${product.category.toLowerCase()}-type-${index}`} className="h-14 md:h-16 text-lg rounded-2xl border-2 border-gray-200 focus:border-red-500 touch-feedback">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-white border-2 border-gray-200 shadow-2xl rounded-2xl z-50">
                {productOptions
                  .filter(option => option.category === product.category)
                  .map(option => (
                    <SelectItem key={option.type} value={option.type} className="h-12 text-base touch-feedback">
                      {option.label}
                    </SelectItem>
                  ))
                }
              </SelectContent>
            </Select>
          </div>
        )}
        
        <div>
          <Label htmlFor={`${product.category.toLowerCase()}-quantity-${index}`} className="text-base font-medium text-gray-700 mb-2 block">
            Anzahl
          </Label>
          <Input 
            id={`${product.category.toLowerCase()}-quantity-${index}`}
            type="number" 
            min={1}
            value={product.quantity} 
            onChange={(e) => onUpdateQuantity(parseInt(e.target.value))} 
            className="h-14 md:h-16 text-lg rounded-2xl border-2 border-gray-200 focus:border-red-500 touch-feedback"
          />
        </div>
      </div>
    </div>
  );
};
