
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription } from '@/components/ui/card';
import { useData } from '@/context/data';
import { useAuth } from '@/context/auth';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useIsMobile } from '@/hooks/use-mobile';
import { Users, Building, Home, TrendingUp } from 'lucide-react';

const GebietsmanagerDashboard: React.FC = () => {
  const { visits, houses, products } = useData();
  const { users } = useAuth();
  const isMobile = useIsMobile();
  
  // Finde alle Teams und deren IDs
  const teams = [...new Set(users
    .filter(user => user.teamId)
    .map(user => user.teamId))];
  
  // Statistiken pro Team
  const teamStats = teams.map(teamId => {
    const teamMembers = users.filter(user => user.teamId === teamId);
    const teamLeader = teamMembers.find(user => user.role === 'teamleiter')?.name || 'Nicht zugewiesen';
    const beratersCount = teamMembers.filter(user => user.role === 'berater').length;
    const mentorsCount = teamMembers.filter(user => user.role === 'mentor').length;
    
    const teamVisits = visits.filter(v => {
      const visitUser = teamMembers.find(u => u.id === v.userId);
      return visitUser !== undefined;
    });
    
    const teamProducts = products.filter(p => {
      const productUser = teamMembers.find(u => u.id === p.userId);
      return productUser !== undefined;
    });
    
    return {
      teamId,
      teamLeader,
      memberCount: teamMembers.length,
      beratersCount,
      mentorsCount,
      visitsCount: teamVisits.length,
      productsCount: teamProducts.length
    };
  });

  // Gesamtzahlen
  const totalUsers = users.length;
  const totalVisits = visits.length;
  const totalHouses = houses.length;
  const totalProducts = products.length;

  const statsCards = [
    {
      title: "Benutzer",
      value: totalUsers,
      icon: Users,
      color: "from-blue-500 to-blue-600",
      textColor: "text-blue-600"
    },
    {
      title: "Teams",
      value: teams.length,
      icon: Building,
      color: "from-purple-500 to-purple-600",
      textColor: "text-purple-600"
    },
    {
      title: "Häuser",
      value: totalHouses,
      icon: Home,
      color: "from-green-500 to-green-600",
      textColor: "text-green-600"
    },
    {
      title: "Verkäufe",
      value: totalProducts,
      icon: TrendingUp,
      color: "from-orange-500 to-orange-600",
      textColor: "text-orange-600"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 p-4 md:p-6">
      <div className="max-w-7xl mx-auto space-y-6 md:space-y-8">
        {/* Header Section */}
        <div className="text-center md:text-left space-y-2 md:space-y-4 animate-fade-in">
          <h1 className={`font-bold text-gray-800 ${isMobile ? 'text-2xl' : 'text-4xl'}`}>
            Gebietsmanager Dashboard
          </h1>
          <p className={`text-gray-600 ${isMobile ? 'text-sm' : 'text-lg'}`}>
            Gesamtübersicht über alle Teams und Aktivitäten
          </p>
          <p className={`text-gray-500 ${isMobile ? 'text-xs' : 'text-sm'}`}>
            {format(new Date(), "'Stand:' d. MMMM yyyy", { locale: de })}
          </p>
        </div>

        {/* Stats Cards */}
        <div className={`grid gap-4 md:gap-6 ${isMobile ? 'grid-cols-2' : 'grid-cols-2 md:grid-cols-4'}`}>
          {statsCards.map((stat, index) => (
            <Card key={stat.title} className={`glass-card hover-lift ${isMobile ? 'p-3' : 'p-4'} rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-scale-in`} style={{ animationDelay: `${index * 0.1}s` }}>
              <CardContent className="p-0">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className={`font-medium text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                      {stat.title}
                    </p>
                    <p className={`font-bold ${stat.textColor} ${isMobile ? 'text-xl' : 'text-3xl'}`}>
                      {stat.value}
                    </p>
                  </div>
                  <div className={`rounded-2xl bg-gradient-to-br ${stat.color} p-2 shadow-lg`}>
                    <stat.icon className={`${isMobile ? 'h-5 w-5' : 'h-6 w-6'} text-white`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        
        {/* Team Overview Table */}
        <Card className="glass-card hover-lift rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-slide-in">
          <CardHeader className={`${isMobile ? 'p-4 pb-2' : 'p-6 pb-4'}`}>
            <CardTitle className={`flex items-center gap-3 ${isMobile ? 'text-lg' : 'text-xl'} font-bold text-gray-800`}>
              <Building className={`${isMobile ? 'h-5 w-5' : 'h-6 w-6'} text-blue-600`} />
              Team-Übersicht
            </CardTitle>
            <CardDescription className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-600`}>
              {format(new Date(), "'Stand:' d. MMMM yyyy", { locale: de })}
            </CardDescription>
          </CardHeader>
          <CardContent className={`${isMobile ? 'p-4 pt-0' : 'p-6 pt-0'}`}>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-gray-700`}>Team</TableHead>
                    <TableHead className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-gray-700`}>Teamleiter</TableHead>
                    <TableHead className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-gray-700`}>Mitglieder</TableHead>
                    <TableHead className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-gray-700`}>Besuche</TableHead>
                    <TableHead className={`${isMobile ? 'text-xs' : 'text-sm'} font-semibold text-gray-700`}>Verkäufe</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {teamStats.map((team, index) => (
                    <TableRow key={team.teamId?.toString()} className="hover:bg-blue-50/50 transition-colors animate-fade-in" style={{ animationDelay: `${index * 0.1}s` }}>
                      <TableCell className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-gray-800`}>
                        {team.teamId}
                      </TableCell>
                      <TableCell className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-700`}>
                        {team.teamLeader}
                      </TableCell>
                      <TableCell className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-700`}>
                        <div className="flex flex-col">
                          <span className="font-medium">{team.memberCount}</span>
                          <span className="text-gray-500 text-xs">
                            ({team.beratersCount} Berater, {team.mentorsCount} Mentoren)
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-700`}>
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {team.visitsCount}
                        </span>
                      </TableCell>
                      <TableCell className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-700`}>
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          {team.productsCount}
                        </span>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default GebietsmanagerDashboard;
