
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/design-system/components/Card';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Door } from '@/types';
import { cn } from '@/lib/utils';

interface DoorSelectionProps {
  salesDoors: Door[];
  selectedDoors: string[];
  onDoorToggle: (doorId: string) => void;
}

export const DoorSelection: React.FC<DoorSelectionProps> = ({
  salesDoors,
  selectedDoors,
  onDoorToggle
}) => {
  const isMultiDoor = salesDoors.length > 1;

  if (!isMultiDoor) {
    return null;
  }

  return (
    <Card variant="default" className="mb-6">
      <CardHeader>
        <CardTitle className="text-lg">
          Türen auswählen ({selectedDoors.length} von {salesDoors.length} ausgewählt)
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {salesDoors.map(door => (
            <div
              key={door.id}
              className={cn(
                "flex items-center space-x-4 p-4 rounded-xl border-2 transition-all duration-250 cursor-pointer",
                "hover:bg-blue-50 hover:border-blue-200",
                selectedDoors.includes(door.id)
                  ? "bg-blue-50 border-blue-300"
                  : "bg-white border-neutral-200"
              )}
              onClick={() => onDoorToggle(door.id)}
            >
              <Checkbox
                id={`door-${door.id}`}
                checked={selectedDoors.includes(door.id)}
                onCheckedChange={() => onDoorToggle(door.id)}
                className="h-5 w-5"
              />
              <Label
                htmlFor={`door-${door.id}`}
                className="text-base font-medium text-neutral-800 cursor-pointer flex-1"
              >
                {door.name} {door.floor ? `(${door.floor})` : ''}
              </Label>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
