
import React, { useState, useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useData } from '@/context/data';
import { VisitStatus } from '@/types';
import { toast } from 'sonner';
import { DoorCreationStep } from './DoorCreationStep';
import { StatusSelectionStep } from './StatusSelectionStep';
import { AppointmentDialog } from './AppointmentDialog';
import { format } from 'date-fns';
import { Card, AddressCard } from '@/design-system/components/Card';
import { Button, StatusButton } from '@/design-system/components/Button';
import { useSwipeGestures, triggerHapticFeedback } from '@/hooks/useSwipeGestures';
import ErrorBoundary from '@/components/ErrorBoundary';

interface EFHVisitTrackerProps {
  visitId: string;
}

const EFHVisitTracker: React.FC<EFHVisitTrackerProps> = ({ visitId }) => {
  const navigate = useNavigate();
  const { getVisit, getHouseById, getAddressById, updateVisitStatus, addDoor, getDoorsByVisit, updateDoorStatus } = useData();
  const [isUpdating, setIsUpdating] = useState(false);
  const [isCreatingDoor, setIsCreatingDoor] = useState(false);
  const [showAppointmentDialog, setShowAppointmentDialog] = useState(false);

  // Memoized data to prevent unnecessary re-renders
  const visitData = useMemo(() => {
    const visit = getVisit(visitId);
    const house = visit ? getHouseById(visit.houseId) : null;
    const address = house ? getAddressById(house.addressId) : null;
    const existingDoors = getDoorsByVisit(visitId);

    return { visit, house, address, existingDoors };
  }, [visitId, getVisit, getHouseById, getAddressById, getDoorsByVisit]);

  const { visit, house, address, existingDoors } = visitData;

  if (!visit || !house || !address) {
    return (
      <Card variant="error" className="max-w-md mx-auto m-4">
        <div className="text-center p-8">
          <p className="text-red-600 font-medium">Besuch nicht gefunden</p>
          <Button
            variant="outline"
            onClick={() => navigate('/')}
            className="mt-4"
          >
            Zur Startseite
          </Button>
        </div>
      </Card>
    );
  }

  const handleCreateDoor = useCallback(async (doorName: string) => {
    setIsCreatingDoor(true);
    triggerHapticFeedback('light');

    try {
      addDoor({
        visitId: visitId,
        name: doorName,
        status: 'N/A'
      });
      toast.success('Tür erfolgreich erstellt');
      triggerHapticFeedback('medium');
    } catch (error) {
      toast.error('Fehler beim Erstellen der Tür');
      triggerHapticFeedback('heavy');
    } finally {
      setIsCreatingDoor(false);
    }
  }, [visitId, addDoor]);

  const handleStatusUpdate = useCallback(async (status: VisitStatus) => {
    if (status === 'Angetroffen → Termin') {
      setShowAppointmentDialog(true);
      return;
    }

    setIsUpdating(true);
    triggerHapticFeedback('medium');

    try {
      updateVisitStatus(visitId, status);

      // Update door status if it exists
      if (existingDoors.length > 0) {
        updateDoorStatus(existingDoors[0]!.id, status);
      }

      // Status-specific success messages and navigation
      const statusMessages = {
        'N/A': 'Nicht angetroffen gespeichert',
        'Angetroffen → Kein Interesse': 'Kein Interesse gespeichert',
        'Angetroffen → Sale': 'Verkauf gespeichert! 🎉',
        'Angetroffen → Termin': 'Termin vereinbart',
      };

      toast.success(statusMessages[status]);

      // Navigate based on status
      if (status === 'Angetroffen → Sale') {
        navigate(`/products/${visitId}`);
      } else if (status === 'N/A' || status === 'Angetroffen → Kein Interesse') {
        // Quick redirect to home for N/A and no interest
        setTimeout(() => {
          navigate('/');
        }, 1000); // 1 second delay to show success message
      }
    } catch (error) {
      toast.error('Fehler beim Aktualisieren des Status');
      triggerHapticFeedback('heavy');
    } finally {
      setIsUpdating(false);
    }
  }, [visitId, existingDoors, updateVisitStatus, updateDoorStatus, navigate]);

  const handleAppointmentConfirm = async (date: string, time: string) => {
    setIsUpdating(true);
    try {
      updateVisitStatus(visitId, 'Angetroffen → Termin', date, time);
      
      // Update door status if it exists
      if (existingDoors.length > 0) {
        updateDoorStatus(existingDoors[0].id, 'Angetroffen → Termin', date, time);
      }
      
      toast.success(`Termin erfolgreich vereinbart für ${format(new Date(date), 'dd.MM.yyyy')} um ${time}`);
    } catch (error) {
      toast.error('Fehler beim Speichern des Termins');
    } finally {
      setIsUpdating(false);
    }
  };

  // Swipe gestures for navigation
  const swipeGestures = useSwipeGestures({
    onSwipeLeft: () => {
      if (existingDoors.length > 0) {
        // Quick action: mark as sale
        handleStatusUpdate('Angetroffen → Sale');
      }
    },
    onSwipeRight: () => {
      navigate(-1);
    },
    threshold: 100
  });

  // Step 1: Show door creation if no door exists
  if (existingDoors.length === 0) {
    return (
      <ErrorBoundary>
        <div ref={swipeGestures.ref} className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
          <DoorCreationStep
            visitId={visitId}
            address={address}
            house={house}
            onCreateDoor={handleCreateDoor}
            isCreating={isCreatingDoor}
          />
        </div>
      </ErrorBoundary>
    );
  }

  // Step 2: Show status selection when door exists
  return (
    <ErrorBoundary>
      <div ref={swipeGestures.ref} className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <StatusSelectionStep
          visitId={visitId}
          visit={visit}
          address={address}
          house={house}
          existingDoors={existingDoors}
          onStatusUpdate={handleStatusUpdate}
          isUpdating={isUpdating}
        />

        {/* Appointment Dialog */}
        <AppointmentDialog
          open={showAppointmentDialog}
          onClose={() => setShowAppointmentDialog(false)}
          onConfirm={handleAppointmentConfirm}
        />

        {/* Swipe hint */}
        <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 text-center text-sm text-neutral-500 bg-white/80 backdrop-blur-sm rounded-full px-4 py-2">
          💡 Wischen Sie nach links für schnellen Verkauf
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default EFHVisitTracker;
