
import React, { useState, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useIsMobile } from '@/hooks/use-mobile';
import { MapPin } from 'lucide-react';
import { useMapbox } from './hooks/useMapbox';
import { MapTokenInput } from './components/MapTokenInput';
import { EnhancedMapControls } from './components/EnhancedMapControls';
import { EnhancedMapLegend } from './components/EnhancedMapLegend';

const MapView: React.FC = () => {
  const [mapboxToken, setMapboxToken] = useState('');
  const [showTokenInput, setShowTokenInput] = useState(true);
  const isMobile = useIsMobile();
  
  const {
    mapContainer,
    mapLoaded,
    currentLocation,
    isTracking,
    showFiltered,
    routeInfo,
    hasRoute,
    initializeMap,
    getCurrentLocation,
    toggleTracking,
    toggleFilter,
    routeToNearest
  } = useMapbox(mapboxToken);

  const handleTokenSubmit = useCallback((token: string) => {
    setMapboxToken(token);
    setShowTokenInput(false);
    setTimeout(() => initializeMap(), 100);
  }, [initializeMap]);

  const handleAutoLoad = useCallback((token: string) => {
    setMapboxToken(token);
    setShowTokenInput(false);
    setTimeout(() => initializeMap(), 100);
  }, [initializeMap]);

  const getLocationStatus = () => {
    if (isTracking) return '🔴 Live-Tracking';
    if (currentLocation) return '📍 GPS aktiv';
    return '';
  };

  return (
    <div className="w-full h-full">
      <Card className="h-full w-full border-0 rounded-none md:border md:rounded-lg">
        <CardHeader className="pb-2">
          <CardTitle className="text-base flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Kartenansicht
            {(currentLocation || isTracking) && (
              <span className={`text-xs ml-2 ${isTracking ? 'text-red-600' : 'text-green-600'}`}>
                {getLocationStatus()}
              </span>
            )}
            {showFiltered && (
              <span className="text-xs ml-2 text-orange-600">
                🔍 Gefiltert
              </span>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0 h-[calc(100%-60px)]">
          {showTokenInput ? (
            <MapTokenInput 
              onTokenSubmit={handleTokenSubmit}
              onAutoLoad={handleAutoLoad}
            />
          ) : !mapLoaded ? (
            <div className="flex items-center justify-center h-full w-full">
              <div className="flex flex-col items-center space-y-4">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
                <p className="text-sm text-muted-foreground">Karte wird geladen...</p>
              </div>
            </div>
          ) : (
            <div className="relative w-full h-full">
              <div ref={mapContainer} className="w-full h-full" />
              
              <EnhancedMapControls 
                onLocationClick={getCurrentLocation}
                onTrackingToggle={toggleTracking}
                onRouteToNearest={routeToNearest}
                onToggleFilter={toggleFilter}
                isTracking={isTracking}
                currentLocation={currentLocation}
                hasRoute={hasRoute}
                showFiltered={showFiltered}
              />
              <EnhancedMapLegend 
                currentLocation={currentLocation} 
                routeInfo={routeInfo}
                showFiltered={showFiltered}
              />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default MapView;
