
import React from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { useAuth } from '@/context/auth';
import TeamleiterDashboard from '@/components/dashboard/TeamleiterDashboard';

const TeamsOverviewPage: React.FC = () => {
  const { user } = useAuth();

  if (!user || user.role !== 'teamleiter') {
    return (
      <MainLayout title="Teams Übersicht">
        <div className="p-4 text-center w-full">
          Sie haben keine Berechtigung, diese Seite anzuzeigen.
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout title="Teams Übersicht">
      <div className="w-full h-full">
        {user.teamId && <TeamleiterDashboard teamId={user.teamId} />}
      </div>
    </MainLayout>
  );
};

export default TeamsOverviewPage;
