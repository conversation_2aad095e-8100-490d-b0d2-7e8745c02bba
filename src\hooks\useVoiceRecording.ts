import { useState, useRef, useCallback, useEffect } from 'react';
import { triggerHapticFeedback } from './useSwipeGestures';
import { toast } from 'sonner';

interface VoiceRecordingOptions {
  maxDuration?: number; // seconds
  enableAutoTranscription?: boolean;
  onRecordingComplete?: (audioBlob: Blob, duration: number) => void;
  onTranscriptionComplete?: (text: string) => void;
  onError?: (error: string) => void;
}

interface RecordingState {
  isRecording: boolean;
  isPaused: boolean;
  duration: number;
  audioLevel: number;
  error: string | null;
}

export const useVoiceRecording = (options: VoiceRecordingOptions = {}) => {
  const {
    maxDuration = 300, // 5 minutes default
    enableAutoTranscription = false,
    onRecordingComplete,
    onTranscriptionComplete,
    onError
  } = options;

  const [recordingState, setRecordingState] = useState<RecordingState>({
    isRecording: false,
    isPaused: false,
    duration: 0,
    audioLevel: 0,
    error: null
  });

  const [permissionStatus, setPermissionStatus] = useState<'granted' | 'denied' | 'prompt' | 'unknown'>('unknown');
  const [recordings, setRecordings] = useState<{ id: string; blob: Blob; duration: number; timestamp: string; transcription?: string }[]>([]);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioStreamRef = useRef<MediaStream | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const animationFrameRef = useRef<number | null>(null);
  const startTimeRef = useRef<number>(0);
  const chunksRef = useRef<Blob[]>([]);

  // Request microphone permission
  const requestPermission = useCallback(async (): Promise<boolean> => {
    try {
      if ('permissions' in navigator) {
        const permission = await navigator.permissions.query({ name: 'microphone' as PermissionName });
        setPermissionStatus(permission.state);
        
        if (permission.state === 'denied') {
          const errorMsg = 'Mikrofon-Berechtigung verweigert. Bitte in den Einstellungen aktivieren.';
          setRecordingState(prev => ({ ...prev, error: errorMsg }));
          onError?.(errorMsg);
          return false;
        }
      }

      // Test microphone access
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 44100
        } 
      });
      
      stream.getTracks().forEach(track => track.stop());
      setPermissionStatus('granted');
      return true;
    } catch (error) {
      const errorMsg = 'Mikrofon-Zugriff fehlgeschlagen';
      setRecordingState(prev => ({ ...prev, error: errorMsg }));
      onError?.(errorMsg);
      setPermissionStatus('denied');
      return false;
    }
  }, [onError]);

  // Audio level monitoring
  const monitorAudioLevel = useCallback(() => {
    if (!analyserRef.current) return;

    const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
    analyserRef.current.getByteFrequencyData(dataArray);
    
    const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;
    const normalizedLevel = average / 255;
    
    setRecordingState(prev => ({ ...prev, audioLevel: normalizedLevel }));
    
    if (recordingState.isRecording && !recordingState.isPaused) {
      animationFrameRef.current = requestAnimationFrame(monitorAudioLevel);
    }
  }, [recordingState.isRecording, recordingState.isPaused]);

  // Update recording duration
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (recordingState.isRecording && !recordingState.isPaused) {
      interval = setInterval(() => {
        const elapsed = (Date.now() - startTimeRef.current) / 1000;
        setRecordingState(prev => ({ ...prev, duration: elapsed }));
        
        // Auto-stop at max duration
        if (elapsed >= maxDuration) {
          stopRecording();
          toast.warning(`Aufnahme automatisch beendet (${maxDuration}s erreicht)`);
        }
      }, 100);
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [recordingState.isRecording, recordingState.isPaused, maxDuration]);

  // Start recording
  const startRecording = useCallback(async () => {
    const hasPermission = await requestPermission();
    if (!hasPermission) return;

    try {
      // Get audio stream
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 44100
        } 
      });
      
      audioStreamRef.current = stream;

      // Setup audio context for level monitoring
      audioContextRef.current = new AudioContext();
      const source = audioContextRef.current.createMediaStreamSource(stream);
      analyserRef.current = audioContextRef.current.createAnalyser();
      analyserRef.current.fftSize = 256;
      source.connect(analyserRef.current);

      // Setup MediaRecorder
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      
      mediaRecorderRef.current = mediaRecorder;
      chunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(chunksRef.current, { type: 'audio/webm' });
        const duration = recordingState.duration;
        
        // Save recording
        const recording = {
          id: Date.now().toString(),
          blob: audioBlob,
          duration,
          timestamp: new Date().toISOString()
        };
        
        setRecordings(prev => [...prev, recording]);
        onRecordingComplete?.(audioBlob, duration);
        
        // Auto-transcription if enabled
        if (enableAutoTranscription) {
          transcribeAudio(audioBlob);
        }
        
        triggerHapticFeedback('success');
        toast.success(`Sprachnotiz aufgenommen (${duration.toFixed(1)}s)`);
      };

      // Start recording
      mediaRecorder.start(100); // Collect data every 100ms
      startTimeRef.current = Date.now();
      
      setRecordingState({
        isRecording: true,
        isPaused: false,
        duration: 0,
        audioLevel: 0,
        error: null
      });

      // Start audio level monitoring
      monitorAudioLevel();
      
      triggerHapticFeedback('medium');
      
    } catch (error) {
      const errorMsg = 'Aufnahme konnte nicht gestartet werden';
      setRecordingState(prev => ({ ...prev, error: errorMsg }));
      onError?.(errorMsg);
      console.error('Recording start error:', error);
    }
  }, [requestPermission, recordingState.duration, onRecordingComplete, enableAutoTranscription, monitorAudioLevel, onError]);

  // Stop recording
  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && recordingState.isRecording) {
      mediaRecorderRef.current.stop();
      
      // Cleanup
      if (audioStreamRef.current) {
        audioStreamRef.current.getTracks().forEach(track => track.stop());
        audioStreamRef.current = null;
      }
      
      if (audioContextRef.current) {
        audioContextRef.current.close();
        audioContextRef.current = null;
      }
      
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
      
      setRecordingState({
        isRecording: false,
        isPaused: false,
        duration: 0,
        audioLevel: 0,
        error: null
      });
      
      triggerHapticFeedback('light');
    }
  }, [recordingState.isRecording]);

  // Pause/Resume recording
  const pauseRecording = useCallback(() => {
    if (mediaRecorderRef.current && recordingState.isRecording) {
      if (recordingState.isPaused) {
        mediaRecorderRef.current.resume();
        startTimeRef.current = Date.now() - (recordingState.duration * 1000);
        monitorAudioLevel();
      } else {
        mediaRecorderRef.current.pause();
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
        }
      }
      
      setRecordingState(prev => ({ ...prev, isPaused: !prev.isPaused }));
      triggerHapticFeedback('light');
    }
  }, [recordingState.isRecording, recordingState.isPaused, recordingState.duration, monitorAudioLevel]);

  // Simple transcription (placeholder - would integrate with speech-to-text service)
  const transcribeAudio = useCallback(async (audioBlob: Blob) => {
    try {
      // This is a placeholder for actual transcription service integration
      // In a real implementation, you would send the audio to a service like:
      // - Google Speech-to-Text
      // - Azure Speech Services
      // - AWS Transcribe
      // - OpenAI Whisper API
      
      // For now, we'll simulate transcription
      setTimeout(() => {
        const mockTranscription = "Sprachnotiz automatisch transkribiert - Integration mit Spracherkennung erforderlich";
        
        setRecordings(prev => prev.map(recording => 
          recording.blob === audioBlob 
            ? { ...recording, transcription: mockTranscription }
            : recording
        ));
        
        onTranscriptionComplete?.(mockTranscription);
        toast.info('Transkription verfügbar');
      }, 2000);
      
    } catch (error) {
      console.error('Transcription error:', error);
      toast.error('Transkription fehlgeschlagen');
    }
  }, [onTranscriptionComplete]);

  // Delete recording
  const deleteRecording = useCallback((recordingId: string) => {
    setRecordings(prev => prev.filter(r => r.id !== recordingId));
    triggerHapticFeedback('light');
    toast.success('Sprachnotiz gelöscht');
  }, []);

  // Play recording
  const playRecording = useCallback((recordingId: string) => {
    const recording = recordings.find(r => r.id === recordingId);
    if (!recording) return;

    const audio = new Audio(URL.createObjectURL(recording.blob));
    audio.play().catch(error => {
      console.error('Playback error:', error);
      toast.error('Wiedergabe fehlgeschlagen');
    });
    
    triggerHapticFeedback('light');
  }, [recordings]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (recordingState.isRecording) {
        stopRecording();
      }
    };
  }, [recordingState.isRecording, stopRecording]);

  return {
    recordingState,
    permissionStatus,
    recordings,
    startRecording,
    stopRecording,
    pauseRecording,
    deleteRecording,
    playRecording,
    transcribeAudio
  };
};
