
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, Card<PERSON><PERSON>le, CardDescription } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { CompletedVisitData } from './types';
import { useIsMobile } from '@/hooks/use-mobile';
import { MapPin, CheckCircle } from 'lucide-react';

interface CompletedVisitsTableProps {
  completedVisits: CompletedVisitData[];
  teamName?: string;
}

const CompletedVisitsTable: React.FC<CompletedVisitsTableProps> = ({ 
  completedVisits, 
  teamName = "Teams" 
}) => {
  const isMobile = useIsMobile();
  
  return (
    <Card className="glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-slide-in">
      <CardHeader className={`${isMobile ? 'p-4 pb-2' : 'p-6 pb-4'}`}>
        <CardTitle className={`flex items-center gap-3 ${isMobile ? 'text-lg' : 'text-2xl'} font-bold text-gray-800`}>
          <MapPin className={`${isMobile ? 'h-5 w-5' : 'h-6 w-6'} text-green-600`} />
          Abgeschlossene Adressen
        </CardTitle>
        <CardDescription className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-600`}>
          Erfolgreich bearbeitete Adressen von {teamName} ({completedVisits.length} Einträge)
        </CardDescription>
      </CardHeader>
      <CardContent className={`${isMobile ? 'p-4 pt-0' : 'p-6 pt-0'}`}>
        <div className="overflow-x-auto scrollbar-none">
          <Table>
            <TableHeader>
              <TableRow className="border-gray-200">
                <TableHead className={`font-semibold text-gray-700 ${isMobile ? 'text-xs' : 'text-sm'}`}>Datum</TableHead>
                <TableHead className={`font-semibold text-gray-700 ${isMobile ? 'text-xs' : 'text-sm'}`}>Adresse</TableHead>
                {!isMobile && <TableHead className={`font-semibold text-gray-700 text-sm`}>Berater</TableHead>}
                {!isMobile && <TableHead className={`font-semibold text-gray-700 text-sm`}>Mentor</TableHead>}
                <TableHead className={`font-semibold text-gray-700 ${isMobile ? 'text-xs' : 'text-sm'}`}>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {completedVisits.map((item, index) => (
                <TableRow key={index} className="hover:bg-green-50/50 transition-colors border-gray-100" style={{ animationDelay: `${index * 0.05}s` }}>
                  <TableCell className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-600`}>
                    {item.visitDate}
                  </TableCell>
                  <TableCell className={`font-medium text-gray-800 ${isMobile ? 'text-xs max-w-[120px]' : 'text-sm max-w-[200px]'} truncate`}>
                    {item.address}
                  </TableCell>
                  {!isMobile && <TableCell className="text-sm text-gray-700">{item.beraterName}</TableCell>}
                  {!isMobile && <TableCell className="text-sm text-gray-700">{item.mentorName}</TableCell>}
                  <TableCell>
                    <Badge className="bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-md">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      {isMobile ? "✓" : "Abgeschlossen"}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
              {completedVisits.length === 0 && (
                <TableRow>
                  <TableCell colSpan={isMobile ? 3 : 5} className={`text-center py-8 text-gray-500 ${isMobile ? 'text-sm' : 'text-base'}`}>
                    Keine abgeschlossenen Adressen vorhanden
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};

export default CompletedVisitsTable;
