
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { useAuth } from '@/context/auth';
import { User, Mail, Lock, ArrowRight, ArrowLeft, CheckCircle, AlertCircle } from 'lucide-react';
import <PERSON><PERSON>ield from './FormField';
import PasswordStrengthIndicator from './PasswordStrengthIndicator';
import { RegisterFormState, ValidationError } from '@/context/auth/types';
import {
  validateName,
  validateEmail,
  validatePassword,
  validatePasswordConfirmation,
  createFormValidator
} from '@/utils/validation';
import { cn } from '@/lib/utils';

interface RegisterFormProps {
  name: string;
  setName: (name: string) => void;
  email: string;
  setEmail: (email: string) => void;
  password: string;
  setPassword: (password: string) => void;
  setActiveTab: (tab: "login" | "register") => void;
}

const RegisterForm: React.FC<RegisterFormProps> = ({
  name,
  setName,
  email,
  setEmail,
  password,
  setPassword,
  setActiveTab
}) => {
  const { register, isLoading, error, clearError } = useAuth();
  const [confirmPassword, setConfirmPassword] = useState('');
  const [formState, setFormState] = useState<RegisterFormState>({
    name,
    email,
    password,
    confirmPassword: '',
    isSubmitting: false,
    currentStep: 1,
    maxSteps: 2,
  });

  // Form validator
  const validator = createFormValidator();

  // Password validation
  const passwordValidation = validatePassword(formState.password);

  // Sync props with internal state
  useEffect(() => {
    setFormState(prev => ({
      ...prev,
      name,
      email,
      password,
    }));
  }, [name, email, password]);

  // Handle field changes with validation
  const handleNameChange = (value: string) => {
    setName(value);
    setFormState(prev => ({ ...prev, name: value }));
    clearError();

    if (validator.getFieldError('name')) {
      validator.clearError('name');
    }
  };

  const handleEmailChange = (value: string) => {
    setEmail(value);
    setFormState(prev => ({ ...prev, email: value }));
    clearError();

    if (validator.getFieldError('email')) {
      validator.clearError('email');
    }
  };

  const handlePasswordChange = (value: string) => {
    setPassword(value);
    setFormState(prev => ({ ...prev, password: value }));
    clearError();

    if (validator.getFieldError('password')) {
      validator.clearError('password');
    }
  };

  const handleConfirmPasswordChange = (value: string) => {
    setConfirmPassword(value);
    setFormState(prev => ({ ...prev, confirmPassword: value }));
    clearError();

    if (validator.getFieldError('confirmPassword')) {
      validator.clearError('confirmPassword');
    }
  };

  // Handle field blur for validation
  const handleFieldBlur = (field: string) => {
    validator.setTouched(field);

    let error: ValidationError | null = null;

    switch (field) {
      case 'name':
        error = validateName(formState.name);
        break;
      case 'email':
        error = validateEmail(formState.email);
        break;
      case 'password':
        if (passwordValidation.strength === 'weak') {
          error = {
            field: 'password',
            message: 'Passwort ist zu schwach. Bitte folgen Sie den Empfehlungen.'
          };
        }
        break;
      case 'confirmPassword':
        error = validatePasswordConfirmation(formState.password, formState.confirmPassword);
        break;
    }

    if (error) {
      validator.setError(error);
    } else {
      validator.clearError(field);
    }
  };

  // Step navigation
  const canProceedToStep2 = () => {
    const nameError = validateName(formState.name);
    const emailError = validateEmail(formState.email);
    return !nameError && !emailError && formState.name.trim() && formState.email.trim();
  };

  const canSubmit = () => {
    const nameError = validateName(formState.name);
    const emailError = validateEmail(formState.email);
    const passwordWeak = passwordValidation.strength === 'weak';
    const confirmError = validatePasswordConfirmation(formState.password, formState.confirmPassword);

    return !nameError && !emailError && !passwordWeak && !confirmError &&
           formState.name.trim() && formState.email.trim() &&
           formState.password.trim() && formState.confirmPassword.trim();
  };

  const nextStep = () => {
    if (canProceedToStep2()) {
      setFormState(prev => ({ ...prev, currentStep: 2 }));
    }
  };

  const prevStep = () => {
    setFormState(prev => ({ ...prev, currentStep: 1 }));
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!canSubmit()) {
      return;
    }

    setFormState(prev => ({ ...prev, isSubmitting: true }));

    try {
      const result = await register({
        name: formState.name.trim(),
        email: formState.email.toLowerCase().trim(),
        password: formState.password,
        confirmPassword: formState.confirmPassword,
      });

      if (result.success) {
        // Registration successful, user is automatically logged in
        // Navigation will be handled by the parent component
      }
      // Error handling is now managed by the auth provider
    } catch (error) {
      console.error('Registration error:', error);
    } finally {
      setFormState(prev => ({ ...prev, isSubmitting: false }));
    }
  };

  return (
    <div className="space-y-6">
      {/* Progress indicator */}
      <div className="flex items-center justify-center space-x-4">
        <div className="flex items-center">
          <div className={cn(
            "w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold transition-colors",
            formState.currentStep >= 1 ? "bg-red-500 text-white" : "bg-gray-200 text-gray-500"
          )}>
            {formState.currentStep > 1 ? <CheckCircle className="w-4 h-4" /> : "1"}
          </div>
          <span className="ml-2 text-sm font-medium text-gray-700">Persönliche Daten</span>
        </div>

        <div className={cn(
          "w-8 h-0.5 transition-colors",
          formState.currentStep >= 2 ? "bg-red-500" : "bg-gray-200"
        )} />

        <div className="flex items-center">
          <div className={cn(
            "w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold transition-colors",
            formState.currentStep >= 2 ? "bg-red-500 text-white" : "bg-gray-200 text-gray-500"
          )}>
            2
          </div>
          <span className="ml-2 text-sm font-medium text-gray-700">Passwort</span>
        </div>
      </div>

      {/* Global error message */}
      {error && error.type !== 'validation' && (
        <div className="flex items-center space-x-2 p-4 bg-red-50 border border-red-200 rounded-xl">
          <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0" />
          <span className="text-sm font-medium text-red-700">{error.message}</span>
        </div>
      )}

      <form onSubmit={handleRegister} className="space-y-6">
        {/* Step 1: Personal Information */}
        {formState.currentStep === 1 && (
          <div className="space-y-4 animate-fade-in">
            <FormField
              id="register-name"
              label="Vollständiger Name"
              type="text"
              value={formState.name}
              onChange={handleNameChange}
              onBlur={() => handleFieldBlur('name')}
              placeholder="Max Mustermann"
              error={validator.getFieldError('name') || (error?.field === 'name' ? error : null)}
              required
              icon={<User className="h-5 w-5" />}
              autoComplete="name"
              maxLength={50}
              data-testid="register-name"
            />

            <FormField
              id="register-email"
              label="E-Mail-Adresse"
              type="email"
              value={formState.email}
              onChange={handleEmailChange}
              onBlur={() => handleFieldBlur('email')}
              placeholder="<EMAIL>"
              error={validator.getFieldError('email') || (error?.field === 'email' ? error : null)}
              required
              icon={<Mail className="h-5 w-5" />}
              autoComplete="email"
              data-testid="register-email"
            />

            <Button
              type="button"
              onClick={nextStep}
              disabled={!canProceedToStep2()}
              className={cn(
                "w-full h-14 text-lg font-semibold rounded-xl transition-all duration-200",
                "bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800",
                "hover:scale-105 shadow-lg hover:shadow-xl touch-manipulation",
                "disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100",
                "min-h-[44px]"
              )}
              data-testid="register-next"
            >
              <span className="flex items-center">
                Weiter
                <ArrowRight className="ml-2 h-5 w-5" />
              </span>
            </Button>
          </div>
        )}

        {/* Step 2: Password */}
        {formState.currentStep === 2 && (
          <div className="space-y-4 animate-fade-in">
            <FormField
              id="register-password"
              label="Passwort"
              type="password"
              value={formState.password}
              onChange={handlePasswordChange}
              onBlur={() => handleFieldBlur('password')}
              placeholder="••••••••"
              error={validator.getFieldError('password') || (error?.field === 'password' ? error : null)}
              required
              icon={<Lock className="h-5 w-5" />}
              showPasswordToggle
              autoComplete="new-password"
              data-testid="register-password"
            />

            {/* Password strength indicator */}
            {formState.password && (
              <PasswordStrengthIndicator
                validation={passwordValidation}
                className="mt-3"
              />
            )}

            <FormField
              id="register-confirm-password"
              label="Passwort bestätigen"
              type="password"
              value={formState.confirmPassword}
              onChange={handleConfirmPasswordChange}
              onBlur={() => handleFieldBlur('confirmPassword')}
              placeholder="••••••••"
              error={validator.getFieldError('confirmPassword') || (error?.field === 'confirmPassword' ? error : null)}
              required
              icon={<Lock className="h-5 w-5" />}
              showPasswordToggle
              autoComplete="new-password"
              data-testid="register-confirm-password"
            />

            {/* Action buttons */}
            <div className="flex space-x-3">
              <Button
                type="button"
                onClick={prevStep}
                variant="outline"
                className={cn(
                  "flex-1 h-14 text-lg font-semibold rounded-xl transition-all duration-200",
                  "border-2 border-gray-300 hover:border-red-500 hover:text-red-600",
                  "touch-manipulation min-h-[44px]"
                )}
                data-testid="register-back"
              >
                <ArrowLeft className="mr-2 h-5 w-5" />
                Zurück
              </Button>

              <Button
                type="submit"
                disabled={!canSubmit() || isLoading || formState.isSubmitting}
                className={cn(
                  "flex-1 h-14 text-lg font-semibold rounded-xl transition-all duration-200",
                  "bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800",
                  "hover:scale-105 shadow-lg hover:shadow-xl touch-manipulation",
                  "disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100",
                  "min-h-[44px]"
                )}
                data-testid="register-submit"
              >
                {(isLoading || formState.isSubmitting) ? (
                  <div className="flex items-center">
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-3"></div>
                    Registrierung...
                  </div>
                ) : (
                  'Registrieren'
                )}
              </Button>
            </div>
          </div>
        )}
      </form>
    </div>
  );
};

export default RegisterForm;
