
import React from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { useAuth } from '@/context/auth';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { useData } from '@/context/data';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { useIsMobile } from '@/hooks/use-mobile';
import { Users, TrendingUp, BarChart3 } from 'lucide-react';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';

const BeraterStatisticsPage: React.FC = () => {
  const { user, users } = useAuth();
  const { visits, products } = useData();
  const isMobile = useIsMobile();

  if (!user || user.role !== 'mentor') {
    return (
      <MainLayout title="Berater Statistiken">
        <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 flex items-center justify-center p-4">
          <Card className="glass-card rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm p-6">
            <CardContent className="text-center">
              <p className="text-gray-600">Sie haben keine Berechtigung, diese Seite anzuzeigen.</p>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    );
  }

  // Get all beraters assigned to this mentor
  const assignedBeraters = users.filter(u => u.mentorId === user.id);

  // Calculate summary statistics
  const totalVisits = assignedBeraters.reduce((sum, berater) => {
    return sum + visits.filter(v => v.userId === berater.id).length;
  }, 0);

  const totalSales = assignedBeraters.reduce((sum, berater) => {
    return sum + products.filter(p => p.userId === berater.id).length;
  }, 0);

  // Prepare data for charts
  const visitData = assignedBeraters.map(berater => {
    const beraterVisits = visits.filter(v => v.userId === berater.id).length;
    return {
      name: berater.name,
      visits: beraterVisits
    };
  });

  const salesData = assignedBeraters.map(berater => {
    const beraterSales = products.filter(p => p.userId === berater.id).length;
    return {
      name: berater.name,
      sales: beraterSales
    };
  });

  const statsCards = [
    {
      title: "Team Berater",
      value: assignedBeraters.length,
      icon: Users,
      color: "from-blue-500 to-blue-600",
      textColor: "text-blue-600"
    },
    {
      title: "Gesamt Besuche", 
      value: totalVisits,
      icon: BarChart3,
      color: "from-purple-500 to-purple-600",
      textColor: "text-purple-600"
    },
    {
      title: "Gesamt Verkäufe",
      value: totalSales,
      icon: TrendingUp,
      color: "from-green-500 to-green-600", 
      textColor: "text-green-600"
    }
  ];

  return (
    <MainLayout title="Berater Statistiken">
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 p-4 md:p-6">
        <div className="max-w-7xl mx-auto space-y-6 md:space-y-8">
          {/* Header Section */}
          <div className="text-center md:text-left space-y-2 md:space-y-4 animate-fade-in">
            <h1 className={`font-bold text-gray-800 ${isMobile ? 'text-2xl' : 'text-4xl'}`}>
              Berater Statistiken
            </h1>
            <p className={`text-gray-600 ${isMobile ? 'text-sm' : 'text-lg'}`}>
              Statistiken für {assignedBeraters.length} zugewiesene Berater
            </p>
            <p className={`text-gray-500 ${isMobile ? 'text-xs' : 'text-sm'}`}>
              {format(new Date(), "'Stand:' d. MMMM yyyy", { locale: de })}
            </p>
          </div>

          {/* Statistics Cards */}
          <div className={`grid gap-4 md:gap-6 ${isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-3'}`}>
            {statsCards.map((stat, index) => (
              <Card key={stat.title} className={`glass-card hover-lift ${isMobile ? 'p-4' : 'p-6'} rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-scale-in`} style={{ animationDelay: `${index * 0.1}s` }}>
                <CardContent className="p-0">
                  <div className="flex items-center justify-between">
                    <div className="space-y-2">
                      <p className={`font-medium text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                        {stat.title}
                      </p>
                      <p className={`font-bold ${stat.textColor} ${isMobile ? 'text-2xl' : 'text-4xl'}`}>
                        {stat.value}
                      </p>
                    </div>
                    <div className={`rounded-2xl bg-gradient-to-br ${stat.color} p-3 shadow-lg`}>
                      <stat.icon className={`${isMobile ? 'h-6 w-6' : 'h-8 w-8'} text-white`} />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          
          {/* Charts Section */}
          <div className="space-y-6 md:space-y-8">
            {/* Visits Chart */}
            <Card className="glass-card hover-lift rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-slide-in">
              <CardHeader className={`${isMobile ? 'p-4 pb-2' : 'p-6 pb-4'}`}>
                <CardTitle className={`flex items-center gap-3 ${isMobile ? 'text-lg' : 'text-2xl'} font-bold text-gray-800`}>
                  <BarChart3 className={`${isMobile ? 'h-5 w-5' : 'h-6 w-6'} text-purple-600`} />
                  Besuche pro Berater
                </CardTitle>
                <CardDescription className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-600`}>
                  Verteilung der Besuche auf das Team
                </CardDescription>
              </CardHeader>
              <CardContent className={`${isMobile ? 'p-4 pt-0' : 'p-6 pt-0'}`}>
                <div style={{ height: isMobile ? '250px' : '400px' }} className="w-full">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={visitData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                      <XAxis 
                        dataKey="name" 
                        tick={{ fontSize: isMobile ? 12 : 14 }}
                        stroke="#64748b"
                      />
                      <YAxis 
                        tick={{ fontSize: isMobile ? 12 : 14 }}
                        stroke="#64748b"
                      />
                      <Tooltip 
                        contentStyle={{
                          backgroundColor: 'rgba(255, 255, 255, 0.95)',
                          border: 'none',
                          borderRadius: '12px',
                          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
                        }}
                      />
                      <Legend />
                      <Bar 
                        dataKey="visits" 
                        name="Anzahl Besuche" 
                        fill="#8b5cf6" 
                        radius={[4, 4, 0, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            {/* Sales Chart */}
            <Card className="glass-card hover-lift rounded-3xl border-0 shadow-xl bg-white/90 backdrop-blur-sm animate-slide-in">
              <CardHeader className={`${isMobile ? 'p-4 pb-2' : 'p-6 pb-4'}`}>
                <CardTitle className={`flex items-center gap-3 ${isMobile ? 'text-lg' : 'text-2xl'} font-bold text-gray-800`}>
                  <TrendingUp className={`${isMobile ? 'h-5 w-5' : 'h-6 w-6'} text-green-600`} />
                  Verkäufe pro Berater
                </CardTitle>
                <CardDescription className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-600`}>
                  Verkaufsleistung des Teams
                </CardDescription>
              </CardHeader>
              <CardContent className={`${isMobile ? 'p-4 pt-0' : 'p-6 pt-0'}`}>
                <div style={{ height: isMobile ? '250px' : '400px' }} className="w-full">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={salesData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
                      <XAxis 
                        dataKey="name" 
                        tick={{ fontSize: isMobile ? 12 : 14 }}
                        stroke="#64748b"
                      />
                      <YAxis 
                        tick={{ fontSize: isMobile ? 12 : 14 }}
                        stroke="#64748b"
                      />
                      <Tooltip 
                        contentStyle={{
                          backgroundColor: 'rgba(255, 255, 255, 0.95)',
                          border: 'none',
                          borderRadius: '12px',
                          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
                        }}
                      />
                      <Legend />
                      <Bar 
                        dataKey="sales" 
                        name="Anzahl Verkäufe" 
                        fill="#10b981" 
                        radius={[4, 4, 0, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default BeraterStatisticsPage;
