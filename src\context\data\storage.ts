
import { Address, House, Visit, Door, ProductEntry } from "@/types";
import { mockAddresses, mockHouses } from "./mockData";

export const loadFromStorage = () => {
  const storedAddresses = localStorage.getItem("addresses");
  const storedHouses = localStorage.getItem("houses");
  const storedVisits = localStorage.getItem("visits");
  const storedDoors = localStorage.getItem("doors");
  const storedProducts = localStorage.getItem("products");

  return {
    addresses: storedAddresses ? JSON.parse(storedAddresses) : mockAddresses,
    houses: storedHouses ? JSON.parse(storedHouses) : mockHouses,
    visits: storedVisits ? JSON.parse(storedVisits) : [],
    doors: storedDoors ? JSON.parse(storedDoors) : [],
    products: storedProducts ? JSON.parse(storedProducts) : []
  };
};

export const saveToStorage = (
  key: "addresses" | "houses" | "visits" | "doors" | "products", 
  data: Address[] | House[] | Visit[] | Door[] | ProductEntry[]
) => {
  localStorage.setItem(key, JSON.stringify(data));
};
