import { Visit, Address, House, VisitPattern, VisitRecommendation } from '@/types';
import { v4 as uuidv4 } from 'uuid';

// Enhanced pattern types for advanced analysis
export interface SeasonalPattern {
  season: 'spring' | 'summer' | 'autumn' | 'winter';
  successRate: number;
  preferredTimes: number[];
  confidence: number;
}

export interface WeatherPattern {
  weatherType: 'sunny' | 'rainy' | 'cloudy' | 'snowy';
  successRate: number;
  sampleSize: number;
}

export interface RouteOptimization {
  clusterId: string;
  addresses: string[];
  optimalSequence: string[];
  estimatedTime: number;
  confidence: number;
}

export interface PredictiveWindow {
  startTime: string; // ISO string
  endTime: string;
  probability: number;
  factors: string[];
}

/**
 * Advanced pattern recognition with 15-minute granularity
 */
export const analyzeDetailedTimePatterns = (
  addressId: string,
  visits: Visit[],
  houses: House[]
): VisitPattern[] => {
  const addressHouses = houses.filter(h => h.addressId === addressId);
  const addressVisits = visits.filter(v => 
    addressHouses.some(h => h.id === v.houseId)
  );

  if (addressVisits.length < 3) return [];

  const patterns: VisitPattern[] = [];
  
  // 15-minute granularity analysis
  const timeSlots = new Map<string, { success: number; total: number }>();
  
  addressVisits.forEach(visit => {
    const date = new Date(visit.timestamp);
    const hour = date.getHours();
    const minute = date.getMinutes();
    const quarterHour = Math.floor(minute / 15) * 15;
    const timeSlot = `${hour.toString().padStart(2, '0')}:${quarterHour.toString().padStart(2, '0')}`;
    
    if (!timeSlots.has(timeSlot)) {
      timeSlots.set(timeSlot, { success: 0, total: 0 });
    }
    
    const slot = timeSlots.get(timeSlot)!;
    slot.total++;
    if (visit.status !== 'N/A') {
      slot.success++;
    }
  });

  // Find optimal time slots
  const optimalSlots: string[] = [];
  const avoidSlots: string[] = [];
  
  timeSlots.forEach((data, timeSlot) => {
    if (data.total >= 2) {
      const successRate = data.success / data.total;
      if (successRate >= 0.7) {
        optimalSlots.push(timeSlot);
      } else if (successRate <= 0.3) {
        avoidSlots.push(timeSlot);
      }
    }
  });

  if (optimalSlots.length > 0 || avoidSlots.length > 0) {
    patterns.push({
      id: uuidv4(),
      addressId,
      patternType: 'time_of_day',
      patternData: {
        successfulTimes: optimalSlots.map(slot => {
          const [hour] = slot.split(':');
          return parseInt(hour);
        }),
        failedTimes: avoidSlots.map(slot => {
          const [hour] = slot.split(':');
          return parseInt(hour);
        }),
        confidence: Math.min(addressVisits.length / 10, 1),
        dataPoints: addressVisits.length,
        detailedSlots: Array.from(timeSlots.entries()).map(([slot, data]) => ({
          timeSlot: slot,
          successRate: data.success / data.total,
          sampleSize: data.total
        }))
      },
      lastUpdated: new Date().toISOString(),
      createdAt: new Date().toISOString()
    });
  }

  return patterns;
};

/**
 * Seasonal pattern analysis
 */
export const analyzeSeasonalPatterns = (
  addressId: string,
  visits: Visit[],
  houses: House[]
): SeasonalPattern[] => {
  const addressHouses = houses.filter(h => h.addressId === addressId);
  const addressVisits = visits.filter(v => 
    addressHouses.some(h => h.id === v.houseId)
  );

  const seasonalData = new Map<string, { success: number; total: number; times: number[] }>();
  
  addressVisits.forEach(visit => {
    const date = new Date(visit.timestamp);
    const month = date.getMonth();
    let season: string;
    
    if (month >= 2 && month <= 4) season = 'spring';
    else if (month >= 5 && month <= 7) season = 'summer';
    else if (month >= 8 && month <= 10) season = 'autumn';
    else season = 'winter';
    
    if (!seasonalData.has(season)) {
      seasonalData.set(season, { success: 0, total: 0, times: [] });
    }
    
    const data = seasonalData.get(season)!;
    data.total++;
    data.times.push(date.getHours());
    
    if (visit.status !== 'N/A') {
      data.success++;
    }
  });

  return Array.from(seasonalData.entries())
    .filter(([_, data]) => data.total >= 2)
    .map(([season, data]) => ({
      season: season as 'spring' | 'summer' | 'autumn' | 'winter',
      successRate: data.success / data.total,
      preferredTimes: [...new Set(data.times)].sort((a, b) => a - b),
      confidence: Math.min(data.total / 5, 1)
    }));
};

/**
 * Smart address clustering for route optimization
 */
export const clusterAddresses = (
  addresses: Address[],
  visits: Visit[],
  houses: House[]
): RouteOptimization[] => {
  const clusters: RouteOptimization[] = [];
  const processedAddresses = new Set<string>();
  
  addresses.forEach(address => {
    if (processedAddresses.has(address.id)) return;
    
    // Find nearby addresses (simplified clustering by postal code and street)
    const nearbyAddresses = addresses.filter(addr => 
      addr.zipCode === address.zipCode && 
      addr.street === address.street &&
      !processedAddresses.has(addr.id)
    );
    
    if (nearbyAddresses.length >= 2) {
      // Calculate optimal sequence based on house numbers
      const sortedAddresses = nearbyAddresses.sort((a, b) => {
        const houseA = houses.find(h => h.addressId === a.id);
        const houseB = houses.find(h => h.addressId === b.id);
        
        if (!houseA || !houseB) return 0;
        
        const numA = parseInt(houseA.houseNumber) || 0;
        const numB = parseInt(houseB.houseNumber) || 0;
        
        return numA - numB;
      });
      
      clusters.push({
        clusterId: uuidv4(),
        addresses: sortedAddresses.map(a => a.id),
        optimalSequence: sortedAddresses.map(a => a.id),
        estimatedTime: sortedAddresses.length * 15, // 15 minutes per address
        confidence: 0.8
      });
      
      sortedAddresses.forEach(addr => processedAddresses.add(addr.id));
    }
  });
  
  return clusters;
};

/**
 * Predictive analytics for optimal visit windows
 */
export const predictOptimalVisitWindows = (
  addressId: string,
  visits: Visit[],
  houses: House[],
  currentDate: Date = new Date()
): PredictiveWindow[] => {
  const addressHouses = houses.filter(h => h.addressId === addressId);
  const addressVisits = visits.filter(v => 
    addressHouses.some(h => h.id === v.houseId)
  );

  if (addressVisits.length < 3) return [];

  const windows: PredictiveWindow[] = [];
  const dayOfWeek = currentDate.getDay();
  const hour = currentDate.getHours();
  
  // Analyze historical success patterns for similar time periods
  const similarVisits = addressVisits.filter(visit => {
    const visitDate = new Date(visit.timestamp);
    return visitDate.getDay() === dayOfWeek;
  });
  
  if (similarVisits.length >= 2) {
    const successfulVisits = similarVisits.filter(v => v.status !== 'N/A');
    const successRate = successfulVisits.length / similarVisits.length;
    
    if (successRate > 0.5) {
      // Find the most successful time windows
      const timeWindows = new Map<string, number>();
      
      successfulVisits.forEach(visit => {
        const visitHour = new Date(visit.timestamp).getHours();
        const windowStart = Math.floor(visitHour / 2) * 2; // 2-hour windows
        const windowKey = `${windowStart}-${windowStart + 2}`;
        
        timeWindows.set(windowKey, (timeWindows.get(windowKey) || 0) + 1);
      });
      
      // Create prediction windows for today
      Array.from(timeWindows.entries())
        .sort(([, a], [, b]) => b - a)
        .slice(0, 3) // Top 3 windows
        .forEach(([window, count]) => {
          const [startHour] = window.split('-').map(Number);
          const probability = count / successfulVisits.length;
          
          if (probability > 0.3) {
            const startTime = new Date(currentDate);
            startTime.setHours(startHour, 0, 0, 0);
            
            const endTime = new Date(startTime);
            endTime.setHours(startHour + 2);
            
            windows.push({
              startTime: startTime.toISOString(),
              endTime: endTime.toISOString(),
              probability,
              factors: [
                `Historical success rate: ${(successRate * 100).toFixed(0)}%`,
                `Based on ${similarVisits.length} similar visits`,
                `${count} successful visits in this time window`
              ]
            });
          }
        });
    }
  }
  
  return windows.sort((a, b) => b.probability - a.probability);
};

/**
 * Machine learning-inspired recommendation improvement
 */
export const improveRecommendationAccuracy = (
  recommendations: VisitRecommendation[],
  actualOutcomes: { recommendationId: string; wasSuccessful: boolean; timestamp: string }[]
): VisitRecommendation[] => {
  return recommendations.map(rec => {
    const outcomes = actualOutcomes.filter(o => o.recommendationId === rec.id);
    
    if (outcomes.length > 0) {
      const successCount = outcomes.filter(o => o.wasSuccessful).length;
      const actualSuccessRate = successCount / outcomes.length;
      
      // Adjust confidence based on actual performance
      const confidenceAdjustment = actualSuccessRate - 0.5; // Baseline 50%
      const newConfidence = Math.max(0.1, Math.min(1.0, rec.confidence + confidenceAdjustment * 0.2));
      
      return {
        ...rec,
        confidence: newConfidence,
        lastUpdated: new Date().toISOString()
      };
    }
    
    return rec;
  });
};

/**
 * Holiday and special event detection
 */
export const detectSpecialPeriods = (date: Date): { type: string; impact: 'low' | 'medium' | 'high' } | null => {
  const month = date.getMonth();
  const day = date.getDate();
  const dayOfWeek = date.getDay();
  
  // German holidays and special periods
  const specialPeriods = [
    { start: { month: 11, day: 20 }, end: { month: 0, day: 6 }, type: 'Christmas/New Year', impact: 'high' as const },
    { start: { month: 6, day: 15 }, end: { month: 7, day: 31 }, type: 'Summer Vacation', impact: 'medium' as const },
    { start: { month: 9, day: 15 }, end: { month: 10, day: 5 }, type: 'Autumn Break', impact: 'low' as const }
  ];
  
  for (const period of specialPeriods) {
    const isInPeriod = (month === period.start.month && day >= period.start.day) ||
                      (month === period.end.month && day <= period.end.day) ||
                      (period.start.month > period.end.month && 
                       (month > period.start.month || month < period.end.month));
    
    if (isInPeriod) {
      return { type: period.type, impact: period.impact };
    }
  }
  
  // Weekend detection
  if (dayOfWeek === 0 || dayOfWeek === 6) {
    return { type: 'Weekend', impact: 'medium' };
  }
  
  return null;
};
