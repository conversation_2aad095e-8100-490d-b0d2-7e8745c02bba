import { useState, useEffect, useCallback, useRef } from 'react';
import { useData } from '@/context/data';
import { triggerHapticFeedback } from './useSwipeGestures';
import { toast } from 'sonner';
import { Visit, Door, ProductEntry, Address, House } from '@/types';

interface OfflineAction {
  id: string;
  type: 'add_visit' | 'update_visit' | 'add_door' | 'update_door' | 'add_product' | 'add_address' | 'add_house';
  data: any;
  timestamp: string;
  retryCount: number;
  maxRetries: number;
}

interface SyncStatus {
  isOnline: boolean;
  isSyncing: boolean;
  pendingActions: number;
  lastSyncTime: string | null;
  syncErrors: string[];
}

export const useOfflineSync = () => {
  const dataContext = useData();
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    isOnline: navigator.onLine,
    isSyncing: false,
    pendingActions: 0,
    lastSyncTime: null,
    syncErrors: []
  });

  const [offlineActions, setOfflineActions] = useState<OfflineAction[]>([]);
  const syncIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Load offline actions from localStorage
  useEffect(() => {
    const savedActions = localStorage.getItem('offlineActions');
    if (savedActions) {
      try {
        const actions = JSON.parse(savedActions);
        setOfflineActions(actions);
        setSyncStatus(prev => ({ ...prev, pendingActions: actions.length }));
      } catch (error) {
        console.error('Error loading offline actions:', error);
      }
    }

    const lastSync = localStorage.getItem('lastSyncTime');
    if (lastSync) {
      setSyncStatus(prev => ({ ...prev, lastSyncTime: lastSync }));
    }
  }, []);

  // Save offline actions to localStorage
  useEffect(() => {
    localStorage.setItem('offlineActions', JSON.stringify(offlineActions));
    setSyncStatus(prev => ({ ...prev, pendingActions: offlineActions.length }));
  }, [offlineActions]);

  // Online/Offline event listeners
  useEffect(() => {
    const handleOnline = () => {
      setSyncStatus(prev => ({ ...prev, isOnline: true, syncErrors: [] }));
      triggerHapticFeedback('success');
      toast.success('Verbindung wiederhergestellt - Synchronisierung startet...');
      
      // Start sync after a short delay
      setTimeout(() => {
        syncOfflineActions();
      }, 1000);
    };

    const handleOffline = () => {
      setSyncStatus(prev => ({ ...prev, isOnline: false }));
      triggerHapticFeedback('warning');
      toast.warning('Offline-Modus aktiviert - Änderungen werden lokal gespeichert');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Add action to offline queue
  const addOfflineAction = useCallback((
    type: OfflineAction['type'],
    data: any,
    maxRetries: number = 3
  ) => {
    const action: OfflineAction = {
      id: `${type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      data,
      timestamp: new Date().toISOString(),
      retryCount: 0,
      maxRetries
    };

    setOfflineActions(prev => [...prev, action]);

    // If online, try to sync immediately
    if (syncStatus.isOnline && !syncStatus.isSyncing) {
      setTimeout(() => syncOfflineActions(), 100);
    }
  }, [syncStatus.isOnline, syncStatus.isSyncing]);

  // Execute a single offline action
  const executeAction = useCallback(async (action: OfflineAction): Promise<boolean> => {
    try {
      switch (action.type) {
        case 'add_visit':
          dataContext.addVisit(action.data);
          break;
        case 'update_visit':
          dataContext.updateVisitStatus(
            action.data.visitId,
            action.data.status,
            action.data.appointmentDate,
            action.data.appointmentTime
          );
          break;
        case 'add_door':
          dataContext.addDoor(action.data);
          break;
        case 'update_door':
          dataContext.updateDoorStatus(
            action.data.doorId,
            action.data.status,
            action.data.appointmentDate,
            action.data.appointmentTime
          );
          break;
        case 'add_product':
          dataContext.addProduct(action.data);
          break;
        case 'add_address':
          dataContext.addAddress(action.data);
          break;
        case 'add_house':
          dataContext.addHouse(action.data);
          break;
        default:
          console.warn('Unknown action type:', action.type);
          return false;
      }
      return true;
    } catch (error) {
      console.error('Error executing action:', error);
      return false;
    }
  }, [dataContext]);

  // Sync all offline actions
  const syncOfflineActions = useCallback(async () => {
    if (!syncStatus.isOnline || syncStatus.isSyncing || offlineActions.length === 0) {
      return;
    }

    setSyncStatus(prev => ({ ...prev, isSyncing: true, syncErrors: [] }));

    const successfulActions: string[] = [];
    const failedActions: OfflineAction[] = [];
    const errors: string[] = [];

    for (const action of offlineActions) {
      try {
        const success = await executeAction(action);
        
        if (success) {
          successfulActions.push(action.id);
        } else {
          // Increment retry count
          const updatedAction = {
            ...action,
            retryCount: action.retryCount + 1
          };

          if (updatedAction.retryCount < updatedAction.maxRetries) {
            failedActions.push(updatedAction);
          } else {
            errors.push(`Aktion ${action.type} nach ${action.maxRetries} Versuchen fehlgeschlagen`);
          }
        }
      } catch (error) {
        console.error('Sync error for action:', action, error);
        
        const updatedAction = {
          ...action,
          retryCount: action.retryCount + 1
        };

        if (updatedAction.retryCount < updatedAction.maxRetries) {
          failedActions.push(updatedAction);
        } else {
          errors.push(`Aktion ${action.type}: ${error instanceof Error ? error.message : 'Unbekannter Fehler'}`);
        }
      }

      // Small delay between actions to prevent overwhelming
      await new Promise(resolve => setTimeout(resolve, 50));
    }

    // Update offline actions (remove successful, keep failed for retry)
    setOfflineActions(failedActions);

    // Update sync status
    const lastSyncTime = new Date().toISOString();
    localStorage.setItem('lastSyncTime', lastSyncTime);

    setSyncStatus(prev => ({
      ...prev,
      isSyncing: false,
      lastSyncTime,
      syncErrors: errors
    }));

    // Show sync results
    if (successfulActions.length > 0) {
      triggerHapticFeedback('success');
      toast.success(`${successfulActions.length} Änderung(en) erfolgreich synchronisiert`);
    }

    if (errors.length > 0) {
      triggerHapticFeedback('error');
      toast.error(`${errors.length} Synchronisierungsfehler aufgetreten`);
    }

    // Schedule retry for failed actions
    if (failedActions.length > 0) {
      retryTimeoutRef.current = setTimeout(() => {
        syncOfflineActions();
      }, 30000); // Retry after 30 seconds
    }
  }, [syncStatus.isOnline, syncStatus.isSyncing, offlineActions, executeAction]);

  // Periodic sync when online
  useEffect(() => {
    if (syncStatus.isOnline && offlineActions.length > 0) {
      syncIntervalRef.current = setInterval(() => {
        syncOfflineActions();
      }, 60000); // Sync every minute
    } else {
      if (syncIntervalRef.current) {
        clearInterval(syncIntervalRef.current);
        syncIntervalRef.current = null;
      }
    }

    return () => {
      if (syncIntervalRef.current) {
        clearInterval(syncIntervalRef.current);
      }
    };
  }, [syncStatus.isOnline, offlineActions.length, syncOfflineActions]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (syncIntervalRef.current) {
        clearInterval(syncIntervalRef.current);
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, []);

  // Offline-aware data operations
  const offlineAddVisit = useCallback((visitData: Omit<Visit, "id" | "userId">) => {
    if (syncStatus.isOnline) {
      return dataContext.addVisit(visitData);
    } else {
      // Generate temporary ID for offline use
      const tempId = `temp-visit-${Date.now()}`;
      const visit = { ...visitData, id: tempId, userId: 'offline-user' };
      
      addOfflineAction('add_visit', visitData);
      triggerHapticFeedback('light');
      toast.info('Besuch offline gespeichert - wird bei Verbindung synchronisiert');
      
      return visit as Visit;
    }
  }, [syncStatus.isOnline, dataContext, addOfflineAction]);

  const offlineUpdateVisitStatus = useCallback((
    visitId: string,
    status: any,
    appointmentDate?: string,
    appointmentTime?: string
  ) => {
    if (syncStatus.isOnline) {
      dataContext.updateVisitStatus(visitId, status, appointmentDate, appointmentTime);
    } else {
      addOfflineAction('update_visit', {
        visitId,
        status,
        appointmentDate,
        appointmentTime
      });
      triggerHapticFeedback('light');
      toast.info('Status offline aktualisiert - wird bei Verbindung synchronisiert');
    }
  }, [syncStatus.isOnline, dataContext, addOfflineAction]);

  const offlineAddDoor = useCallback((doorData: Omit<Door, "id">) => {
    if (syncStatus.isOnline) {
      return dataContext.addDoor(doorData);
    } else {
      const tempId = `temp-door-${Date.now()}`;
      const door = { ...doorData, id: tempId };
      
      addOfflineAction('add_door', doorData);
      triggerHapticFeedback('light');
      toast.info('Tür offline hinzugefügt - wird bei Verbindung synchronisiert');
      
      return door as Door;
    }
  }, [syncStatus.isOnline, dataContext, addOfflineAction]);

  // Force sync
  const forcSync = useCallback(() => {
    if (syncStatus.isOnline && !syncStatus.isSyncing) {
      syncOfflineActions();
    } else if (!syncStatus.isOnline) {
      toast.warning('Keine Internetverbindung verfügbar');
    } else {
      toast.info('Synchronisierung läuft bereits...');
    }
  }, [syncStatus.isOnline, syncStatus.isSyncing, syncOfflineActions]);

  // Clear all offline actions (for testing/reset)
  const clearOfflineActions = useCallback(() => {
    setOfflineActions([]);
    localStorage.removeItem('offlineActions');
    toast.success('Offline-Aktionen gelöscht');
  }, []);

  return {
    syncStatus,
    offlineActions,
    offlineAddVisit,
    offlineUpdateVisitStatus,
    offlineAddDoor,
    forcSync,
    clearOfflineActions,
    addOfflineAction
  };
};
